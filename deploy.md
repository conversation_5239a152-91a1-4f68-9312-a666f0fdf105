# SGX TEE 应用安全部署方案 - 专用构建机器版

## 概述

本文档描述了基于专用构建机器的SGX TEE应用安全部署方案。该方案使用隔离的专用机器进行编译和签名，建立可信的MR_SIGNER认证机制，安全管理敏感信息，并提供独立的客户端验证云端SGX应用的可信性。

## 当前架构分析

### 现有组件
- **KMS (Key Management Service)**: 密钥管理服务，基于SGX attestation提供密钥分发
- **Wallet Signer**: 签名服务，使用Shamir秘密共享机制
- **Wallet Service**: 钱包API服务，处理用户管理和业务逻辑
- **部署环境**: Azure Kubernetes Service + SGX节点

### 现有安全机制
- Gramine LibOS + DCAP远程attestation
- mTLS通信基于SGX证书验证
- Docker多阶段构建包含SGX签名
- 环境变量配置信任关系(MR_ENCLAVE/MR_SIGNER)

### 新需求和挑战
1. **专用构建环境**: 不使用GitHub Actions，改用专用机器进行编译和签名
2. **MR_SIGNER认证**: 需要建立可信的MR_SIGNER白名单和认证机制
3. **敏感信息管理**: 私钥等敏感环境变量需要安全存储和分发
4. **客户端验证**: 需要独立的客户端来验证云端SGX应用的可信性

## 方案1：专用构建机器 + 客户端验证方案

### 核心设计原则
- **隔离构建**: 使用专用的、物理隔离的构建环境
- **可信认证**: 建立基于密码学的MR_SIGNER信任链
- **零信任验证**: 客户端独立验证所有SGX应用的可信性
- **敏感信息保护**: 采用硬件安全模块(HSM)保护关键密钥

## 阶段1：专用构建机器安全配置

### 1.1 专用构建环境设计

**目标**: 建立物理隔离、高度安全的SGX应用构建环境

**硬件要求**:
```yaml
# 专用构建机器规格
hardware_requirements:
  cpu: Intel Xeon with SGX support (Ice Lake or newer)
  memory: 64GB ECC RAM
  storage: 2TB NVMe SSD (encrypted)
  network: 隔离网络，仅允许必要的出站连接
  security: TPM 2.0, Secure Boot enabled
  location: 物理安全的数据中心
```

**软件环境配置**:
```bash
#!/bin/bash
# scripts/setup-build-machine.sh

set -euo pipefail

# 1. 基础系统加固
echo "Setting up secure build environment..."

# 禁用不必要的服务
systemctl disable bluetooth
systemctl disable cups
systemctl disable avahi-daemon

# 配置防火墙
ufw --force enable
ufw default deny incoming
ufw default deny outgoing
ufw allow out 443  # HTTPS
ufw allow out 53   # DNS

# 2. 安装SGX驱动和SDK
echo "Installing SGX components..."
wget https://download.01.org/intel-sgx/sgx-linux/2.22/distro/ubuntu20.04-server/sgx_linux_x64_driver_2.11.54c9c4c.bin
chmod +x sgx_linux_x64_driver_2.11.54c9c4c.bin
sudo ./sgx_linux_x64_driver_2.11.54c9c4c.bin

# 3. 安装Gramine
echo "Installing Gramine..."
curl -fsSLo /usr/share/keyrings/gramine-keyring.gpg https://packages.gramineproject.io/gramine-keyring.gpg
echo 'deb [arch=amd64 signed-by=/usr/share/keyrings/gramine-keyring.gpg] https://packages.gramineproject.io/ 1.7 main' | sudo tee /etc/apt/sources.list.d/gramine.list
apt update && apt install -y gramine

# 4. 配置构建用户
useradd -m -s /bin/bash sgxbuilder
usermod -aG sgx sgxbuilder

# 5. 设置构建目录权限
mkdir -p /secure-build/{source,output,keys,logs}
chown -R sgxbuilder:sgxbuilder /secure-build
chmod 700 /secure-build
```

**构建环境隔离**:
```bash
#!/bin/bash
# scripts/isolated-build.sh

# 创建隔离的构建环境
sudo -u sgxbuilder bash << 'EOF'
cd /secure-build

# 1. 清理之前的构建
rm -rf source/* output/* logs/*

# 2. 获取源代码（通过安全渠道）
git clone --depth 1 --branch main https://github.com/your-org/clique-wallet.git source/
cd source

# 3. 验证源代码完整性
git verify-commit HEAD
gpg --verify source-integrity.sig

# 4. 设置可重现构建环境
export SOURCE_DATE_EPOCH=$(git log -1 --format=%ct)
export RUSTFLAGS="-C target-cpu=generic -C link-arg=-Wl,--build-id=none"
export CARGO_HOME=/secure-build/.cargo
export RUSTUP_HOME=/secure-build/.rustup

# 5. 执行构建
make clean
make SGX=1 REPRODUCIBLE=1

# 6. 记录构建信息
echo "Build completed at $(date)" >> /secure-build/logs/build.log
sha256sum target/release/* >> /secure-build/logs/checksums.txt
EOF
```

### 1.2 SGX签名密钥管理

**目标**: 安全管理SGX enclave签名密钥，确保密钥的机密性和完整性

**HSM集成方案**:
```bash
#!/bin/bash
# scripts/setup-hsm.sh

# 1. 配置硬件安全模块
echo "Configuring HSM for SGX signing keys..."

# 使用Azure Dedicated HSM或本地HSM
# 生成SGX签名密钥对
pkcs11-tool --module /usr/lib/libCryptoki2_64.so \
  --login --pin $HSM_PIN \
  --keypairgen --key-type rsa:3072 \
  --label "sgx-signing-key-$(date +%Y%m%d)"

# 2. 配置Gramine使用HSM
cat > /secure-build/keys/hsm-config.toml << EOF
[hsm]
module_path = "/usr/lib/libCryptoki2_64.so"
slot_id = 0
pin_file = "/secure-build/keys/hsm-pin.enc"
key_label = "sgx-signing-key-$(date +%Y%m%d)"
EOF
```

**签名流程自动化**:
```bash
#!/bin/bash
# scripts/sign-enclave.sh

set -euo pipefail

SERVICE_NAME=$1
BUILD_OUTPUT_DIR="/secure-build/output"

echo "Signing SGX enclave for service: $SERVICE_NAME"

# 1. 验证构建产物
if [[ ! -f "$BUILD_OUTPUT_DIR/$SERVICE_NAME.manifest" ]]; then
    echo "Error: Manifest file not found"
    exit 1
fi

# 2. 使用HSM进行签名
gramine-sgx-sign \
    --manifest "$BUILD_OUTPUT_DIR/$SERVICE_NAME.manifest" \
    --output "$BUILD_OUTPUT_DIR/$SERVICE_NAME.manifest.sgx" \
    --key-config /secure-build/keys/hsm-config.toml

# 3. 验证签名
gramine-sgx-sigstruct-view "$BUILD_OUTPUT_DIR/$SERVICE_NAME.sig" > "$BUILD_OUTPUT_DIR/$SERVICE_NAME.sigstruct.txt"

# 4. 提取MR值
MR_ENCLAVE=$(grep "mr_enclave" "$BUILD_OUTPUT_DIR/$SERVICE_NAME.sigstruct.txt" | awk '{print $2}')
MR_SIGNER=$(grep "mr_signer" "$BUILD_OUTPUT_DIR/$SERVICE_NAME.sigstruct.txt" | awk '{print $2}')

# 5. 生成签名证明
cat > "$BUILD_OUTPUT_DIR/$SERVICE_NAME.attestation.json" << EOF
{
    "service_name": "$SERVICE_NAME",
    "build_timestamp": "$(date -Iseconds)",
    "source_commit": "$(git rev-parse HEAD)",
    "mr_enclave": "$MR_ENCLAVE",
    "mr_signer": "$MR_SIGNER",
    "signer_identity": "$(whoami)@$(hostname)",
    "build_machine": "$(hostname)",
    "signature": "$(echo -n "$SERVICE_NAME$MR_ENCLAVE$MR_SIGNER" | openssl dgst -sha256 -sign /secure-build/keys/build-signing.key | base64 -w 0)"
}
EOF

echo "✅ Enclave signed successfully"
echo "MR_ENCLAVE: $MR_ENCLAVE"
echo "MR_SIGNER: $MR_SIGNER"
```

### 1.3 构建产物验证和分发

**目标**: 确保构建产物的完整性，安全分发到部署环境

**构建产物打包**:
```bash
#!/bin/bash
# scripts/package-build-artifacts.sh

SERVICE_NAME=$1
VERSION=$2
BUILD_OUTPUT_DIR="/secure-build/output"
PACKAGE_DIR="/secure-build/packages"

echo "Packaging build artifacts for $SERVICE_NAME:$VERSION"

# 1. 创建包目录
mkdir -p "$PACKAGE_DIR/$SERVICE_NAME-$VERSION"
cd "$PACKAGE_DIR/$SERVICE_NAME-$VERSION"

# 2. 复制构建产物
cp "$BUILD_OUTPUT_DIR/$SERVICE_NAME" ./
cp "$BUILD_OUTPUT_DIR/$SERVICE_NAME.manifest" ./
cp "$BUILD_OUTPUT_DIR/$SERVICE_NAME.manifest.sgx" ./
cp "$BUILD_OUTPUT_DIR/$SERVICE_NAME.sig" ./
cp "$BUILD_OUTPUT_DIR/$SERVICE_NAME.attestation.json" ./

# 3. 生成SBOM (Software Bill of Materials)
syft . -o spdx-json > sbom.spdx.json

# 4. 创建包清单
cat > manifest.json << EOF
{
    "package_name": "$SERVICE_NAME",
    "version": "$VERSION",
    "build_date": "$(date -Iseconds)",
    "files": [
        {
            "name": "$SERVICE_NAME",
            "type": "executable",
            "sha256": "$(sha256sum $SERVICE_NAME | cut -d' ' -f1)"
        },
        {
            "name": "$SERVICE_NAME.manifest.sgx",
            "type": "sgx_manifest",
            "sha256": "$(sha256sum $SERVICE_NAME.manifest.sgx | cut -d' ' -f1)"
        },
        {
            "name": "$SERVICE_NAME.sig",
            "type": "sgx_signature",
            "sha256": "$(sha256sum $SERVICE_NAME.sig | cut -d' ' -f1)"
        }
    ]
}
EOF

# 5. 数字签名整个包
tar czf "../$SERVICE_NAME-$VERSION.tar.gz" .
cd ..
openssl dgst -sha256 -sign /secure-build/keys/package-signing.key \
    -out "$SERVICE_NAME-$VERSION.tar.gz.sig" \
    "$SERVICE_NAME-$VERSION.tar.gz"

echo "✅ Package created: $SERVICE_NAME-$VERSION.tar.gz"
```

**安全分发机制**:
```bash
#!/bin/bash
# scripts/secure-distribute.sh

PACKAGE_FILE=$1
DESTINATION_ENV=$2  # dev, staging, prod

echo "Distributing package $PACKAGE_FILE to $DESTINATION_ENV"

# 1. 验证包完整性
if ! openssl dgst -sha256 -verify /secure-build/keys/package-signing.pub \
    -signature "$PACKAGE_FILE.sig" "$PACKAGE_FILE"; then
    echo "❌ Package signature verification failed"
    exit 1
fi

# 2. 上传到安全存储
case $DESTINATION_ENV in
    "dev")
        STORAGE_URL="https://secure-artifacts-dev.vault.azure.net"
        ;;
    "staging")
        STORAGE_URL="https://secure-artifacts-staging.vault.azure.net"
        ;;
    "prod")
        STORAGE_URL="https://secure-artifacts-prod.vault.azure.net"
        ;;
esac

# 3. 使用Azure CLI上传（需要预先配置身份验证）
az storage blob upload \
    --account-name secureartifacts \
    --container-name sgx-packages \
    --name "$PACKAGE_FILE" \
    --file "$PACKAGE_FILE" \
    --auth-mode login

# 4. 记录分发日志
echo "$(date -Iseconds): Distributed $PACKAGE_FILE to $DESTINATION_ENV" >> /secure-build/logs/distribution.log

echo "✅ Package distributed successfully"
```

## 阶段2：MR_SIGNER认证机制

### 2.1 可信MR_SIGNER白名单管理

**目标**: 建立基于密码学的MR_SIGNER信任链，确保只有授权的签名者可以签名SGX应用

**MR_SIGNER信任模型**:
```yaml
# config/mr-signer-trust-model.yaml
trust_model:
  # 根信任锚点 - 组织的主签名密钥
  root_authority:
    name: "Clique Root CA"
    public_key_path: "/secure-keys/root-ca.pub"
    key_id: "clique-root-2024"

  # 授权的签名者
  authorized_signers:
    - name: "Production Build Machine"
      mr_signer: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
      public_key_path: "/secure-keys/prod-signer.pub"
      key_id: "prod-build-2024"
      environment: "production"
      valid_from: "2024-01-01T00:00:00Z"
      valid_until: "2024-12-31T23:59:59Z"

    - name: "Staging Build Machine"
      mr_signer: "b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef1234567"
      public_key_path: "/secure-keys/staging-signer.pub"
      key_id: "staging-build-2024"
      environment: "staging"
      valid_from: "2024-01-01T00:00:00Z"
      valid_until: "2024-12-31T23:59:59Z"

  # 信任策略
  trust_policies:
    production:
      required_signers: ["prod-build-2024"]
      min_signature_count: 1
      allow_debug: false

    staging:
      required_signers: ["staging-build-2024", "prod-build-2024"]
      min_signature_count: 1
      allow_debug: true
```

**MR_SIGNER证书生成**:
```bash
#!/bin/bash
# scripts/generate-mr-signer-cert.sh

set -euo pipefail

SIGNER_NAME=$1
ENVIRONMENT=$2
VALIDITY_DAYS=${3:-365}

echo "Generating MR_SIGNER certificate for $SIGNER_NAME ($ENVIRONMENT)"

# 1. 生成签名密钥对（如果不存在）
if [[ ! -f "/secure-keys/$SIGNER_NAME.key" ]]; then
    openssl genrsa -out "/secure-keys/$SIGNER_NAME.key" 3072
    chmod 600 "/secure-keys/$SIGNER_NAME.key"
fi

# 2. 生成证书签名请求
openssl req -new \
    -key "/secure-keys/$SIGNER_NAME.key" \
    -out "/secure-keys/$SIGNER_NAME.csr" \
    -subj "/C=US/ST=CA/L=San Francisco/O=Clique/OU=SGX Build/CN=$SIGNER_NAME"

# 3. 使用根CA签名证书
openssl x509 -req \
    -in "/secure-keys/$SIGNER_NAME.csr" \
    -CA "/secure-keys/root-ca.crt" \
    -CAkey "/secure-keys/root-ca.key" \
    -CAcreateserial \
    -out "/secure-keys/$SIGNER_NAME.crt" \
    -days $VALIDITY_DAYS \
    -extensions v3_req \
    -extfile <(cat << EOF
[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
subjectAltName = @alt_names
[alt_names]
DNS.1 = $SIGNER_NAME
DNS.2 = $SIGNER_NAME.$ENVIRONMENT.clique.internal
EOF
)

# 4. 计算MR_SIGNER值
# 使用Gramine工具从公钥计算MR_SIGNER
gramine-sgx-get-token \
    --output-file "/tmp/temp.token" \
    --sig "/secure-keys/$SIGNER_NAME.crt"

MR_SIGNER=$(gramine-sgx-sigstruct-view "/tmp/temp.token" | grep "mr_signer" | awk '{print $2}')
rm -f "/tmp/temp.token"

# 5. 生成签名者配置
cat > "/secure-keys/$SIGNER_NAME.config.json" << EOF
{
    "signer_name": "$SIGNER_NAME",
    "environment": "$ENVIRONMENT",
    "mr_signer": "$MR_SIGNER",
    "certificate_path": "/secure-keys/$SIGNER_NAME.crt",
    "private_key_path": "/secure-keys/$SIGNER_NAME.key",
    "valid_from": "$(date -Iseconds)",
    "valid_until": "$(date -d "+$VALIDITY_DAYS days" -Iseconds)",
    "key_fingerprint": "$(openssl x509 -in /secure-keys/$SIGNER_NAME.crt -fingerprint -sha256 -noout | cut -d'=' -f2)"
}
EOF

echo "✅ MR_SIGNER certificate generated"
echo "MR_SIGNER: $MR_SIGNER"
echo "Certificate: /secure-keys/$SIGNER_NAME.crt"
```

### 2.2 动态信任管理服务

**目标**: 提供动态的MR_SIGNER信任管理，支持信任关系的实时更新和撤销

**信任管理服务实现**:
```rust
// services/trust-manager/src/main.rs
use axum::{extract::{Path, Query, State}, http::StatusCode, response::Json, routing::{get, post, delete}, Router};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustedSigner {
    pub name: String,
    pub mr_signer: String,
    pub public_key: String,
    pub key_id: String,
    pub environment: String,
    pub valid_from: DateTime<Utc>,
    pub valid_until: DateTime<Utc>,
    pub is_active: bool,
    pub certificate_fingerprint: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustPolicy {
    pub environment: String,
    pub required_signers: Vec<String>,
    pub min_signature_count: u32,
    pub allow_debug: bool,
    pub max_enclave_age_hours: u32,
}

#[derive(Debug)]
pub struct TrustManager {
    trusted_signers: Arc<RwLock<HashMap<String, TrustedSigner>>>,
    trust_policies: Arc<RwLock<HashMap<String, TrustPolicy>>>,
    revocation_list: Arc<RwLock<Vec<String>>>, // 撤销的MR_SIGNER列表
}

#[derive(Debug, Serialize, Deserialize)]
pub struct VerificationRequest {
    pub mr_signer: String,
    pub mr_enclave: String,
    pub environment: String,
    pub attestation_data: String,
}

#[derive(Debug, Serialize)]
pub struct VerificationResult {
    pub is_trusted: bool,
    pub signer_info: Option<TrustedSigner>,
    pub policy_compliance: bool,
    pub verification_details: Vec<String>,
    pub timestamp: DateTime<Utc>,
}

impl TrustManager {
    pub async fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let mut trusted_signers = HashMap::new();
        let mut trust_policies = HashMap::new();

        // 从配置文件加载初始信任配置
        let config = Self::load_trust_config().await?;

        for signer in config.authorized_signers {
            trusted_signers.insert(signer.key_id.clone(), signer);
        }

        for (env, policy) in config.trust_policies {
            trust_policies.insert(env, policy);
        }

        Ok(Self {
            trusted_signers: Arc::new(RwLock::new(trusted_signers)),
            trust_policies: Arc::new(RwLock::new(trust_policies)),
            revocation_list: Arc::new(RwLock::new(Vec::new())),
        })
    }

    pub async fn verify_signer(&self, request: &VerificationRequest) -> VerificationResult {
        let mut verification_details = Vec::new();
        let mut is_trusted = false;
        let mut signer_info = None;
        let mut policy_compliance = false;

        // 1. 检查撤销列表
        let revocation_list = self.revocation_list.read().await;
        if revocation_list.contains(&request.mr_signer) {
            verification_details.push("MR_SIGNER is in revocation list".to_string());
            return VerificationResult {
                is_trusted: false,
                signer_info: None,
                policy_compliance: false,
                verification_details,
                timestamp: Utc::now(),
            };
        }

        // 2. 查找匹配的签名者
        let trusted_signers = self.trusted_signers.read().await;
        for (key_id, signer) in trusted_signers.iter() {
            if signer.mr_signer == request.mr_signer {
                // 检查有效期
                let now = Utc::now();
                if now >= signer.valid_from && now <= signer.valid_until && signer.is_active {
                    is_trusted = true;
                    signer_info = Some(signer.clone());
                    verification_details.push(format!("Found trusted signer: {}", signer.name));
                    break;
                } else {
                    verification_details.push(format!("Signer {} is expired or inactive", signer.name));
                }
            }
        }

        // 3. 检查策略合规性
        if let Some(ref signer) = signer_info {
            let trust_policies = self.trust_policies.read().await;
            if let Some(policy) = trust_policies.get(&request.environment) {
                if policy.required_signers.contains(&signer.key_id) {
                    policy_compliance = true;
                    verification_details.push("Policy compliance verified".to_string());
                } else {
                    verification_details.push("Signer not authorized for this environment".to_string());
                }
            } else {
                verification_details.push("No policy found for environment".to_string());
            }
        }

        VerificationResult {
            is_trusted: is_trusted && policy_compliance,
            signer_info,
            policy_compliance,
            verification_details,
            timestamp: Utc::now(),
        }
    }

    pub async fn add_trusted_signer(&self, signer: TrustedSigner) -> Result<(), String> {
        let mut trusted_signers = self.trusted_signers.write().await;

        // 验证签名者证书
        if !self.verify_signer_certificate(&signer).await {
            return Err("Invalid signer certificate".to_string());
        }

        trusted_signers.insert(signer.key_id.clone(), signer);
        Ok(())
    }

    pub async fn revoke_signer(&self, mr_signer: String) -> Result<(), String> {
        let mut revocation_list = self.revocation_list.write().await;
        if !revocation_list.contains(&mr_signer) {
            revocation_list.push(mr_signer);
        }
        Ok(())
    }

    async fn verify_signer_certificate(&self, signer: &TrustedSigner) -> bool {
        // 实现证书验证逻辑
        // 1. 验证证书链
        // 2. 检查证书有效期
        // 3. 验证MR_SIGNER计算
        true // 简化实现
    }

    async fn load_trust_config() -> Result<TrustConfig, Box<dyn std::error::Error>> {
        // 从配置文件加载信任配置
        let config_str = tokio::fs::read_to_string("config/mr-signer-trust-model.yaml").await?;
        let config: TrustConfig = serde_yaml::from_str(&config_str)?;
        Ok(config)
    }
}

#[derive(Debug, Deserialize)]
struct TrustConfig {
    authorized_signers: Vec<TrustedSigner>,
    trust_policies: HashMap<String, TrustPolicy>,
}

// HTTP API端点
async fn verify_signer_endpoint(
    State(trust_manager): State<Arc<TrustManager>>,
    Json(request): Json<VerificationRequest>,
) -> Result<Json<VerificationResult>, StatusCode> {
    let result = trust_manager.verify_signer(&request).await;
    Ok(Json(result))
}

async fn add_signer_endpoint(
    State(trust_manager): State<Arc<TrustManager>>,
    Json(signer): Json<TrustedSigner>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match trust_manager.add_trusted_signer(signer).await {
        Ok(_) => Ok(Json(serde_json::json!({"status": "success"}))),
        Err(e) => {
            tracing::error!("Failed to add signer: {}", e);
            Err(StatusCode::BAD_REQUEST)
        }
    }
}

async fn revoke_signer_endpoint(
    State(trust_manager): State<Arc<TrustManager>>,
    Path(mr_signer): Path<String>,
) -> Result<Json<serde_json::Value>, StatusCode> {
    match trust_manager.revoke_signer(mr_signer).await {
        Ok(_) => Ok(Json(serde_json::json!({"status": "revoked"}))),
        Err(e) => {
            tracing::error!("Failed to revoke signer: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

async fn list_signers_endpoint(
    State(trust_manager): State<Arc<TrustManager>>,
) -> Result<Json<Vec<TrustedSigner>>, StatusCode> {
    let trusted_signers = trust_manager.trusted_signers.read().await;
    let signers: Vec<TrustedSigner> = trusted_signers.values().cloned().collect();
    Ok(Json(signers))
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::init();

    let trust_manager = Arc::new(TrustManager::new().await?);

    let app = Router::new()
        .route("/verify", post(verify_signer_endpoint))
        .route("/signers", post(add_signer_endpoint))
        .route("/signers", get(list_signers_endpoint))
        .route("/signers/:mr_signer/revoke", delete(revoke_signer_endpoint))
        .with_state(trust_manager);

    let listener = tokio::net::TcpListener::bind("0.0.0.0:8080").await?;
    tracing::info!("Trust manager service starting on port 8080");

    axum::serve(listener, app).await?;

    Ok(())
}
```

## 阶段3：敏感信息管理方案

### 3.1 分层密钥管理架构

**目标**: 建立分层的密钥管理体系，安全存储和分发私钥等敏感信息

**密钥分层设计**:
```yaml
# config/key-hierarchy.yaml
key_hierarchy:
  # 第1层：根密钥（HSM保护）
  root_keys:
    - name: "master-encryption-key"
      type: "AES-256"
      storage: "hsm"
      purpose: "encrypt_tier2_keys"

    - name: "master-signing-key"
      type: "RSA-4096"
      storage: "hsm"
      purpose: "sign_certificates"

  # 第2层：服务密钥（加密存储）
  service_keys:
    - name: "kms-service-key"
      type: "ECDSA-P256"
      encrypted_with: "master-encryption-key"
      purpose: "kms_operations"

    - name: "wallet-signer-key"
      type: "ECDSA-P256"
      encrypted_with: "master-encryption-key"
      purpose: "wallet_signing"

    - name: "wallet-service-key"
      type: "ECDSA-P256"
      encrypted_with: "master-encryption-key"
      purpose: "service_authentication"

  # 第3层：会话密钥（运行时生成）
  session_keys:
    - name: "tls-session-keys"
      type: "ephemeral"
      lifetime: "24h"
      purpose: "secure_communication"
```

**HSM集成配置**:
```bash
#!/bin/bash
# scripts/setup-key-management.sh

set -euo pipefail

echo "Setting up hierarchical key management system..."

# 1. 初始化HSM
echo "Initializing HSM..."
pkcs11-tool --module /usr/lib/libCryptoki2_64.so --init-token --label "CLIQUE-SGX" --so-pin $HSM_SO_PIN
pkcs11-tool --module /usr/lib/libCryptoki2_64.so --init-pin --login --so-pin $HSM_SO_PIN --pin $HSM_USER_PIN

# 2. 生成根密钥
echo "Generating root keys in HSM..."

# 主加密密钥
pkcs11-tool --module /usr/lib/libCryptoki2_64.so \
  --login --pin $HSM_USER_PIN \
  --keygen --key-type AES:256 \
  --label "master-encryption-key" \
  --id 01

# 主签名密钥
pkcs11-tool --module /usr/lib/libCryptoki2_64.so \
  --login --pin $HSM_USER_PIN \
  --keypairgen --key-type rsa:4096 \
  --label "master-signing-key" \
  --id 02

# 3. 配置密钥访问策略
cat > /secure-keys/hsm-policy.json << EOF
{
  "hsm_config": {
    "module_path": "/usr/lib/libCryptoki2_64.so",
    "slot_id": 0,
    "pin_source": "environment:HSM_USER_PIN"
  },
  "key_policies": {
    "master-encryption-key": {
      "id": "01",
      "operations": ["encrypt", "decrypt"],
      "authorized_services": ["key-manager"],
      "max_operations_per_hour": 1000
    },
    "master-signing-key": {
      "id": "02",
      "operations": ["sign", "verify"],
      "authorized_services": ["certificate-authority"],
      "max_operations_per_hour": 100
    }
  }
}
EOF

echo "✅ HSM key management setup completed"
```

### 3.2 安全密钥分发服务

**目标**: 实现安全的密钥分发机制，确保敏感信息只能被授权的SGX应用获取

**密钥分发服务实现**:
```rust
// services/key-distributor/src/main.rs
use axum::{extract::{Path, State}, http::StatusCode, response::Json, routing::{get, post}, Router};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::RwLock;
use aes_gcm::{Aes256Gcm, Key, Nonce, aead::{Aead, NewAead}};
use rand::RngCore;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyRequest {
    pub service_name: String,
    pub key_name: String,
    pub attestation: String,
    pub mr_enclave: String,
    pub mr_signer: String,
}

#[derive(Debug, Serialize)]
pub struct KeyResponse {
    pub encrypted_key: String,
    pub nonce: String,
    pub key_metadata: KeyMetadata,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeyMetadata {
    pub key_name: String,
    pub key_type: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
    pub usage_count: u64,
    pub max_usage: Option<u64>,
}

#[derive(Debug, Clone)]
pub struct SecureKey {
    pub name: String,
    pub key_data: Vec<u8>,
    pub metadata: KeyMetadata,
    pub authorized_enclaves: Vec<String>,
    pub authorized_signers: Vec<String>,
}

pub struct KeyDistributor {
    keys: Arc<RwLock<HashMap<String, SecureKey>>>,
    master_key: Arc<Aes256Gcm>,
    trust_manager_url: String,
}

impl KeyDistributor {
    pub async fn new(trust_manager_url: String) -> Result<Self, Box<dyn std::error::Error>> {
        // 从HSM获取主加密密钥
        let master_key_bytes = Self::get_master_key_from_hsm().await?;
        let master_key = Aes256Gcm::new(Key::from_slice(&master_key_bytes));

        let mut keys = HashMap::new();

        // 加载预配置的密钥
        Self::load_service_keys(&mut keys).await?;

        Ok(Self {
            keys: Arc::new(RwLock::new(keys)),
            master_key: Arc::new(master_key),
            trust_manager_url,
        })
    }

    pub async fn distribute_key(&self, request: KeyRequest) -> Result<KeyResponse, String> {
        // 1. 验证attestation和MR值
        if !self.verify_attestation(&request).await? {
            return Err("Attestation verification failed".to_string());
        }

        // 2. 检查授权
        let keys = self.keys.read().await;
        let key = keys.get(&request.key_name)
            .ok_or("Key not found")?;

        if !self.is_authorized(&request, key) {
            return Err("Not authorized for this key".to_string());
        }

        // 3. 加密密钥数据
        let mut nonce_bytes = [0u8; 12];
        rand::thread_rng().fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        let encrypted_key = self.master_key
            .encrypt(nonce, key.key_data.as_ref())
            .map_err(|_| "Encryption failed")?;

        // 4. 更新使用计数
        drop(keys);
        let mut keys_mut = self.keys.write().await;
        if let Some(key_mut) = keys_mut.get_mut(&request.key_name) {
            key_mut.metadata.usage_count += 1;
        }

        Ok(KeyResponse {
            encrypted_key: base64::encode(&encrypted_key),
            nonce: base64::encode(&nonce_bytes),
            key_metadata: key.metadata.clone(),
        })
    }

    async fn verify_attestation(&self, request: &KeyRequest) -> Result<bool, String> {
        // 调用信任管理服务验证attestation
        let client = reqwest::Client::new();
        let verification_request = serde_json::json!({
            "mr_signer": request.mr_signer,
            "mr_enclave": request.mr_enclave,
            "environment": "production",
            "attestation_data": request.attestation
        });

        let response = client
            .post(&format!("{}/verify", self.trust_manager_url))
            .json(&verification_request)
            .send()
            .await
            .map_err(|e| format!("Trust manager request failed: {}", e))?;

        if !response.status().is_success() {
            return Ok(false);
        }

        let result: serde_json::Value = response.json().await
            .map_err(|e| format!("Failed to parse response: {}", e))?;

        Ok(result["is_trusted"].as_bool().unwrap_or(false))
    }

    fn is_authorized(&self, request: &KeyRequest, key: &SecureKey) -> bool {
        // 检查enclave和signer授权
        (key.authorized_enclaves.is_empty() || key.authorized_enclaves.contains(&request.mr_enclave)) &&
        (key.authorized_signers.is_empty() || key.authorized_signers.contains(&request.mr_signer))
    }

    async fn get_master_key_from_hsm() -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // 使用PKCS#11接口从HSM获取主密钥
        // 这里简化实现，实际应该使用PKCS#11库

        use std::process::Command;

        let output = Command::new("pkcs11-tool")
            .args(&[
                "--module", "/usr/lib/libCryptoki2_64.so",
                "--login", "--pin", &std::env::var("HSM_USER_PIN")?,
                "--read-object", "--id", "01",
                "--type", "secrkey"
            ])
            .output()?;

        if !output.status.success() {
            return Err("Failed to retrieve master key from HSM".into());
        }

        Ok(output.stdout)
    }

    async fn load_service_keys(keys: &mut HashMap<String, SecureKey>) -> Result<(), Box<dyn std::error::Error>> {
        // 从加密存储加载服务密钥
        let key_configs = [
            ("kms-master-key", "services/kms/master.key.enc"),
            ("wallet-signer-key", "services/wallet-signer/signing.key.enc"),
            ("wallet-service-key", "services/wallet-service/service.key.enc"),
        ];

        for (key_name, key_path) in &key_configs {
            if let Ok(encrypted_data) = tokio::fs::read(key_path).await {
                // 解密密钥数据（使用主密钥）
                let key_data = Self::decrypt_service_key(&encrypted_data).await?;

                let secure_key = SecureKey {
                    name: key_name.to_string(),
                    key_data,
                    metadata: KeyMetadata {
                        key_name: key_name.to_string(),
                        key_type: "ECDSA-P256".to_string(),
                        created_at: chrono::Utc::now(),
                        expires_at: None,
                        usage_count: 0,
                        max_usage: Some(10000),
                    },
                    authorized_enclaves: vec![], // 允许所有授权的enclave
                    authorized_signers: vec![], // 允许所有授权的signer
                };

                keys.insert(key_name.to_string(), secure_key);
            }
        }

        Ok(())
    }

    async fn decrypt_service_key(encrypted_data: &[u8]) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
        // 使用HSM中的主密钥解密服务密钥
        // 简化实现
        Ok(encrypted_data.to_vec())
    }
}

// HTTP API端点
async fn distribute_key_endpoint(
    State(distributor): State<Arc<KeyDistributor>>,
    Json(request): Json<KeyRequest>,
) -> Result<Json<KeyResponse>, StatusCode> {
    match distributor.distribute_key(request).await {
        Ok(response) => Ok(Json(response)),
        Err(e) => {
            tracing::error!("Key distribution failed: {}", e);
            Err(StatusCode::FORBIDDEN)
        }
    }
}

async fn health_check() -> &'static str {
    "OK"
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::init();

    let trust_manager_url = std::env::var("TRUST_MANAGER_URL")
        .unwrap_or_else(|_| "http://trust-manager:8080".to_string());

    let distributor = Arc::new(KeyDistributor::new(trust_manager_url).await?);

    let app = Router::new()
        .route("/keys/:key_name", post(distribute_key_endpoint))
        .route("/health", get(health_check))
        .with_state(distributor);

    let listener = tokio::net::TcpListener::bind("0.0.0.0:8080").await?;
    tracing::info!("Key distributor service starting on port 8080");

    axum::serve(listener, app).await?;

    Ok(())
}
```

## 阶段4：SGX应用验证客户端

### 4.1 客户端验证架构设计

**目标**: 实现独立的客户端来验证云端SGX应用的可信性，确保客户端能够验证所有SGX服务的真实性

**客户端验证流程**:
```mermaid
graph TD
    A[客户端启动] --> B[加载信任配置]
    B --> C[连接SGX服务]
    C --> D[请求Attestation]
    D --> E[验证Attestation]
    E --> F{验证通过?}
    F -->|是| G[建立安全通信]
    F -->|否| H[拒绝连接]
    G --> I[执行业务操作]
    I --> J[持续验证]
```

**客户端验证库实现**:
```rust
// client-sdk/src/sgx_verifier.rs
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use reqwest::Client;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustConfig {
    pub trusted_signers: HashMap<String, TrustedSigner>,
    pub service_endpoints: HashMap<String, ServiceConfig>,
    pub verification_policies: VerificationPolicies,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustedSigner {
    pub name: String,
    pub mr_signer: String,
    pub public_key: String,
    pub valid_until: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServiceConfig {
    pub name: String,
    pub endpoint: String,
    pub expected_mr_enclave: String,
    pub required_mr_signer: String,
    pub attestation_endpoint: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VerificationPolicies {
    pub max_attestation_age_seconds: u64,
    pub require_debug_disabled: bool,
    pub minimum_tcb_level: u32,
    pub allowed_advisories: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AttestationResponse {
    pub attestation: String,
    pub timestamp: DateTime<Utc>,
    pub service_info: ServiceInfo,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ServiceInfo {
    pub service_name: String,
    pub version: String,
    pub mr_enclave: String,
    pub mr_signer: String,
    pub tcb_level: u32,
    pub debug_enabled: bool,
}

#[derive(Debug)]
pub struct SGXVerifier {
    trust_config: TrustConfig,
    http_client: Client,
    verified_services: HashMap<String, VerifiedService>,
}

#[derive(Debug, Clone)]
struct VerifiedService {
    service_name: String,
    last_verification: DateTime<Utc>,
    attestation_valid_until: DateTime<Utc>,
    tls_config: rustls::ClientConfig,
}

impl SGXVerifier {
    pub fn new(trust_config: TrustConfig) -> Result<Self, Box<dyn std::error::Error>> {
        let http_client = Client::builder()
            .timeout(std::time::Duration::from_secs(30))
            .build()?;

        Ok(Self {
            trust_config,
            http_client,
            verified_services: HashMap::new(),
        })
    }

    /// 验证SGX服务并建立安全连接
    pub async fn verify_and_connect(&mut self, service_name: &str) -> Result<SecureConnection, VerificationError> {
        // 1. 获取服务配置
        let service_config = self.trust_config.service_endpoints
            .get(service_name)
            .ok_or(VerificationError::ServiceNotConfigured)?;

        // 2. 请求attestation
        let attestation = self.request_attestation(service_config).await?;

        // 3. 验证attestation
        let verification_result = self.verify_attestation(&attestation, service_config).await?;

        if !verification_result.is_valid {
            return Err(VerificationError::AttestationInvalid(verification_result.error_details));
        }

        // 4. 建立安全连接
        let secure_connection = self.establish_secure_connection(service_config, &attestation).await?;

        // 5. 缓存验证结果
        self.cache_verification_result(service_name, &attestation);

        Ok(secure_connection)
    }

    async fn request_attestation(&self, service_config: &ServiceConfig) -> Result<AttestationResponse, VerificationError> {
        let response = self.http_client
            .get(&service_config.attestation_endpoint)
            .send()
            .await
            .map_err(|e| VerificationError::NetworkError(e.to_string()))?;

        if !response.status().is_success() {
            return Err(VerificationError::AttestationRequestFailed(response.status().as_u16()));
        }

        let attestation: AttestationResponse = response.json().await
            .map_err(|e| VerificationError::InvalidResponse(e.to_string()))?;

        Ok(attestation)
    }

    async fn verify_attestation(
        &self,
        attestation: &AttestationResponse,
        service_config: &ServiceConfig
    ) -> Result<VerificationResult, VerificationError> {
        let mut checks = Vec::new();
        let mut is_valid = true;

        // 1. 验证attestation时效性
        let age = Utc::now().signed_duration_since(attestation.timestamp);
        if age.num_seconds() > self.trust_config.verification_policies.max_attestation_age_seconds as i64 {
            checks.push("Attestation is too old".to_string());
            is_valid = false;
        }

        // 2. 验证MR_ENCLAVE
        if attestation.service_info.mr_enclave != service_config.expected_mr_enclave {
            checks.push(format!(
                "MR_ENCLAVE mismatch: expected {}, got {}",
                service_config.expected_mr_enclave,
                attestation.service_info.mr_enclave
            ));
            is_valid = false;
        }

        // 3. 验证MR_SIGNER
        if attestation.service_info.mr_signer != service_config.required_mr_signer {
            checks.push(format!(
                "MR_SIGNER mismatch: expected {}, got {}",
                service_config.required_mr_signer,
                attestation.service_info.mr_signer
            ));
            is_valid = false;
        }

        // 4. 验证签名者可信性
        if !self.is_signer_trusted(&attestation.service_info.mr_signer) {
            checks.push("MR_SIGNER is not in trusted list".to_string());
            is_valid = false;
        }

        // 5. 验证调试模式
        if self.trust_config.verification_policies.require_debug_disabled && attestation.service_info.debug_enabled {
            checks.push("Debug mode is enabled but not allowed".to_string());
            is_valid = false;
        }

        // 6. 验证TCB级别
        if attestation.service_info.tcb_level < self.trust_config.verification_policies.minimum_tcb_level {
            checks.push(format!(
                "TCB level {} is below minimum required {}",
                attestation.service_info.tcb_level,
                self.trust_config.verification_policies.minimum_tcb_level
            ));
            is_valid = false;
        }

        // 7. 验证attestation签名
        if !self.verify_attestation_signature(&attestation.attestation, &attestation.service_info.mr_signer).await? {
            checks.push("Attestation signature verification failed".to_string());
            is_valid = false;
        }

        Ok(VerificationResult {
            is_valid,
            checks,
            error_details: if is_valid { None } else { Some(checks.join("; ")) },
        })
    }

    fn is_signer_trusted(&self, mr_signer: &str) -> bool {
        self.trust_config.trusted_signers.values()
            .any(|signer| signer.mr_signer == mr_signer && signer.valid_until > Utc::now())
    }

    async fn verify_attestation_signature(&self, attestation: &str, mr_signer: &str) -> Result<bool, VerificationError> {
        // 实现attestation签名验证
        // 1. 解析attestation数据
        // 2. 提取签名
        // 3. 使用对应的公钥验证签名

        // 简化实现 - 实际应该使用SGX SDK进行验证
        Ok(true)
    }

    async fn establish_secure_connection(
        &self,
        service_config: &ServiceConfig,
        attestation: &AttestationResponse
    ) -> Result<SecureConnection, VerificationError> {
        // 1. 创建基于attestation的TLS配置
        let tls_config = self.create_attestation_based_tls_config(attestation)?;

        // 2. 建立连接
        let connection = SecureConnection::new(
            service_config.endpoint.clone(),
            tls_config,
            attestation.service_info.clone(),
        );

        Ok(connection)
    }

    fn create_attestation_based_tls_config(&self, attestation: &AttestationResponse) -> Result<rustls::ClientConfig, VerificationError> {
        use rustls::{ClientConfig, RootCertStore};
        use std::sync::Arc;

        let mut root_store = RootCertStore::empty();

        // 添加基于attestation的证书验证
        let config = ClientConfig::builder()
            .with_safe_defaults()
            .with_root_certificates(root_store)
            .with_no_client_auth();

        Ok(config)
    }

    fn cache_verification_result(&mut self, service_name: &str, attestation: &AttestationResponse) {
        let verified_service = VerifiedService {
            service_name: service_name.to_string(),
            last_verification: Utc::now(),
            attestation_valid_until: attestation.timestamp + chrono::Duration::seconds(
                self.trust_config.verification_policies.max_attestation_age_seconds as i64
            ),
            tls_config: rustls::ClientConfig::builder().with_safe_defaults().with_root_certificates(
                rustls::RootCertStore::empty()
            ).with_no_client_auth(),
        };

        self.verified_services.insert(service_name.to_string(), verified_service);
    }

    /// 检查缓存的验证结果是否仍然有效
    pub fn is_verification_cached(&self, service_name: &str) -> bool {
        if let Some(verified_service) = self.verified_services.get(service_name) {
            Utc::now() < verified_service.attestation_valid_until
        } else {
            false
        }
    }

    /// 从配置文件加载信任配置
    pub fn load_trust_config(config_path: &str) -> Result<TrustConfig, Box<dyn std::error::Error>> {
        let config_str = std::fs::read_to_string(config_path)?;
        let config: TrustConfig = serde_yaml::from_str(&config_str)?;
        Ok(config)
    }
}

#[derive(Debug)]
pub struct SecureConnection {
    endpoint: String,
    tls_config: rustls::ClientConfig,
    service_info: ServiceInfo,
    client: Client,
}

impl SecureConnection {
    fn new(endpoint: String, tls_config: rustls::ClientConfig, service_info: ServiceInfo) -> Self {
        let client = Client::builder()
            .use_preconfigured_tls(tls_config.clone())
            .build()
            .unwrap();

        Self {
            endpoint,
            tls_config,
            service_info,
            client,
        }
    }

    /// 发送经过验证的请求
    pub async fn send_request<T: serde::Serialize, R: serde::de::DeserializeOwned>(
        &self,
        path: &str,
        payload: &T,
    ) -> Result<R, Box<dyn std::error::Error>> {
        let url = format!("{}{}", self.endpoint, path);

        let response = self.client
            .post(&url)
            .json(payload)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(format!("Request failed with status: {}", response.status()).into());
        }

        let result: R = response.json().await?;
        Ok(result)
    }

    pub fn get_service_info(&self) -> &ServiceInfo {
        &self.service_info
    }
}

#[derive(Debug)]
struct VerificationResult {
    is_valid: bool,
    checks: Vec<String>,
    error_details: Option<String>,
}

#[derive(Debug, thiserror::Error)]
pub enum VerificationError {
    #[error("Service not configured: {0}")]
    ServiceNotConfigured,

    #[error("Network error: {0}")]
    NetworkError(String),

    #[error("Attestation request failed with status: {0}")]
    AttestationRequestFailed(u16),

    #[error("Invalid response: {0}")]
    InvalidResponse(String),

    #[error("Attestation is invalid: {0}")]
    AttestationInvalid(String),

    #[error("TLS configuration error: {0}")]
    TlsConfigError(String),
}
```

### 4.2 客户端SDK使用示例

**目标**: 提供易用的客户端SDK，让开发者能够轻松集成SGX验证功能

**客户端配置文件**:
```yaml
# client-config/trust-config.yaml
trusted_signers:
  prod-signer-2024:
    name: "Production Build Machine 2024"
    mr_signer: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
    public_key: |
      -----BEGIN PUBLIC KEY-----
      MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      -----END PUBLIC KEY-----
    valid_until: "2024-12-31T23:59:59Z"

service_endpoints:
  wallet-service:
    name: "Clique Wallet Service"
    endpoint: "https://wallet.test.superstack.xyz"
    expected_mr_enclave: "b3c4d5e6f7890123456789012345678901234567890abcdef1234567890abcdef"
    required_mr_signer: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
    attestation_endpoint: "https://wallet.test.superstack.xyz/attestation"

  wallet-signer:
    name: "Clique Wallet Signer"
    endpoint: "https://signer.test.superstack.xyz"
    expected_mr_enclave: "c4d5e6f7890123456789012345678901234567890abcdef1234567890abcdef12"
    required_mr_signer: "a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
    attestation_endpoint: "https://signer.test.superstack.xyz/attestation"

verification_policies:
  max_attestation_age_seconds: 300  # 5分钟
  require_debug_disabled: true
  minimum_tcb_level: 2
  allowed_advisories: []
```

**客户端使用示例**:
```rust
// examples/wallet-client.rs
use clique_sgx_client::{SGXVerifier, TrustConfig};
use serde_json::json;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 加载信任配置
    let trust_config = TrustConfig::load_from_file("client-config/trust-config.yaml")?;

    // 2. 创建SGX验证器
    let mut verifier = SGXVerifier::new(trust_config)?;

    // 3. 验证并连接到钱包服务
    println!("Connecting to wallet service...");
    let wallet_connection = verifier.verify_and_connect("wallet-service").await?;

    println!("✅ Successfully verified and connected to wallet service");
    println!("Service info: {:?}", wallet_connection.get_service_info());

    // 4. 执行业务操作
    let sign_request = json!({
        "user_id": "test-user",
        "message": "Hello, SGX World!",
        "network": "ethereum"
    });

    let sign_response: serde_json::Value = wallet_connection
        .send_request("/sign", &sign_request)
        .await?;

    println!("Sign response: {}", sign_response);

    // 5. 验证并连接到签名服务
    println!("Connecting to signer service...");
    let signer_connection = verifier.verify_and_connect("wallet-signer").await?;

    println!("✅ Successfully verified and connected to signer service");

    Ok(())
}
```

### 4.3 服务端Attestation接口实现

**目标**: 在现有SGX服务中添加attestation接口，供客户端验证使用

**Wallet Service Attestation接口**:
```rust
// services/clique-wallet-service/src/attestation.rs
use axum::{http::StatusCode, response::Json, routing::get, Router};
use serde::{Serialize, Deserialize};
use chrono::{DateTime, Utc};
use clique_sibyl_commonlib::attestation::generate_attestation;

#[derive(Debug, Serialize)]
pub struct AttestationResponse {
    pub attestation: String,
    pub timestamp: DateTime<Utc>,
    pub service_info: ServiceInfo,
}

#[derive(Debug, Serialize)]
pub struct ServiceInfo {
    pub service_name: String,
    pub version: String,
    pub mr_enclave: String,
    pub mr_signer: String,
    pub tcb_level: u32,
    pub debug_enabled: bool,
}

pub async fn get_attestation() -> Result<Json<AttestationResponse>, StatusCode> {
    // 1. 生成attestation
    let attestation_data = match generate_attestation(b"client-verification") {
        Ok(data) => data,
        Err(e) => {
            tracing::error!("Failed to generate attestation: {}", e);
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    };

    // 2. 获取enclave信息
    let service_info = get_service_info().await?;

    // 3. 构造响应
    let response = AttestationResponse {
        attestation: base64::encode(&attestation_data),
        timestamp: Utc::now(),
        service_info,
    };

    Ok(Json(response))
}

async fn get_service_info() -> Result<ServiceInfo, StatusCode> {
    // 从环境变量或配置文件获取MR值
    let mr_enclave = std::env::var("EXPECTED_MR_ENCLAVE")
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;
    let mr_signer = std::env::var("MR_SIGNER")
        .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

    // 检查是否在调试模式
    let debug_enabled = std::env::var("SGX_DEBUG")
        .unwrap_or_else(|_| "false".to_string())
        .parse::<bool>()
        .unwrap_or(false);

    Ok(ServiceInfo {
        service_name: "clique-wallet-service".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        mr_enclave,
        mr_signer,
        tcb_level: get_tcb_level().await,
        debug_enabled,
    })
}

async fn get_tcb_level() -> u32 {
    // 实际实现应该查询SGX平台信息
    // 这里简化返回固定值
    2
}

pub fn attestation_router() -> Router {
    Router::new()
        .route("/attestation", get(get_attestation))
}
```

**集成到主服务**:
```rust
// services/clique-wallet-service/src/main.rs (修改部分)
mod attestation;

// 在main函数中添加attestation路由
let app = Router::new()
    .route("/healthz", get(handle_healthz))
    // ... 其他路由
    .merge(attestation::attestation_router())  // 添加这行
    .layer(cors_layer)
    .layer(session_layer);
```

   on:
     push:
       branches: [main, develop]
     pull_request:
       branches: [main]

   env:
     REGISTRY: cliquesecure.azurecr.io
     AZURE_KEY_VAULT_URI: https://clique-sgx-vault.vault.azure.net/

   jobs:
     secure-build:
       runs-on: ubuntu-latest
       permissions:
         contents: read
         id-token: write
         packages: write

       steps:
       - name: Checkout code
         uses: actions/checkout@v4
         with:
           fetch-depth: 0  # 完整历史用于可重现构建

       - name: Azure Login
         uses: azure/login@v1
         with:
           client-id: ${{ secrets.AZURE_CLIENT_ID }}
           tenant-id: ${{ secrets.AZURE_TENANT_ID }}
           subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}

       - name: Install Cosign
         uses: sigstore/cosign-installer@v3
         with:
           cosign-release: 'v2.2.2'

       - name: Build reproducible image
         run: |
           # 设置可重现构建环境变量
           export SOURCE_DATE_EPOCH=$(git log -1 --format=%ct)
           export DOCKER_BUILDKIT=1

           docker build \
             --build-arg SOURCE_DATE_EPOCH=$SOURCE_DATE_EPOCH \
             --build-arg BUILDKIT_INLINE_CACHE=1 \
             --secret id=enclave-key,src=${{ secrets.ENCLAVE_KEY_PATH }} \
             --tag ${{ env.REGISTRY }}/${{ matrix.service }}:${{ github.sha }} \
             --tag ${{ env.REGISTRY }}/${{ matrix.service }}:latest \
             services/${{ matrix.service }}

       - name: Generate SBOM
         run: |
           # 生成软件物料清单
           syft ${{ env.REGISTRY }}/${{ matrix.service }}:${{ github.sha }} \
             -o spdx-json > sbom.spdx.json

       - name: Sign image and SBOM
         run: |
           # 签名容器镜像
           cosign sign --yes \
             --key azurekms://${{ env.AZURE_KEY_VAULT_URI }}keys/container-signing-key \
             ${{ env.REGISTRY }}/${{ matrix.service }}:${{ github.sha }}

           # 签名SBOM
           cosign attest --yes \
             --key azurekms://${{ env.AZURE_KEY_VAULT_URI }}keys/container-signing-key \
             --predicate sbom.spdx.json \
             ${{ env.REGISTRY }}/${{ matrix.service }}:${{ github.sha }}

       - name: Verify signatures
         run: |
           # 验证签名
           cosign verify \
             --key azurekms://${{ env.AZURE_KEY_VAULT_URI }}keys/container-signing-key \
             ${{ env.REGISTRY }}/${{ matrix.service }}:${{ github.sha }}

   strategy:
     matrix:
       service: [clique-wallet-signer, clique-wallet-service]
   ```

4. **签名验证脚本**
   ```bash
   #!/bin/bash
   # scripts/verify-image-signature.sh

   set -euo pipefail

   IMAGE_URI=$1
   KEY_VAULT_URI=$2
   KEY_NAME=$3

   echo "Verifying signature for image: $IMAGE_URI"

   # 验证镜像签名
   cosign verify \
     --key azurekms://${KEY_VAULT_URI}keys/${KEY_NAME} \
     "$IMAGE_URI"

   # 验证SBOM attestation
   cosign verify-attestation \
     --key azurekms://${KEY_VAULT_URI}keys/${KEY_NAME} \
     --type spdxjson \
     "$IMAGE_URI"

   echo "✅ Image signature verification successful"
   ```

### 1.3 SLSA供应链安全

**目标**: 实施SLSA Level 3标准，提供构建来源证明和完整的供应链保护

**实施步骤**:
1. **SLSA构建器配置**
   ```yaml
   # .github/workflows/slsa-build.yml
   name: SLSA Level 3 Build

   on:
     push:
       branches: [main, develop]
     release:
       types: [published]

   permissions:
     contents: read
     id-token: write
     packages: write

   jobs:
     # 构建阶段 - 使用SLSA构建器
     build:
       uses: slsa-framework/slsa-github-generator/.github/workflows/builder_docker_slsa3.yml@v1.9.0
       with:
         image: cliquesecure.azurecr.io
         registry-username: ${{ github.actor }}
         compile-builder: true
       secrets:
         registry-password: ${{ secrets.GITHUB_TOKEN }}

     # SGX特定的构建验证
     sgx-build-verification:
       needs: build
       runs-on: ubuntu-latest
       steps:
       - name: Verify SGX enclave measurements
         run: |
           # 下载构建产物
           docker pull ${{ needs.build.outputs.image }}@${{ needs.build.outputs.digest }}

           # 提取并验证SGX签名结构
           docker run --rm -v $(pwd):/output \
             ${{ needs.build.outputs.image }}@${{ needs.build.outputs.digest }} \
             sh -c "cp *.sig /output/"

           # 验证MR_ENCLAVE和MR_SIGNER
           gramine-sgx-sigstruct-view *.sig > enclave_measurements.txt

           # 与预期值比较
           python3 scripts/verify-enclave-measurements.py \
             --measurements enclave_measurements.txt \
             --expected-config config/expected-measurements.yaml

     # 生成和验证SLSA证明
     provenance:
       needs: [build, sgx-build-verification]
       runs-on: ubuntu-latest
       steps:
       - name: Generate SLSA provenance
         uses: slsa-framework/slsa-github-generator/actions/generator/generic@v1.9.0
         with:
           base64-subjects: ${{ needs.build.outputs.digest }}
           provenance-name: provenance.intoto.jsonl

       - name: Upload provenance
         uses: actions/upload-artifact@v4
         with:
           name: slsa-provenance
           path: provenance.intoto.jsonl
   ```

2. **构建证明验证脚本**
   ```python
   #!/usr/bin/env python3
   # scripts/verify-slsa-provenance.py

   import json
   import subprocess
   import sys
   from pathlib import Path

   def verify_slsa_provenance(image_uri, source_repo):
       """验证SLSA证明文件"""
       try:
           # 使用slsa-verifier验证
           result = subprocess.run([
               'slsa-verifier', 'verify-image', image_uri,
               '--source-uri', f'github.com/{source_repo}',
               '--builder-id', 'https://github.com/slsa-framework/slsa-github-generator/.github/workflows/builder_docker_slsa3.yml@refs/tags/v1.9.0'
           ], capture_output=True, text=True, check=True)

           print("✅ SLSA provenance verification successful")
           return True

       except subprocess.CalledProcessError as e:
           print(f"❌ SLSA provenance verification failed: {e.stderr}")
           return False

   def verify_sgx_measurements(provenance_file, expected_measurements):
       """验证SGX enclave测量值"""
       with open(provenance_file, 'r') as f:
           provenance = json.load(f)

       # 提取构建参数中的SGX相关信息
       build_config = provenance.get('predicate', {}).get('buildConfig', {})

       # 验证构建环境和参数
       required_env = [
           'SGX=1',
           'GRAMINE_VERSION=1.7',
           'SOURCE_DATE_EPOCH'
       ]

       for env_var in required_env:
           if env_var not in str(build_config):
               print(f"❌ Missing required build environment: {env_var}")
               return False

       print("✅ SGX build configuration verified")
       return True

   if __name__ == "__main__":
       if len(sys.argv) != 3:
           print("Usage: verify-slsa-provenance.py <image_uri> <source_repo>")
           sys.exit(1)

       image_uri = sys.argv[1]
       source_repo = sys.argv[2]

       success = verify_slsa_provenance(image_uri, source_repo)
       if not success:
           sys.exit(1)
   ```

3. **预期测量值配置**
   ```yaml
   # config/expected-measurements.yaml
   services:
     clique-wallet-signer:
       mr_enclave: "expected_mr_enclave_hash_here"
       mr_signer: "expected_mr_signer_hash_here"
       isv_prod_id: 0
       isv_svn: 0
       debug: false

     clique-wallet-service:
       mr_enclave: "expected_mr_enclave_hash_here"
       mr_signer: "expected_mr_signer_hash_here"
       isv_prod_id: 0
       isv_svn: 0
       debug: false

   # 构建环境要求
   build_requirements:
     gramine_version: "1.7"
     rust_version: "1.75.0"
     sgx_sdk_version: "2.22"
     reproducible_build: true
   ```

4. **供应链安全策略**
   ```yaml
   # .github/workflows/supply-chain-security.yml
   name: Supply Chain Security Checks

   on:
     pull_request:
       branches: [main]
     schedule:
       - cron: '0 2 * * *'  # 每日安全扫描

   jobs:
     dependency-scan:
       runs-on: ubuntu-latest
       steps:
       - uses: actions/checkout@v4

       - name: Run Trivy vulnerability scanner
         uses: aquasecurity/trivy-action@master
         with:
           scan-type: 'fs'
           scan-ref: '.'
           format: 'sarif'
           output: 'trivy-results.sarif'

       - name: Upload Trivy scan results
         uses: github/codeql-action/upload-sarif@v2
         with:
           sarif_file: 'trivy-results.sarif'

     license-compliance:
       runs-on: ubuntu-latest
       steps:
       - uses: actions/checkout@v4

       - name: Check license compliance
         run: |
           # 扫描依赖许可证
           cargo license --json > licenses.json
           python3 scripts/check-license-compliance.py licenses.json

     secrets-scan:
       runs-on: ubuntu-latest
       steps:
       - uses: actions/checkout@v4
         with:
           fetch-depth: 0

       - name: Run GitLeaks
         uses: gitleaks/gitleaks-action@v2
         env:
           GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
   ```

## 阶段2：部署验证增强

### 2.1 预部署Attestation验证

**目标**: 在部署前验证SGX enclave的完整性和可信性

**实施步骤**:
1. **Attestation验证服务**
   ```rust
   // services/attestation-verifier/src/main.rs
   use axum::{extract::Json, http::StatusCode, response::Json as ResponseJson, routing::post, Router};
   use serde::{Deserialize, Serialize};
   use std::collections::HashMap;
   use tokio::process::Command;

   #[derive(Debug, Deserialize)]
   pub struct DeploymentManifest {
       pub service_name: String,
       pub image_uri: String,
       pub expected_mr_enclave: String,
       pub expected_mr_signer: String,
       pub config_hash: String,
   }

   #[derive(Debug, Serialize)]
   pub struct VerificationResult {
       pub valid: bool,
       pub checks: HashMap<String, CheckResult>,
       pub timestamp: chrono::DateTime<chrono::Utc>,
   }

   #[derive(Debug, Serialize)]
   pub struct CheckResult {
       pub passed: bool,
       pub details: String,
   }

   pub struct AttestationVerifier {
       trusted_enclaves: Vec<String>,
       trusted_signers: Vec<String>,
       cosign_public_key: String,
   }

   impl AttestationVerifier {
       pub async fn verify_deployment(&self, manifest: &DeploymentManifest) -> Result<VerificationResult, Box<dyn std::error::Error>> {
           let mut checks = HashMap::new();
           let mut all_passed = true;

           // 1. 验证容器镜像签名
           let image_sig_check = self.verify_image_signature(&manifest.image_uri).await?;
           all_passed &= image_sig_check.passed;
           checks.insert("image_signature".to_string(), image_sig_check);

           // 2. 提取并验证SGX测量值
           let enclave_check = self.verify_enclave_measurements(manifest).await?;
           all_passed &= enclave_check.passed;
           checks.insert("enclave_measurements".to_string(), enclave_check);

           // 3. 验证SLSA证明
           let slsa_check = self.verify_slsa_provenance(&manifest.image_uri).await?;
           all_passed &= slsa_check.passed;
           checks.insert("slsa_provenance".to_string(), slsa_check);

           // 4. 验证配置完整性
           let config_check = self.verify_config_integrity(&manifest.config_hash).await?;
           all_passed &= config_check.passed;
           checks.insert("config_integrity".to_string(), config_check);

           Ok(VerificationResult {
               valid: all_passed,
               checks,
               timestamp: chrono::Utc::now(),
           })
       }

       async fn verify_image_signature(&self, image_uri: &str) -> Result<CheckResult, Box<dyn std::error::Error>> {
           let output = Command::new("cosign")
               .args(&[
                   "verify",
                   "--key", &self.cosign_public_key,
                   image_uri
               ])
               .output()
               .await?;

           let passed = output.status.success();
           let details = if passed {
               "Image signature verification successful".to_string()
           } else {
               format!("Image signature verification failed: {}",
                      String::from_utf8_lossy(&output.stderr))
           };

           Ok(CheckResult { passed, details })
       }

       async fn verify_enclave_measurements(&self, manifest: &DeploymentManifest) -> Result<CheckResult, Box<dyn std::error::Error>> {
           // 从容器中提取SGX签名文件
           let extract_output = Command::new("docker")
               .args(&[
                   "run", "--rm", "-v", "/tmp:/output",
                   &manifest.image_uri,
                   "sh", "-c", "cp *.sig /output/"
               ])
               .output()
               .await?;

           if !extract_output.status.success() {
               return Ok(CheckResult {
                   passed: false,
                   details: "Failed to extract SGX signature file".to_string(),
               });
           }

           // 解析SGX签名结构
           let sigstruct_output = Command::new("gramine-sgx-sigstruct-view")
               .arg("/tmp/*.sig")
               .output()
               .await?;

           if !sigstruct_output.status.success() {
               return Ok(CheckResult {
                   passed: false,
                   details: "Failed to parse SGX signature structure".to_string(),
               });
           }

           let sigstruct_info = String::from_utf8_lossy(&sigstruct_output.stdout);

           // 提取MR_ENCLAVE和MR_SIGNER
           let mr_enclave = self.extract_measurement(&sigstruct_info, "mr_enclave")?;
           let mr_signer = self.extract_measurement(&sigstruct_info, "mr_signer")?;

           // 验证测量值
           let enclave_match = mr_enclave == manifest.expected_mr_enclave;
           let signer_match = mr_signer == manifest.expected_mr_signer;
           let passed = enclave_match && signer_match;

           let details = format!(
               "MR_ENCLAVE: {} (expected: {}, match: {}), MR_SIGNER: {} (expected: {}, match: {})",
               mr_enclave, manifest.expected_mr_enclave, enclave_match,
               mr_signer, manifest.expected_mr_signer, signer_match
           );

           Ok(CheckResult { passed, details })
       }

       async fn verify_slsa_provenance(&self, image_uri: &str) -> Result<CheckResult, Box<dyn std::error::Error>> {
           let output = Command::new("slsa-verifier")
               .args(&[
                   "verify-image", image_uri,
                   "--source-uri", "github.com/your-org/clique-wallet",
                   "--builder-id", "https://github.com/slsa-framework/slsa-github-generator/.github/workflows/builder_docker_slsa3.yml@refs/tags/v1.9.0"
               ])
               .output()
               .await?;

           let passed = output.status.success();
           let details = if passed {
               "SLSA provenance verification successful".to_string()
           } else {
               format!("SLSA provenance verification failed: {}",
                      String::from_utf8_lossy(&output.stderr))
           };

           Ok(CheckResult { passed, details })
       }

       async fn verify_config_integrity(&self, config_hash: &str) -> Result<CheckResult, Box<dyn std::error::Error>> {
           // 这里应该实现配置完整性验证逻辑
           // 例如验证配置文件的哈希值、数字签名等
           Ok(CheckResult {
               passed: true,
               details: format!("Configuration integrity verified for hash: {}", config_hash),
           })
       }

       fn extract_measurement(&self, sigstruct_info: &str, measurement_type: &str) -> Result<String, Box<dyn std::error::Error>> {
           // 从sigstruct输出中提取特定的测量值
           for line in sigstruct_info.lines() {
               if line.contains(measurement_type) {
                   if let Some(value) = line.split(':').nth(1) {
                       return Ok(value.trim().to_string());
                   }
               }
           }
           Err(format!("Failed to extract {}", measurement_type).into())
       }
   }

   // HTTP API端点
   async fn verify_deployment_handler(
       Json(manifest): Json<DeploymentManifest>,
   ) -> Result<ResponseJson<VerificationResult>, StatusCode> {
       let verifier = AttestationVerifier {
           trusted_enclaves: vec![], // 从配置加载
           trusted_signers: vec![],  // 从配置加载
           cosign_public_key: "path/to/public/key".to_string(), // 从配置加载
       };

       match verifier.verify_deployment(&manifest).await {
           Ok(result) => Ok(ResponseJson(result)),
           Err(_) => Err(StatusCode::INTERNAL_SERVER_ERROR),
       }
   }

   #[tokio::main]
   async fn main() {
       let app = Router::new()
           .route("/verify", post(verify_deployment_handler));

       let listener = tokio::net::TcpListener::bind("0.0.0.0:8080").await.unwrap();
       axum::serve(listener, app).await.unwrap();
   }
   ```

2. **Kubernetes Admission Controller**
   ```yaml
   # k8s/attestation-verifier/deployment.yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: attestation-verifier
     namespace: sgx-system
   spec:
     replicas: 2
     selector:
       matchLabels:
         app: attestation-verifier
     template:
       metadata:
         labels:
           app: attestation-verifier
       spec:
         serviceAccountName: attestation-verifier
         containers:
         - name: verifier
           image: cliquesecure.azurecr.io/attestation-verifier:latest
           ports:
           - containerPort: 8080
           env:
           - name: COSIGN_PUBLIC_KEY
             valueFrom:
               secretKeyRef:
                 name: cosign-keys
                 key: public-key
           volumeMounts:
           - name: docker-socket
             mountPath: /var/run/docker.sock
           - name: tmp-volume
             mountPath: /tmp
         volumes:
         - name: docker-socket
           hostPath:
             path: /var/run/docker.sock
         - name: tmp-volume
           emptyDir: {}

   ---
   apiVersion: v1
   kind: Service
   metadata:
     name: attestation-verifier
     namespace: sgx-system
   spec:
     selector:
       app: attestation-verifier
     ports:
     - port: 443
       targetPort: 8080
       protocol: TCP
     type: ClusterIP

   ---
   apiVersion: admissionregistration.k8s.io/v1
   kind: ValidatingAdmissionWebhook
   metadata:
     name: sgx-attestation-validator
   webhooks:
   - name: validate-sgx-deployment.sgx-system.svc
     clientConfig:
       service:
         name: attestation-verifier
         namespace: sgx-system
         path: "/verify"
     rules:
     - operations: ["CREATE", "UPDATE"]
       apiGroups: ["apps"]
       apiVersions: ["v1"]
       resources: ["deployments"]
     namespaceSelector:
       matchLabels:
         sgx-enabled: "true"
     admissionReviewVersions: ["v1", "v1beta1"]
     sideEffects: None
     failurePolicy: Fail
   ```

### 2.2 配置完整性检查

**目标**: 确保部署配置未被篡改，符合安全策略

**实施步骤**:
1. **配置签名验证**
   ```bash
   # scripts/verify-config.sh
   # 验证.env文件和K8S配置的数字签名
   gpg --verify config.sig config.yaml
   ```

2. **策略即代码**
   ```rego
   # policies/sgx-deployment.rego
   package sgx.deployment
   
   deny[msg] {
       input.spec.template.spec.containers[_].env[_].name == "SGX_DEBUG"
       input.spec.template.spec.containers[_].env[_].value == "true"
       msg := "SGX debug mode not allowed in production"
   }
   ```

### 2.3 渐进式部署和自动回滚

**目标**: 安全地部署新版本，出现问题时自动回滚

**实施步骤**:
1. **Argo Rollouts金丝雀部署配置**
   ```yaml
   # k8s/rollouts/wallet-service-rollout.yaml
   apiVersion: argoproj.io/v1alpha1
   kind: Rollout
   metadata:
     name: wallet-service-rollout
     namespace: clique-wallet
   spec:
     replicas: 3
     strategy:
       canary:
         # 金丝雀部署步骤
         steps:
         - setWeight: 10  # 10%流量到新版本
         - pause: {duration: 2m}
         - analysis:
             templates:
             - templateName: sgx-attestation-analysis
             - templateName: success-rate-analysis
             args:
             - name: service-name
               value: wallet-service
         - setWeight: 25  # 25%流量
         - pause: {duration: 5m}
         - analysis:
             templates:
             - templateName: sgx-attestation-analysis
             - templateName: performance-analysis
         - setWeight: 50  # 50%流量
         - pause: {duration: 10m}
         - analysis:
             templates:
             - templateName: comprehensive-health-analysis
         - setWeight: 100 # 完全切换

         # 流量分割配置
         trafficRouting:
           istio:
             virtualService:
               name: wallet-service-vs
             destinationRule:
               name: wallet-service-dr
               canarySubsetName: canary
               stableSubsetName: stable

         # 自动回滚配置
         abortScaleDownDelaySeconds: 30
         scaleDownDelaySeconds: 30
         autoRollbackOnFailure: true

     selector:
       matchLabels:
         app: wallet-service
     template:
       metadata:
         labels:
           app: wallet-service
       spec:
         containers:
         - name: wallet-service
           image: cliquesecure.azurecr.io/wallet-service:latest
           ports:
           - containerPort: 3000
           - containerPort: 3001
           livenessProbe:
             httpGet:
               path: /healthz/enhanced
               port: 3000
               scheme: HTTPS
             initialDelaySeconds: 30
             periodSeconds: 10
           readinessProbe:
             httpGet:
               path: /healthz/ready
               port: 3000
               scheme: HTTPS
             initialDelaySeconds: 5
             periodSeconds: 5

   ---
   # SGX Attestation分析模板
   apiVersion: argoproj.io/v1alpha1
   kind: AnalysisTemplate
   metadata:
     name: sgx-attestation-analysis
   spec:
     args:
     - name: service-name
     metrics:
     - name: attestation-success-rate
       interval: 30s
       count: 5
       successCondition: result[0] >= 0.95
       failureLimit: 2
       provider:
         prometheus:
           address: http://prometheus.monitoring.svc.cluster.local:9090
           query: |
             sum(rate(sgx_attestation_success_total{service="{{args.service-name}}"}[2m])) /
             sum(rate(sgx_attestation_total{service="{{args.service-name}}"}[2m]))

     - name: enclave-health
       interval: 60s
       count: 3
       successCondition: result[0] == 1
       provider:
         prometheus:
           address: http://prometheus.monitoring.svc.cluster.local:9090
           query: |
             sgx_enclave_status{service="{{args.service-name}}"}

   ---
   # 成功率分析模板
   apiVersion: argoproj.io/v1alpha1
   kind: AnalysisTemplate
   metadata:
     name: success-rate-analysis
   spec:
     metrics:
     - name: success-rate
       interval: 2m
       count: 5
       successCondition: result[0] >= 0.95
       failureLimit: 3
       provider:
         prometheus:
           address: http://prometheus.monitoring.svc.cluster.local:9090
           query: |
             sum(rate(http_requests_total{status!~"5.."}[2m])) /
             sum(rate(http_requests_total[2m]))

   ---
   # 性能分析模板
   apiVersion: argoproj.io/v1alpha1
   kind: AnalysisTemplate
   metadata:
     name: performance-analysis
   spec:
     metrics:
     - name: response-time-p95
       interval: 2m
       count: 5
       successCondition: result[0] <= 500  # 500ms
       provider:
         prometheus:
           address: http://prometheus.monitoring.svc.cluster.local:9090
           query: |
             histogram_quantile(0.95,
               sum(rate(http_request_duration_seconds_bucket[2m])) by (le)
             ) * 1000
   ```

2. **增强的健康检查实现**
   ```rust
   // services/clique-wallet-service/src/health.rs
   use axum::{extract::Query, http::StatusCode, response::Json, routing::get, Router};
   use serde::{Deserialize, Serialize};
   use std::collections::HashMap;
   use chrono::{DateTime, Utc};

   #[derive(Debug, Serialize)]
   pub struct HealthStatus {
       pub status: String,
       pub timestamp: DateTime<Utc>,
       pub checks: HashMap<String, CheckStatus>,
       pub version: String,
       pub uptime: u64,
   }

   #[derive(Debug, Serialize)]
   pub struct CheckStatus {
       pub status: String,
       pub message: String,
       pub duration_ms: u64,
       pub last_success: Option<DateTime<Utc>>,
   }

   #[derive(Debug, Deserialize)]
   pub struct HealthQuery {
       pub detailed: Option<bool>,
   }

   pub async fn enhanced_health_check(Query(params): Query<HealthQuery>) -> Result<Json<HealthStatus>, StatusCode> {
       let start_time = std::time::Instant::now();
       let mut checks = HashMap::new();
       let mut overall_healthy = true;

       // 1. 基础健康检查
       let basic_check_start = std::time::Instant::now();
       let basic_health = perform_basic_health_check().await;
       let basic_duration = basic_check_start.elapsed().as_millis() as u64;

       checks.insert("basic".to_string(), CheckStatus {
           status: if basic_health { "healthy" } else { "unhealthy" }.to_string(),
           message: if basic_health { "All basic checks passed" } else { "Basic checks failed" }.to_string(),
           duration_ms: basic_duration,
           last_success: if basic_health { Some(Utc::now()) } else { None },
       });
       overall_healthy &= basic_health;

       // 2. SGX Attestation验证
       let attestation_check_start = std::time::Instant::now();
       let attestation_result = verify_self_attestation().await;
       let attestation_duration = attestation_check_start.elapsed().as_millis() as u64;

       let attestation_healthy = attestation_result.is_ok();
       checks.insert("sgx_attestation".to_string(), CheckStatus {
           status: if attestation_healthy { "healthy" } else { "unhealthy" }.to_string(),
           message: match attestation_result {
               Ok(_) => "SGX attestation verification successful".to_string(),
               Err(e) => format!("SGX attestation failed: {}", e),
           },
           duration_ms: attestation_duration,
           last_success: if attestation_healthy { Some(Utc::now()) } else { None },
       });
       overall_healthy &= attestation_healthy;

       // 3. 数据库连接检查
       let db_check_start = std::time::Instant::now();
       let db_health = check_database_connection().await;
       let db_duration = db_check_start.elapsed().as_millis() as u64;

       checks.insert("database".to_string(), CheckStatus {
           status: if db_health { "healthy" } else { "unhealthy" }.to_string(),
           message: if db_health { "Database connection healthy" } else { "Database connection failed" }.to_string(),
           duration_ms: db_duration,
           last_success: if db_health { Some(Utc::now()) } else { None },
       });
       overall_healthy &= db_health;

       // 4. KMS连接检查
       let kms_check_start = std::time::Instant::now();
       let kms_health = check_kms_connection().await;
       let kms_duration = kms_check_start.elapsed().as_millis() as u64;

       checks.insert("kms".to_string(), CheckStatus {
           status: if kms_health { "healthy" } else { "unhealthy" }.to_string(),
           message: if kms_health { "KMS connection healthy" } else { "KMS connection failed" }.to_string(),
           duration_ms: kms_duration,
           last_success: if kms_health { Some(Utc::now()) } else { None },
       });
       overall_healthy &= kms_health;

       // 5. Signer服务连接检查
       let signer_check_start = std::time::Instant::now();
       let signer_health = check_signer_connection().await;
       let signer_duration = signer_check_start.elapsed().as_millis() as u64;

       checks.insert("signer".to_string(), CheckStatus {
           status: if signer_health { "healthy" } else { "unhealthy" }.to_string(),
           message: if signer_health { "Signer service healthy" } else { "Signer service connection failed" }.to_string(),
           duration_ms: signer_duration,
           last_success: if signer_health { Some(Utc::now()) } else { None },
       });
       overall_healthy &= signer_health;

       let health_status = HealthStatus {
           status: if overall_healthy { "healthy" } else { "unhealthy" }.to_string(),
           timestamp: Utc::now(),
           checks: if params.detailed.unwrap_or(false) { checks } else { HashMap::new() },
           version: env!("CARGO_PKG_VERSION").to_string(),
           uptime: get_uptime_seconds(),
       };

       if overall_healthy {
           Ok(Json(health_status))
       } else {
           Err(StatusCode::SERVICE_UNAVAILABLE)
       }
   }

   async fn perform_basic_health_check() -> bool {
       // 基础系统检查
       true
   }

   async fn verify_self_attestation() -> Result<(), Box<dyn std::error::Error>> {
       use clique_sibyl_commonlib::attestation::generate_attestation;

       // 生成自身的attestation
       let attestation = generate_attestation(b"health-check")?;

       // 验证attestation的有效性
       // 这里应该包含对attestation结构的验证
       if attestation.is_empty() {
           return Err("Empty attestation generated".into());
       }

       Ok(())
   }

   async fn check_database_connection() -> bool {
       use crate::storage::PostgresClient;

       match PostgresClient::get().await.execute("SELECT 1", &[]).await {
           Ok(_) => true,
           Err(_) => false,
       }
   }

   async fn check_kms_connection() -> bool {
       let kms_url = std::env::var("KMS_URL").unwrap_or_default();
       if kms_url.is_empty() {
           return false;
       }

       // 尝试连接KMS健康检查端点
       match reqwest::get(&format!("{}/healthz", kms_url)).await {
           Ok(response) => response.status().is_success(),
           Err(_) => false,
       }
   }

   async fn check_signer_connection() -> bool {
       use crate::config::config;

       let signer_endpoint = &config().signer_endpoint;
       match reqwest::get(&format!("{}/healthz", signer_endpoint)).await {
           Ok(response) => response.status().is_success(),
           Err(_) => false,
       }
   }

   fn get_uptime_seconds() -> u64 {
       // 实现获取服务运行时间的逻辑
       0
   }

   // 就绪检查端点 - 用于Kubernetes readiness probe
   pub async fn readiness_check() -> Result<Json<serde_json::Value>, StatusCode> {
       // 快速检查关键依赖是否就绪
       let db_ready = check_database_connection().await;
       let kms_ready = check_kms_connection().await;

       if db_ready && kms_ready {
           Ok(Json(serde_json::json!({
               "status": "ready",
               "timestamp": Utc::now()
           })))
       } else {
           Err(StatusCode::SERVICE_UNAVAILABLE)
       }
   }

   pub fn health_router() -> Router {
       Router::new()
           .route("/healthz", get(|| async { "OK" }))
           .route("/healthz/enhanced", get(enhanced_health_check))
           .route("/healthz/ready", get(readiness_check))
   }
   ```

## 阶段3：运行时监控增强

### 3.1 持续Attestation验证服务

**目标**: 定期验证运行中的SGX enclave状态

**实施步骤**:
1. **Attestation监控服务**
   ```rust
   // services/attestation-monitor/src/main.rs
   use axum::{extract::State, http::StatusCode, response::Json, routing::get, Router};
   use clique_sibyl_commonlib::attestation::verify_attestation;
   use prometheus::{Counter, Histogram, Gauge, register_counter, register_histogram, register_gauge};
   use serde::{Deserialize, Serialize};
   use std::{collections::HashMap, sync::Arc, time::Duration};
   use tokio::time::{interval, Instant};
   use tracing::{error, info, warn};

   lazy_static::lazy_static! {
       static ref ATTESTATION_CHECKS_TOTAL: Counter = register_counter!(
           "sgx_attestation_checks_total",
           "Total number of attestation checks performed"
       ).unwrap();

       static ref ATTESTATION_FAILURES_TOTAL: Counter = register_counter!(
           "sgx_attestation_failures_total",
           "Total number of attestation check failures"
       ).unwrap();

       static ref ATTESTATION_DURATION: Histogram = register_histogram!(
           "sgx_attestation_duration_seconds",
           "Time spent on attestation verification"
       ).unwrap();

       static ref ENCLAVE_STATUS: Gauge = register_gauge!(
           "sgx_enclave_status",
           "Current status of SGX enclave (1=healthy, 0=unhealthy)"
       ).unwrap();
   }

   #[derive(Debug, Clone, Deserialize)]
   pub struct MonitorTarget {
       pub name: String,
       pub endpoint: String,
       pub interval_seconds: u64,
       pub expected_mr_enclave: String,
       pub expected_mr_signer: String,
       pub timeout_seconds: u64,
   }

   #[derive(Debug, Serialize)]
   pub struct MonitorStatus {
       pub target: String,
       pub status: String,
       pub last_check: chrono::DateTime<chrono::Utc>,
       pub last_success: Option<chrono::DateTime<chrono::Utc>>,
       pub consecutive_failures: u32,
       pub total_checks: u64,
       pub total_failures: u64,
   }

   #[derive(Debug)]
   pub struct AttestationMonitor {
       targets: Vec<MonitorTarget>,
       status_map: Arc<tokio::sync::RwLock<HashMap<String, MonitorStatus>>>,
       alert_sender: tokio::sync::mpsc::Sender<AlertEvent>,
   }

   #[derive(Debug, Clone)]
   pub enum AlertEvent {
       AttestationFailure {
           target: String,
           error: String,
           consecutive_failures: u32,
       },
       ServiceRecovered {
           target: String,
           downtime_duration: Duration,
       },
       CriticalFailure {
           target: String,
           error: String,
       },
   }

   impl AttestationMonitor {
       pub fn new(targets: Vec<MonitorTarget>) -> (Self, tokio::sync::mpsc::Receiver<AlertEvent>) {
           let (alert_sender, alert_receiver) = tokio::sync::mpsc::channel(100);
           let status_map = Arc::new(tokio::sync::RwLock::new(HashMap::new()));

           // 初始化状态
           for target in &targets {
               let mut status_map_write = status_map.blocking_write();
               status_map_write.insert(target.name.clone(), MonitorStatus {
                   target: target.name.clone(),
                   status: "unknown".to_string(),
                   last_check: chrono::Utc::now(),
                   last_success: None,
                   consecutive_failures: 0,
                   total_checks: 0,
                   total_failures: 0,
               });
           }

           (Self {
               targets,
               status_map,
               alert_sender,
           }, alert_receiver)
       }

       pub async fn start_monitoring(&self) {
           info!("Starting attestation monitoring for {} targets", self.targets.len());

           let mut tasks = Vec::new();

           for target in &self.targets {
               let target = target.clone();
               let status_map = self.status_map.clone();
               let alert_sender = self.alert_sender.clone();

               let task = tokio::spawn(async move {
                   Self::monitor_target(target, status_map, alert_sender).await;
               });

               tasks.push(task);
           }

           // 等待所有监控任务
           futures::future::join_all(tasks).await;
       }

       async fn monitor_target(
           target: MonitorTarget,
           status_map: Arc<tokio::sync::RwLock<HashMap<String, MonitorStatus>>>,
           alert_sender: tokio::sync::mpsc::Sender<AlertEvent>,
       ) {
           let mut interval = interval(Duration::from_secs(target.interval_seconds));

           loop {
               interval.tick().await;

               let start_time = Instant::now();
               ATTESTATION_CHECKS_TOTAL.inc();

               let result = Self::verify_target_attestation(&target).await;
               let duration = start_time.elapsed();
               ATTESTATION_DURATION.observe(duration.as_secs_f64());

               // 更新状态
               let mut status_map_write = status_map.write().await;
               if let Some(status) = status_map_write.get_mut(&target.name) {
                   status.last_check = chrono::Utc::now();
                   status.total_checks += 1;

                   match result {
                       Ok(_) => {
                           let was_failing = status.consecutive_failures > 0;
                           let downtime_duration = if was_failing && status.last_success.is_some() {
                               chrono::Utc::now().signed_duration_since(status.last_success.unwrap())
                                   .to_std().unwrap_or(Duration::from_secs(0))
                           } else {
                               Duration::from_secs(0)
                           };

                           status.status = "healthy".to_string();
                           status.last_success = Some(chrono::Utc::now());
                           status.consecutive_failures = 0;
                           ENCLAVE_STATUS.set(1.0);

                           if was_failing {
                               info!("Target {} recovered after {} failures", target.name, status.consecutive_failures);
                               let _ = alert_sender.send(AlertEvent::ServiceRecovered {
                                   target: target.name.clone(),
                                   downtime_duration,
                               }).await;
                           }
                       }
                       Err(e) => {
                           status.status = "unhealthy".to_string();
                           status.consecutive_failures += 1;
                           status.total_failures += 1;
                           ATTESTATION_FAILURES_TOTAL.inc();
                           ENCLAVE_STATUS.set(0.0);

                           error!("Attestation verification failed for {}: {}", target.name, e);

                           // 发送告警
                           let alert = if status.consecutive_failures >= 5 {
                               AlertEvent::CriticalFailure {
                                   target: target.name.clone(),
                                   error: e.to_string(),
                               }
                           } else {
                               AlertEvent::AttestationFailure {
                                   target: target.name.clone(),
                                   error: e.to_string(),
                                   consecutive_failures: status.consecutive_failures,
                               }
                           };

                           let _ = alert_sender.send(alert).await;
                       }
                   }
               }
           }
       }

       async fn verify_target_attestation(target: &MonitorTarget) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
           let client = reqwest::ClientBuilder::new()
               .timeout(Duration::from_secs(target.timeout_seconds))
               .build()?;

           // 请求目标服务生成attestation
           let attestation_response = client
               .get(&format!("{}/attestation", target.endpoint))
               .send()
               .await?;

           if !attestation_response.status().is_success() {
               return Err(format!("Failed to get attestation from {}: {}",
                                target.name, attestation_response.status()).into());
           }

           let attestation_data: serde_json::Value = attestation_response.json().await?;
           let attestation = attestation_data["attestation"].as_str()
               .ok_or("Missing attestation in response")?;

           // 验证attestation
           let verification_result = verify_attestation(
               attestation,
               &target.expected_mr_enclave,
               &target.expected_mr_signer,
           )?;

           if !verification_result {
               return Err("Attestation verification failed".into());
           }

           Ok(())
       }

       pub async fn get_status(&self) -> HashMap<String, MonitorStatus> {
           self.status_map.read().await.clone()
       }
   }

   // HTTP API处理器
   async fn get_monitor_status(
       State(monitor): State<Arc<AttestationMonitor>>,
   ) -> Result<Json<HashMap<String, MonitorStatus>>, StatusCode> {
       let status = monitor.get_status().await;
       Ok(Json(status))
   }

   async fn get_metrics() -> String {
       use prometheus::TextEncoder;
       let encoder = TextEncoder::new();
       let metric_families = prometheus::gather();
       encoder.encode_to_string(&metric_families).unwrap_or_default()
   }

   #[tokio::main]
   async fn main() -> Result<(), Box<dyn std::error::Error>> {
       tracing_subscriber::init();

       // 从配置文件加载监控目标
       let config_str = std::fs::read_to_string("config.yaml")?;
       let config: serde_yaml::Value = serde_yaml::from_str(&config_str)?;

       let targets: Vec<MonitorTarget> = config["targets"]
           .as_sequence()
           .ok_or("Invalid targets configuration")?
           .iter()
           .map(|t| serde_yaml::from_value(t.clone()))
           .collect::<Result<Vec<_>, _>>()?;

       let (monitor, mut alert_receiver) = AttestationMonitor::new(targets);
       let monitor = Arc::new(monitor);

       // 启动告警处理器
       let alert_handler = tokio::spawn(async move {
           while let Some(alert) = alert_receiver.recv().await {
               handle_alert(alert).await;
           }
       });

       // 启动监控
       let monitor_clone = monitor.clone();
       let monitoring_task = tokio::spawn(async move {
           monitor_clone.start_monitoring().await;
       });

       // 启动HTTP服务器
       let app = Router::new()
           .route("/status", get(get_monitor_status))
           .route("/metrics", get(get_metrics))
           .with_state(monitor);

       let listener = tokio::net::TcpListener::bind("0.0.0.0:8080").await?;
       let server_task = tokio::spawn(async move {
           axum::serve(listener, app).await.unwrap();
       });

       tokio::select! {
           _ = monitoring_task => error!("Monitoring task exited"),
           _ = alert_handler => error!("Alert handler exited"),
           _ = server_task => error!("HTTP server exited"),
       }

       Ok(())
   }

   async fn handle_alert(alert: AlertEvent) {
       match alert {
           AlertEvent::AttestationFailure { target, error, consecutive_failures } => {
               warn!("Attestation failure for {}: {} (consecutive: {})", target, error, consecutive_failures);
               // 发送到告警系统 (Slack, PagerDuty, etc.)
           }
           AlertEvent::ServiceRecovered { target, downtime_duration } => {
               info!("Service {} recovered after {:?} downtime", target, downtime_duration);
           }
           AlertEvent::CriticalFailure { target, error } => {
               error!("CRITICAL: Attestation failure for {}: {}", target, error);
               // 发送紧急告警
           }
       }
   }
   ```

2. **监控配置和部署**
   ```yaml
   # k8s/attestation-monitor/configmap.yaml
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: attestation-monitor-config
     namespace: sgx-system
   data:
     config.yaml: |
       targets:
         - name: wallet-signer
           endpoint: https://wallet-signer.clique-wallet.svc.cluster.local:3000
           interval_seconds: 300
           expected_mr_enclave: "your_expected_mr_enclave_hash"
           expected_mr_signer: "your_expected_mr_signer_hash"
           timeout_seconds: 30
         - name: wallet-service
           endpoint: https://wallet-service.clique-wallet.svc.cluster.local:3000
           interval_seconds: 300
           expected_mr_enclave: "your_expected_mr_enclave_hash"
           expected_mr_signer: "your_expected_mr_signer_hash"
           timeout_seconds: 30
         - name: kms
           endpoint: https://kms.clique-kms.svc.cluster.local:7070
           interval_seconds: 600
           expected_mr_enclave: "your_expected_mr_enclave_hash"
           expected_mr_signer: "your_expected_mr_signer_hash"
           timeout_seconds: 30

   ---
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: attestation-monitor
     namespace: sgx-system
   spec:
     replicas: 2
     selector:
       matchLabels:
         app: attestation-monitor
     template:
       metadata:
         labels:
           app: attestation-monitor
       spec:
         serviceAccountName: attestation-monitor
         containers:
         - name: monitor
           image: cliquesecure.azurecr.io/attestation-monitor:latest
           ports:
           - containerPort: 8080
           volumeMounts:
           - name: config
             mountPath: /app/config.yaml
             subPath: config.yaml
           env:
           - name: RUST_LOG
             value: "info"
           resources:
             requests:
               cpu: 100m
               memory: 128Mi
             limits:
               cpu: 500m
               memory: 512Mi
           livenessProbe:
             httpGet:
               path: /status
               port: 8080
             initialDelaySeconds: 30
             periodSeconds: 30
           readinessProbe:
             httpGet:
               path: /status
               port: 8080
             initialDelaySeconds: 5
             periodSeconds: 10
         volumes:
         - name: config
           configMap:
             name: attestation-monitor-config

   ---
   apiVersion: v1
   kind: Service
   metadata:
     name: attestation-monitor
     namespace: sgx-system
     labels:
       app: attestation-monitor
   spec:
     selector:
       app: attestation-monitor
     ports:
     - name: http
       port: 8080
       targetPort: 8080
     type: ClusterIP

   ---
   apiVersion: v1
   kind: ServiceAccount
   metadata:
     name: attestation-monitor
     namespace: sgx-system

   ---
   apiVersion: rbac.authorization.k8s.io/v1
   kind: ClusterRole
   metadata:
     name: attestation-monitor
   rules:
   - apiGroups: [""]
     resources: ["services", "endpoints"]
     verbs: ["get", "list", "watch"]
   - apiGroups: ["apps"]
     resources: ["deployments"]
     verbs: ["get", "list", "watch"]

   ---
   apiVersion: rbac.authorization.k8s.io/v1
   kind: ClusterRoleBinding
   metadata:
     name: attestation-monitor
   roleRef:
     apiGroup: rbac.authorization.k8s.io
     kind: ClusterRole
     name: attestation-monitor
   subjects:
   - kind: ServiceAccount
     name: attestation-monitor
     namespace: sgx-system
   ```

### 3.2 异常行为检测

**目标**: 检测和响应异常的系统行为

**实施步骤**:
1. **Falco规则配置**
   ```yaml
   # k8s/falco/sgx-rules.yaml
   customRules:
     sgx-security.yaml: |-
       # SGX Enclave异常系统调用检测
       - rule: SGX Enclave Unexpected System Call
         desc: Detect unexpected system calls from SGX enclave processes
         condition: >
           spawned_process and
           proc.name in (clique-wallet-signer, clique-wallet-service) and
           syscall.type in (execve, fork, clone, ptrace)
         output: >
           Unexpected system call from SGX enclave
           (proc=%proc.name pid=%proc.pid syscall=%syscall.type user=%user.name container=%container.name)
         priority: HIGH
         tags: [sgx, security, anomaly]

       # SGX进程异常网络连接
       - rule: SGX Process Unexpected Network Connection
         desc: Detect unexpected network connections from SGX processes
         condition: >
           outbound and
           proc.name in (clique-wallet-signer, clique-wallet-service) and
           not fd.sip in (kms_allowed_ips, signer_allowed_ips, db_allowed_ips)
         output: >
           SGX process making unexpected network connection
           (proc=%proc.name pid=%proc.pid dest=%fd.sip:%fd.sport container=%container.name)
         priority: HIGH
         tags: [sgx, network, security]

       # SGX进程文件系统异常访问
       - rule: SGX Process Unexpected File Access
         desc: Detect unexpected file system access from SGX processes
         condition: >
           open_write and
           proc.name in (clique-wallet-signer, clique-wallet-service) and
           not fd.name startswith /tmp and
           not fd.name startswith /var/log and
           not fd.name startswith /proc/self
         output: >
           SGX process unexpected file write
           (proc=%proc.name pid=%proc.pid file=%fd.name container=%container.name)
         priority: MEDIUM
         tags: [sgx, filesystem, security]

       # 容器运行时异常
       - rule: SGX Container Runtime Anomaly
         desc: Detect runtime anomalies in SGX containers
         condition: >
           container and
           container.image.repository contains "clique" and
           (change_thread_namespace or
            proc.name != proc.exepath or
            proc.cmdline contains "gdb" or
            proc.cmdline contains "strace")
         output: >
           SGX container runtime anomaly detected
           (container=%container.name image=%container.image.repository proc=%proc.name cmdline=%proc.cmdline)
         priority: HIGH
         tags: [sgx, container, runtime]

       # SGX Enclave内存异常
       - rule: SGX Enclave Memory Anomaly
         desc: Detect memory-related anomalies in SGX enclaves
         condition: >
           syscall and
           proc.name in (clique-wallet-signer, clique-wallet-service) and
           syscall.type in (mmap, munmap, mprotect, brk) and
           proc.vmsize > 2147483648  # 2GB
         output: >
           SGX enclave memory anomaly
           (proc=%proc.name pid=%proc.pid syscall=%syscall.type vmsize=%proc.vmsize container=%container.name)
         priority: MEDIUM
         tags: [sgx, memory, performance]

   ---
   # Falco部署配置
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: falco-config
     namespace: falco-system
   data:
     falco.yaml: |
       rules_file:
         - /etc/falco/falco_rules.yaml
         - /etc/falco/falco_rules.local.yaml
         - /etc/falco/sgx-security.yaml

       json_output: true
       json_include_output_property: true
       json_include_tags_property: true

       http_output:
         enabled: true
         url: "http://falco-exporter.falco-system.svc.cluster.local:9376/events"

       grpc:
         enabled: true
         bind_address: "0.0.0.0:5060"
         threadiness: 0

       grpc_output:
         enabled: true

   ---
   apiVersion: apps/v1
   kind: DaemonSet
   metadata:
     name: falco
     namespace: falco-system
   spec:
     selector:
       matchLabels:
         app: falco
     template:
       metadata:
         labels:
           app: falco
       spec:
         serviceAccount: falco
         hostNetwork: true
         hostPID: true
         containers:
         - name: falco
           image: falcosecurity/falco:0.36.2
           securityContext:
             privileged: true
           volumeMounts:
           - name: proc
             mountPath: /host/proc
             readOnly: true
           - name: boot
             mountPath: /host/boot
             readOnly: true
           - name: lib-modules
             mountPath: /host/lib/modules
             readOnly: true
           - name: usr
             mountPath: /host/usr
             readOnly: true
           - name: etc
             mountPath: /host/etc
             readOnly: true
           - name: falco-config
             mountPath: /etc/falco
           env:
           - name: FALCO_GRPC_ENABLED
             value: "true"
           - name: FALCO_GRPC_BIND_ADDRESS
             value: "0.0.0.0:5060"
         volumes:
         - name: proc
           hostPath:
             path: /proc
         - name: boot
           hostPath:
             path: /boot
         - name: lib-modules
           hostPath:
             path: /lib/modules
         - name: usr
           hostPath:
             path: /usr
         - name: etc
           hostPath:
             path: /etc
         - name: falco-config
           configMap:
             name: falco-config
   ```

2. **自定义监控指标和告警**
   ```rust
   // services/security-monitor/src/metrics.rs
   use prometheus::{Counter, Histogram, Gauge, IntGauge, register_counter, register_histogram, register_gauge, register_int_gauge};
   use std::collections::HashMap;
   use tokio::sync::RwLock;

   lazy_static::lazy_static! {
       // SGX相关指标
       static ref SGX_ATTESTATION_FAILURES: Counter = register_counter!(
           "sgx_attestation_failures_total",
           "Total number of SGX attestation failures"
       ).unwrap();

       static ref SGX_ATTESTATION_DURATION: Histogram = register_histogram!(
           "sgx_attestation_duration_seconds",
           "Time spent on SGX attestation verification"
       ).unwrap();

       static ref SGX_ENCLAVE_STATUS: Gauge = register_gauge!(
           "sgx_enclave_status",
           "Current status of SGX enclave (1=healthy, 0=unhealthy)"
       ).unwrap();

       // 安全事件指标
       static ref SECURITY_EVENTS_TOTAL: Counter = register_counter!(
           "security_events_total",
           "Total number of security events detected"
       ).unwrap();

       static ref ANOMALY_DETECTIONS_TOTAL: Counter = register_counter!(
           "anomaly_detections_total",
           "Total number of anomaly detections"
       ).unwrap();

       static ref THREAT_LEVEL: IntGauge = register_int_gauge!(
           "current_threat_level",
           "Current threat level (0=low, 1=medium, 2=high, 3=critical)"
       ).unwrap();

       // 性能指标
       static ref ENCLAVE_MEMORY_USAGE: Gauge = register_gauge!(
           "sgx_enclave_memory_usage_bytes",
           "Current memory usage of SGX enclave"
       ).unwrap();

       static ref ENCLAVE_CPU_USAGE: Gauge = register_gauge!(
           "sgx_enclave_cpu_usage_percent",
           "Current CPU usage of SGX enclave"
       ).unwrap();
   }

   #[derive(Debug, Clone)]
   pub struct SecurityMetrics {
       pub service_name: String,
       pub anomaly_counts: RwLock<HashMap<String, u64>>,
       pub last_attestation: RwLock<Option<chrono::DateTime<chrono::Utc>>>,
   }

   impl SecurityMetrics {
       pub fn new(service_name: String) -> Self {
           Self {
               service_name,
               anomaly_counts: RwLock::new(HashMap::new()),
               last_attestation: RwLock::new(None),
           }
       }

       pub async fn record_attestation_failure(&self, error_type: &str) {
           SGX_ATTESTATION_FAILURES.inc();
           SECURITY_EVENTS_TOTAL.inc();

           let mut counts = self.anomaly_counts.write().await;
           *counts.entry(format!("attestation_failure_{}", error_type)).or_insert(0) += 1;

           // 更新威胁等级
           self.update_threat_level().await;
       }

       pub async fn record_anomaly(&self, anomaly_type: &str, severity: u8) {
           ANOMALY_DETECTIONS_TOTAL.inc();
           SECURITY_EVENTS_TOTAL.inc();

           let mut counts = self.anomaly_counts.write().await;
           *counts.entry(anomaly_type.to_string()).or_insert(0) += 1;

           // 根据严重程度更新威胁等级
           if severity >= 3 {
               THREAT_LEVEL.set(3); // Critical
           } else if severity >= 2 {
               THREAT_LEVEL.set(std::cmp::max(THREAT_LEVEL.get(), 2)); // High
           }
       }

       pub async fn record_successful_attestation(&self) {
           let mut last_attestation = self.last_attestation.write().await;
           *last_attestation = Some(chrono::Utc::now());
           SGX_ENCLAVE_STATUS.set(1.0);
       }

       pub async fn update_resource_usage(&self, memory_bytes: f64, cpu_percent: f64) {
           ENCLAVE_MEMORY_USAGE.set(memory_bytes);
           ENCLAVE_CPU_USAGE.set(cpu_percent);
       }

       async fn update_threat_level(&self) {
           let counts = self.anomaly_counts.read().await;
           let total_anomalies: u64 = counts.values().sum();

           let threat_level = match total_anomalies {
               0..=5 => 0,      // Low
               6..=15 => 1,     // Medium
               16..=30 => 2,    // High
               _ => 3,          // Critical
           };

           THREAT_LEVEL.set(threat_level);
       }

       pub async fn get_anomaly_summary(&self) -> HashMap<String, u64> {
           self.anomaly_counts.read().await.clone()
       }
   }

   // Falco事件处理器
   #[derive(Debug, serde::Deserialize)]
   pub struct FalcoEvent {
       pub time: String,
       pub rule: String,
       pub priority: String,
       pub output: String,
       pub output_fields: HashMap<String, serde_json::Value>,
       pub tags: Option<Vec<String>>,
   }

   pub struct FalcoEventProcessor {
       metrics: SecurityMetrics,
       alert_sender: tokio::sync::mpsc::Sender<SecurityAlert>,
   }

   #[derive(Debug, Clone)]
   pub struct SecurityAlert {
       pub alert_type: String,
       pub severity: String,
       pub message: String,
       pub service: String,
       pub timestamp: chrono::DateTime<chrono::Utc>,
       pub metadata: HashMap<String, String>,
   }

   impl FalcoEventProcessor {
       pub fn new(
           service_name: String,
           alert_sender: tokio::sync::mpsc::Sender<SecurityAlert>,
       ) -> Self {
           Self {
               metrics: SecurityMetrics::new(service_name),
               alert_sender,
           }
       }

       pub async fn process_event(&self, event: FalcoEvent) {
           let severity = match event.priority.as_str() {
               "CRITICAL" => 4,
               "HIGH" => 3,
               "MEDIUM" => 2,
               "LOW" => 1,
               _ => 1,
           };

           // 记录异常
           self.metrics.record_anomaly(&event.rule, severity).await;

           // 创建安全告警
           let mut metadata = HashMap::new();
           for (key, value) in event.output_fields {
               metadata.insert(key, value.to_string());
           }

           let alert = SecurityAlert {
               alert_type: event.rule.clone(),
               severity: event.priority.clone(),
               message: event.output.clone(),
               service: self.metrics.service_name.clone(),
               timestamp: chrono::Utc::now(),
               metadata,
           };

           // 发送告警
           if let Err(e) = self.alert_sender.send(alert).await {
               tracing::error!("Failed to send security alert: {}", e);
           }

           // 特殊处理关键事件
           if severity >= 3 {
               self.handle_critical_event(&event).await;
           }
       }

       async fn handle_critical_event(&self, event: &FalcoEvent) {
           tracing::error!("CRITICAL SECURITY EVENT: {} - {}", event.rule, event.output);

           // 可以在这里实现自动响应措施
           // 例如：隔离容器、停止服务、发送紧急通知等
       }
   }
   ```

### 3.3 安全事件响应机制

**目标**: 自动化响应安全事件，最小化影响

**实施步骤**:
1. **事件响应服务**
   ```rust
   // services/security-responder/src/main.rs
   use axum::{extract::State, http::StatusCode, response::Json, routing::post, Router};
   use serde::{Deserialize, Serialize};
   use std::{collections::HashMap, sync::Arc, time::Duration};
   use tokio::sync::RwLock;
   use tracing::{error, info, warn};

   #[derive(Debug, Clone, Serialize, Deserialize)]
   pub enum SecurityEventType {
       AttestationFailure { service: String, details: String, consecutive_failures: u32 },
       UnexpectedBehavior { service: String, behavior: String, severity: u8 },
       ConfigurationTamper { config: String, change: String },
       CriticalVulnerability { service: String, vulnerability: String },
       UnauthorizedAccess { service: String, source: String },
       ResourceExhaustion { service: String, resource: String, usage: f64 },
   }

   #[derive(Debug, Clone, Serialize, Deserialize)]
   pub struct ResponseAction {
       pub action_type: String,
       pub parameters: HashMap<String, String>,
       pub timeout_seconds: u64,
       pub retry_count: u32,
   }

   #[derive(Debug, Clone, Serialize, Deserialize)]
   pub struct ResponsePolicy {
       pub severity: String,
       pub actions: Vec<ResponseAction>,
       pub escalation_threshold: u32,
       pub cooldown_minutes: u64,
   }

   #[derive(Debug, Clone)]
   pub struct SecurityIncident {
       pub id: String,
       pub event_type: SecurityEventType,
       pub timestamp: chrono::DateTime<chrono::Utc>,
       pub status: String,
       pub actions_taken: Vec<String>,
       pub escalated: bool,
   }

   pub struct SecurityResponder {
       policies: HashMap<String, ResponsePolicy>,
       active_incidents: Arc<RwLock<HashMap<String, SecurityIncident>>>,
       kubernetes_client: kube::Client,
       alert_channels: Vec<AlertChannel>,
   }

   #[derive(Debug, Clone)]
   pub enum AlertChannel {
       Slack { webhook_url: String },
       PagerDuty { integration_key: String },
       Email { recipients: Vec<String> },
       Webhook { url: String, headers: HashMap<String, String> },
   }

   impl SecurityResponder {
       pub async fn new() -> Result<Self, Box<dyn std::error::Error>> {
           let kubernetes_client = kube::Client::try_default().await?;

           // 从配置文件加载响应策略
           let policies = Self::load_response_policies().await?;

           // 配置告警渠道
           let alert_channels = Self::setup_alert_channels().await?;

           Ok(Self {
               policies,
               active_incidents: Arc::new(RwLock::new(HashMap::new())),
               kubernetes_client,
               alert_channels,
           })
       }

       pub async fn handle_security_event(&self, event: SecurityEventType) -> Result<String, Box<dyn std::error::Error>> {
           let incident_id = uuid::Uuid::new_v4().to_string();
           let event_key = self.get_event_key(&event);

           info!("Handling security event: {} (ID: {})", event_key, incident_id);

           // 创建安全事件记录
           let incident = SecurityIncident {
               id: incident_id.clone(),
               event_type: event.clone(),
               timestamp: chrono::Utc::now(),
               status: "active".to_string(),
               actions_taken: Vec::new(),
               escalated: false,
           };

           {
               let mut incidents = self.active_incidents.write().await;
               incidents.insert(incident_id.clone(), incident);
           }

           // 获取响应策略
           if let Some(policy) = self.policies.get(&event_key) {
               self.execute_response_policy(&incident_id, policy, &event).await?;
           } else {
               warn!("No response policy found for event type: {}", event_key);
               self.execute_default_response(&incident_id, &event).await?;
           }

           Ok(incident_id)
       }

       async fn execute_response_policy(
           &self,
           incident_id: &str,
           policy: &ResponsePolicy,
           event: &SecurityEventType,
       ) -> Result<(), Box<dyn std::error::Error>> {
           info!("Executing response policy for incident: {}", incident_id);

           for action in &policy.actions {
               match self.execute_action(incident_id, action, event).await {
                   Ok(_) => {
                       self.record_action_taken(incident_id, &action.action_type).await;
                       info!("Action {} completed for incident {}", action.action_type, incident_id);
                   }
                   Err(e) => {
                       error!("Action {} failed for incident {}: {}", action.action_type, incident_id, e);
                       // 继续执行其他动作，但记录失败
                       self.record_action_taken(incident_id, &format!("{}_FAILED", action.action_type)).await;
                   }
               }
           }

           // 发送告警通知
           self.send_alerts(incident_id, event, policy).await?;

           Ok(())
       }

       async fn execute_action(
           &self,
           incident_id: &str,
           action: &ResponseAction,
           event: &SecurityEventType,
       ) -> Result<(), Box<dyn std::error::Error>> {
           match action.action_type.as_str() {
               "isolate_service" => self.isolate_service(action, event).await,
               "scale_down_service" => self.scale_down_service(action, event).await,
               "restart_service" => self.restart_service(action, event).await,
               "block_traffic" => self.block_traffic(action, event).await,
               "increase_monitoring" => self.increase_monitoring(action, event).await,
               "create_network_policy" => self.create_network_policy(action, event).await,
               "trigger_backup" => self.trigger_backup(action, event).await,
               "rotate_secrets" => self.rotate_secrets(action, event).await,
               _ => {
                   warn!("Unknown action type: {}", action.action_type);
                   Ok(())
               }
           }
       }

       async fn isolate_service(&self, action: &ResponseAction, event: &SecurityEventType) -> Result<(), Box<dyn std::error::Error>> {
           let service_name = self.extract_service_name(event);
           info!("Isolating service: {}", service_name);

           // 创建网络策略以隔离服务
           let network_policy = serde_json::json!({
               "apiVersion": "networking.k8s.io/v1",
               "kind": "NetworkPolicy",
               "metadata": {
                   "name": format!("isolate-{}", service_name),
                   "namespace": action.parameters.get("namespace").unwrap_or(&"default".to_string())
               },
               "spec": {
                   "podSelector": {
                       "matchLabels": {
                           "app": service_name
                       }
                   },
                   "policyTypes": ["Ingress", "Egress"],
                   "ingress": [],
                   "egress": []
               }
           });

           // 应用网络策略
           let api: kube::Api<k8s_openapi::api::networking::v1::NetworkPolicy> =
               kube::Api::namespaced(self.kubernetes_client.clone(),
                   action.parameters.get("namespace").unwrap_or(&"default".to_string()));

           let network_policy: k8s_openapi::api::networking::v1::NetworkPolicy =
               serde_json::from_value(network_policy)?;

           api.create(&kube::api::PostParams::default(), &network_policy).await?;

           info!("Service {} isolated successfully", service_name);
           Ok(())
       }

       async fn scale_down_service(&self, action: &ResponseAction, event: &SecurityEventType) -> Result<(), Box<dyn std::error::Error>> {
           let service_name = self.extract_service_name(event);
           let namespace = action.parameters.get("namespace").unwrap_or(&"default".to_string());

           info!("Scaling down service: {} in namespace: {}", service_name, namespace);

           let api: kube::Api<k8s_openapi::api::apps::v1::Deployment> =
               kube::Api::namespaced(self.kubernetes_client.clone(), namespace);

           let patch = serde_json::json!({
               "spec": {
                   "replicas": 0
               }
           });

           api.patch(&service_name, &kube::api::PatchParams::default(), &kube::api::Patch::Merge(&patch)).await?;

           info!("Service {} scaled down successfully", service_name);
           Ok(())
       }

       async fn restart_service(&self, action: &ResponseAction, event: &SecurityEventType) -> Result<(), Box<dyn std::error::Error>> {
           let service_name = self.extract_service_name(event);
           let namespace = action.parameters.get("namespace").unwrap_or(&"default".to_string());

           info!("Restarting service: {} in namespace: {}", service_name, namespace);

           let api: kube::Api<k8s_openapi::api::apps::v1::Deployment> =
               kube::Api::namespaced(self.kubernetes_client.clone(), namespace);

           // 通过更新annotation来触发重启
           let patch = serde_json::json!({
               "spec": {
                   "template": {
                       "metadata": {
                           "annotations": {
                               "kubectl.kubernetes.io/restartedAt": chrono::Utc::now().to_rfc3339()
                           }
                       }
                   }
               }
           });

           api.patch(&service_name, &kube::api::PatchParams::default(), &kube::api::Patch::Merge(&patch)).await?;

           info!("Service {} restart triggered successfully", service_name);
           Ok(())
       }

       async fn increase_monitoring(&self, _action: &ResponseAction, event: &SecurityEventType) -> Result<(), Box<dyn std::error::Error>> {
           let service_name = self.extract_service_name(event);
           info!("Increasing monitoring for service: {}", service_name);

           // 这里可以实现增加监控频率的逻辑
           // 例如：更新Prometheus配置、增加日志级别等

           Ok(())
       }

       async fn block_traffic(&self, action: &ResponseAction, event: &SecurityEventType) -> Result<(), Box<dyn std::error::Error>> {
           info!("Blocking traffic based on security event");

           // 实现流量阻断逻辑
           // 例如：更新Istio配置、创建防火墙规则等

           Ok(())
       }

       async fn create_network_policy(&self, action: &ResponseAction, event: &SecurityEventType) -> Result<(), Box<dyn std::error::Error>> {
           info!("Creating network policy for security event");

           // 实现网络策略创建逻辑

           Ok(())
       }

       async fn trigger_backup(&self, action: &ResponseAction, event: &SecurityEventType) -> Result<(), Box<dyn std::error::Error>> {
           info!("Triggering backup for security event");

           // 实现备份触发逻辑

           Ok(())
       }

       async fn rotate_secrets(&self, action: &ResponseAction, event: &SecurityEventType) -> Result<(), Box<dyn std::error::Error>> {
           info!("Rotating secrets for security event");

           // 实现密钥轮换逻辑

           Ok(())
       }

       async fn send_alerts(&self, incident_id: &str, event: &SecurityEventType, policy: &ResponsePolicy) -> Result<(), Box<dyn std::error::Error>> {
           let alert_message = format!(
               "Security Incident: {} (ID: {})\nEvent: {:?}\nSeverity: {}",
               self.get_event_key(event),
               incident_id,
               event,
               policy.severity
           );

           for channel in &self.alert_channels {
               if let Err(e) = self.send_alert_to_channel(channel, &alert_message).await {
                   error!("Failed to send alert to channel: {}", e);
               }
           }

           Ok(())
       }

       async fn send_alert_to_channel(&self, channel: &AlertChannel, message: &str) -> Result<(), Box<dyn std::error::Error>> {
           match channel {
               AlertChannel::Slack { webhook_url } => {
                   let payload = serde_json::json!({
                       "text": message,
                       "username": "Security Responder",
                       "icon_emoji": ":warning:"
                   });

                   reqwest::Client::new()
                       .post(webhook_url)
                       .json(&payload)
                       .send()
                       .await?;
               }
               AlertChannel::Email { recipients } => {
                   // 实现邮件发送逻辑
                   info!("Sending email alert to: {:?}", recipients);
               }
               AlertChannel::Webhook { url, headers } => {
                   let mut request = reqwest::Client::new().post(url);
                   for (key, value) in headers {
                       request = request.header(key, value);
                   }
                   request.body(message.to_string()).send().await?;
               }
               AlertChannel::PagerDuty { integration_key } => {
                   // 实现PagerDuty集成
                   info!("Sending PagerDuty alert with key: {}", integration_key);
               }
           }

           Ok(())
       }

       fn extract_service_name(&self, event: &SecurityEventType) -> String {
           match event {
               SecurityEventType::AttestationFailure { service, .. } => service.clone(),
               SecurityEventType::UnexpectedBehavior { service, .. } => service.clone(),
               SecurityEventType::CriticalVulnerability { service, .. } => service.clone(),
               SecurityEventType::UnauthorizedAccess { service, .. } => service.clone(),
               SecurityEventType::ResourceExhaustion { service, .. } => service.clone(),
               _ => "unknown".to_string(),
           }
       }

       fn get_event_key(&self, event: &SecurityEventType) -> String {
           match event {
               SecurityEventType::AttestationFailure { .. } => "attestation_failure".to_string(),
               SecurityEventType::UnexpectedBehavior { .. } => "unexpected_behavior".to_string(),
               SecurityEventType::ConfigurationTamper { .. } => "configuration_tamper".to_string(),
               SecurityEventType::CriticalVulnerability { .. } => "critical_vulnerability".to_string(),
               SecurityEventType::UnauthorizedAccess { .. } => "unauthorized_access".to_string(),
               SecurityEventType::ResourceExhaustion { .. } => "resource_exhaustion".to_string(),
           }
       }

       async fn record_action_taken(&self, incident_id: &str, action: &str) {
           let mut incidents = self.active_incidents.write().await;
           if let Some(incident) = incidents.get_mut(incident_id) {
               incident.actions_taken.push(action.to_string());
           }
       }

       async fn execute_default_response(&self, incident_id: &str, event: &SecurityEventType) -> Result<(), Box<dyn std::error::Error>> {
           warn!("Executing default response for incident: {}", incident_id);

           // 默认响应：记录事件并发送通知
           let alert_message = format!("Unhandled security event: {:?}", event);

           for channel in &self.alert_channels {
               if let Err(e) = self.send_alert_to_channel(channel, &alert_message).await {
                   error!("Failed to send default alert: {}", e);
               }
           }

           Ok(())
       }

       async fn load_response_policies() -> Result<HashMap<String, ResponsePolicy>, Box<dyn std::error::Error>> {
           // 从配置文件或环境变量加载响应策略
           let mut policies = HashMap::new();

           // 示例策略配置
           policies.insert("attestation_failure".to_string(), ResponsePolicy {
               severity: "critical".to_string(),
               actions: vec![
                   ResponseAction {
                       action_type: "isolate_service".to_string(),
                       parameters: [("namespace".to_string(), "clique-wallet".to_string())].iter().cloned().collect(),
                       timeout_seconds: 60,
                       retry_count: 3,
                   },
                   ResponseAction {
                       action_type: "scale_down_service".to_string(),
                       parameters: [("namespace".to_string(), "clique-wallet".to_string())].iter().cloned().collect(),
                       timeout_seconds: 30,
                       retry_count: 2,
                   },
               ],
               escalation_threshold: 3,
               cooldown_minutes: 30,
           });

           Ok(policies)
       }

       async fn setup_alert_channels() -> Result<Vec<AlertChannel>, Box<dyn std::error::Error>> {
           let mut channels = Vec::new();

           // 从环境变量配置告警渠道
           if let Ok(slack_webhook) = std::env::var("SLACK_WEBHOOK_URL") {
               channels.push(AlertChannel::Slack { webhook_url: slack_webhook });
           }

           if let Ok(pagerduty_key) = std::env::var("PAGERDUTY_INTEGRATION_KEY") {
               channels.push(AlertChannel::PagerDuty { integration_key: pagerduty_key });
           }

           Ok(channels)
       }
   }

   // HTTP API端点
   async fn handle_security_event_endpoint(
       State(responder): State<Arc<SecurityResponder>>,
       Json(event): Json<SecurityEventType>,
   ) -> Result<Json<serde_json::Value>, StatusCode> {
       match responder.handle_security_event(event).await {
           Ok(incident_id) => Ok(Json(serde_json::json!({
               "status": "success",
               "incident_id": incident_id
           }))),
           Err(e) => {
               error!("Failed to handle security event: {}", e);
               Err(StatusCode::INTERNAL_SERVER_ERROR)
           }
       }
   }

   #[tokio::main]
   async fn main() -> Result<(), Box<dyn std::error::Error>> {
       tracing_subscriber::init();

       let responder = Arc::new(SecurityResponder::new().await?);

       let app = Router::new()
           .route("/security-event", post(handle_security_event_endpoint))
           .with_state(responder);

       let listener = tokio::net::TcpListener::bind("0.0.0.0:8080").await?;
       info!("Security responder service starting on port 8080");

       axum::serve(listener, app).await?;

       Ok(())
   }
   ```

2. **响应策略配置**
   ```yaml
   # k8s/security-responder/configmap.yaml
   apiVersion: v1
   kind: ConfigMap
   metadata:
     name: security-response-policies
     namespace: sgx-system
   data:
     policies.yaml: |
       policies:
         attestation_failure:
           severity: critical
           actions:
             - action_type: isolate_service
               parameters:
                 namespace: clique-wallet
               timeout_seconds: 60
               retry_count: 3
             - action_type: scale_down_service
               parameters:
                 namespace: clique-wallet
               timeout_seconds: 30
               retry_count: 2
             - action_type: rotate_secrets
               parameters:
                 secret_type: sgx_keys
               timeout_seconds: 120
               retry_count: 1
           escalation_threshold: 3
           cooldown_minutes: 30

         unexpected_behavior:
           severity: warning
           actions:
             - action_type: increase_monitoring
               parameters:
                 duration_minutes: "60"
               timeout_seconds: 10
               retry_count: 1
             - action_type: restart_service
               parameters:
                 namespace: clique-wallet
               timeout_seconds: 120
               retry_count: 2
           escalation_threshold: 5
           cooldown_minutes: 15

         critical_vulnerability:
           severity: critical
           actions:
             - action_type: isolate_service
               parameters:
                 namespace: clique-wallet
               timeout_seconds: 30
               retry_count: 1
             - action_type: trigger_backup
               parameters:
                 backup_type: emergency
               timeout_seconds: 300
               retry_count: 1
             - action_type: block_traffic
               parameters:
                 duration_minutes: "120"
               timeout_seconds: 60
               retry_count: 2
           escalation_threshold: 1
           cooldown_minutes: 60

         resource_exhaustion:
           severity: high
           actions:
             - action_type: scale_down_service
               parameters:
                 namespace: clique-wallet
                 replicas: "1"
               timeout_seconds: 60
               retry_count: 2
             - action_type: increase_monitoring
               parameters:
                 duration_minutes: "30"
               timeout_seconds: 10
               retry_count: 1
           escalation_threshold: 2
           cooldown_minutes: 20

   ---
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: security-responder
     namespace: sgx-system
   spec:
     replicas: 2
     selector:
       matchLabels:
         app: security-responder
     template:
       metadata:
         labels:
           app: security-responder
       spec:
         serviceAccountName: security-responder
         containers:
         - name: responder
           image: cliquesecure.azurecr.io/security-responder:latest
           ports:
           - containerPort: 8080
           env:
           - name: SLACK_WEBHOOK_URL
             valueFrom:
               secretKeyRef:
                 name: alert-secrets
                 key: slack-webhook-url
           - name: PAGERDUTY_INTEGRATION_KEY
             valueFrom:
               secretKeyRef:
                 name: alert-secrets
                 key: pagerduty-key
           volumeMounts:
           - name: policies
             mountPath: /app/policies.yaml
             subPath: policies.yaml
           resources:
             requests:
               cpu: 200m
               memory: 256Mi
             limits:
               cpu: 1000m
               memory: 1Gi
         volumes:
         - name: policies
           configMap:
             name: security-response-policies

   ---
   apiVersion: v1
   kind: ServiceAccount
   metadata:
     name: security-responder
     namespace: sgx-system

   ---
   apiVersion: rbac.authorization.k8s.io/v1
   kind: ClusterRole
   metadata:
     name: security-responder
   rules:
   - apiGroups: [""]
     resources: ["pods", "services", "secrets"]
     verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
   - apiGroups: ["apps"]
     resources: ["deployments", "replicasets"]
     verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
   - apiGroups: ["networking.k8s.io"]
     resources: ["networkpolicies"]
     verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
   - apiGroups: ["networking.istio.io"]
     resources: ["virtualservices", "destinationrules"]
     verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

   ---
   apiVersion: rbac.authorization.k8s.io/v1
   kind: ClusterRoleBinding
   metadata:
     name: security-responder
   roleRef:
     apiGroup: rbac.authorization.k8s.io
     kind: ClusterRole
     name: security-responder
   subjects:
   - kind: ServiceAccount
     name: security-responder
     namespace: sgx-system
   ```

## 实施时间线

### 第1周-第2周：专用构建环境准备
- [ ] 采购和配置专用构建机器
- [ ] 安装SGX驱动和Gramine环境
- [ ] 配置HSM和密钥管理系统
- [ ] 建立安全的网络隔离

### 第3周-第4周：MR_SIGNER认证体系
- [ ] 生成根CA和签名证书
- [ ] 实施信任管理服务
- [ ] 配置MR_SIGNER白名单
- [ ] 建立密钥轮换机制

### 第5周-第6周：敏感信息管理
- [ ] 部署分层密钥管理架构
- [ ] 实施密钥分发服务
- [ ] 配置HSM集成
- [ ] 建立密钥访问审计

### 第7周-第8周：客户端验证实现
- [ ] 开发SGX验证客户端库
- [ ] 实施服务端attestation接口
- [ ] 集成信任配置管理
- [ ] 开发多语言SDK

### 第9周-第10周：集成测试和部署
- [ ] 端到端验证测试
- [ ] 性能和安全测试
- [ ] 生产环境部署
- [ ] 监控和告警配置

### 第11周-第12周：文档和培训
- [ ] 完善技术文档
- [ ] 开发者培训材料
- [ ] 运维手册编写
- [ ] 安全审计报告

## 验证标准

### 专用构建环境验证
- [ ] 构建机器物理安全和网络隔离验证
- [ ] HSM密钥管理功能验证
- [ ] 构建产物完整性和可重现性验证
- [ ] 签名流程自动化验证

### MR_SIGNER认证验证
- [ ] 信任管理服务功能验证
- [ ] MR_SIGNER白名单管理验证
- [ ] 证书链验证功能验证
- [ ] 动态信任更新机制验证

### 敏感信息管理验证
- [ ] 分层密钥管理架构验证
- [ ] 密钥分发服务安全性验证
- [ ] HSM集成功能验证
- [ ] 密钥访问控制验证

### 客户端验证功能验证
- [ ] SGX应用attestation验证功能
- [ ] 多语言SDK功能验证
- [ ] 信任配置管理验证
- [ ] 安全通信建立验证

## 风险评估和缓解

### 实施风险
1. **专用构建机器风险**: 单点故障和维护复杂性
   - 缓解：建立备用构建环境，完善监控和自动化
2. **密钥管理风险**: HSM故障或密钥泄露
   - 缓解：多重备份，定期密钥轮换，访问审计
3. **信任链风险**: MR_SIGNER证书管理复杂性
   - 缓解：自动化证书管理，建立撤销机制
4. **客户端复杂性**: 验证逻辑复杂，集成困难
   - 缓解：提供易用SDK，完善文档和示例

### 安全收益
1. **构建安全**: 物理隔离的构建环境，防止供应链攻击
2. **身份认证**: 基于密码学的MR_SIGNER认证，确保代码来源可信
3. **密钥保护**: 分层密钥管理，HSM保护关键密钥
4. **端到端验证**: 客户端独立验证，不依赖第三方信任

## 关键实施要点

### 安全最佳实践
1. **物理安全**: 专用构建机器必须放置在安全的数据中心
2. **网络隔离**: 构建环境与生产环境完全隔离
3. **访问控制**: 实施最小权限原则，所有操作都有审计日志
4. **密钥轮换**: 定期轮换所有密钥，建立应急撤销机制

### 监控和审计
1. **构建监控**: 监控所有构建活动，异常行为告警
2. **密钥使用审计**: 记录所有密钥访问和使用情况
3. **客户端验证日志**: 记录所有验证请求和结果
4. **安全事件响应**: 建立完整的安全事件响应流程

### 性能优化
1. **缓存机制**: 客户端缓存验证结果，减少重复验证
2. **并行处理**: 构建和验证过程支持并行处理
3. **负载均衡**: 关键服务支持负载均衡和高可用
4. **资源监控**: 监控系统资源使用，及时扩容

## 总结

本方案提供了基于专用构建机器的SGX TEE应用安全部署解决方案，主要特点包括：

### 核心优势
1. **高度安全**: 物理隔离的构建环境，HSM保护的密钥管理
2. **可信认证**: 基于密码学的MR_SIGNER认证机制
3. **客户端验证**: 独立的客户端验证，不依赖第三方
4. **完整性保护**: 从构建到运行的完整信任链

### 适用场景
- 对安全要求极高的金融和支付应用
- 需要客户端独立验证的去中心化应用
- 涉及敏感数据处理的企业应用
- 需要合规认证的监管环境

### 实施建议
1. **分阶段实施**: 按照12周时间线逐步实施
2. **充分测试**: 每个阶段都要进行充分的安全测试
3. **团队培训**: 确保团队掌握SGX和密码学相关知识
4. **持续改进**: 根据实际使用情况持续优化方案

---

*本文档提供了完整的专用构建机器SGX安全部署方案。实施过程中如有问题，请参考相关技术文档或寻求专业支持。*

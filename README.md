Clique Wallet


## enclave_private_key

Generate SGX Enclave Private Key

Run the following commands on a machine with gramine installed.
```sh
gramine-sgx-gen-private-key ./enclave_private_key.pem
```

Or use docker to generate SGX Enclave Private Key
```sh
docker run --rm --entrypoint /usr/bin/sh stuart2024/gramine:1.7-jammy-patched-v2 -c "gramine-sgx-gen-private-key ./enclave_private_key.pem && cat enclave_private_key.pem"
```

## Test wallet service

Prepare the `.env` file
```sh
cp .env.example .env
# edit the .env file to set the correct values
```

Run the test
```sh
SGX=1 make test-service
```

## Test wallet signer

Prepare the `.env` file
```sh
cp .env.example .env
# edit the .env file to set the correct values
```

Run the test
```sh
SGX=1 make test-signer
```
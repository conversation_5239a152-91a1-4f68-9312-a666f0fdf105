# API Key 使用指南

## 概述

API Key 是 Clique Wallet 提供的程序化访问方式，允许开发者通过 API 接口安全地执行钱包操作。每个 API Key 都与特定用户账户关联，继承该用户的钱包权限和访问范围。

### 主要特性

- **安全认证**：基于 Bearer Token 的 HTTP 认证机制
- **权限继承**：API Key 具有创建用户的完整钱包权限
- **加密存储**：使用 AES-256-GCM 加密存储，确保密钥安全
- **灵活管理**：支持创建多个 API Key，可设置过期时间
- **审计追踪**：完整的使用记录和访问日志

### 适用场景

- 自动化交易系统
- 批量钱包操作
- 第三方应用集成
- 服务端钱包管理
- 定时任务和脚本

## 创建 API Key

### 前置条件

在创建 API Key 之前，需要：

1. 拥有有效的 Clique Wallet 账户
2. 已完成用户认证（登录状态）
3. 具有相应的钱包权限

### 创建步骤

#### 1. 发起创建请求

```http
POST https://wallet.test.superstack.xyz/api/keys
Content-Type: application/json
Cookie: session=your_session_cookie

{
  "name": "My Trading Bot",
  "expires_at": "2024-12-31T23:59:59Z"
}
```

#### 2. 请求参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `name` | string | 是 | API Key 的描述名称，便于识别和管理 |
| `expires_at` | string | 否 | 过期时间（ISO 8601 格式），不设置则永不过期 |

#### 3. 响应示例

```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "My Trading Bot",
  "api_key": "sk_550e8400e29b41d4a716************_Kx9mP2nQ7vR8sT1w",
  "created_at": "2024-01-15T10:30:00Z",
  "expires_at": "2024-12-31T23:59:59Z"
}
```

#### 4. 重要提醒

⚠️ **完整的 API Key 只在创建时显示一次**，请立即保存到安全位置。丢失后无法恢复，只能重新创建。

💡 **提示**：虽然完整密钥只显示一次，但你可以通过列表接口查看 API Key 的基本信息和前缀，用于识别和管理。

## API Key 格式和安全

### 格式说明

API Key 采用以下格式：
```
sk_{uuid_without_hyphens}_{random_16_chars}
```

- `sk_`：固定前缀，标识这是一个 Secret Key
- `uuid_without_hyphens`：32位UUID（去除连字符）
- `random_16_chars`：16位随机字符串

示例：`sk_550e8400e29b41d4a716************_Kx9mP2nQ7vR8sT1w`

### 安全特性

1. **加密存储**：服务端使用 AES-256-GCM 加密存储
2. **完整性验证**：包含数据完整性校验，防止篡改
3. **常数时间比较**：防止时序攻击
4. **O(1) 查询性能**：基于主键的高效验证

### 安全注意事项

- 将 API Key 存储在环境变量中，不要硬编码在代码里
- 使用 HTTPS 传输，避免明文传输
- 定期轮换 API Key，建议每 90 天更换一次
- 为不同用途创建不同的 API Key，便于权限管理
- 监控 API Key 使用情况，及时发现异常访问

## 使用 API Key 进行认证

### HTTP 认证方式

在所有 API 请求中，将 API Key 添加到 `Authorization` 头部：

```http
Authorization: Bearer sk_550e8400e29b41d4a716************_Kx9mP2nQ7vR8sT1w
```

### 认证流程

1. **提取 API Key**：从 `Authorization` 头部提取 Bearer Token
2. **格式验证**：验证 API Key 格式是否正确
3. **数据库查询**：基于 UUID 部分进行 O(1) 查询
4. **完整性验证**：解密并验证存储的完整数据
5. **权限检查**：验证 API Key 是否有效且未过期
6. **用户信息获取**：返回关联的用户信息和权限

### 示例请求

```bash
curl -X GET \
  https://wallet.test.superstack.xyz/api/keys \
  -H "Authorization: Bearer sk_550e8400e29b41d4a716************_Kx9mP2nQ7vR8sT1w" \
  -H "Content-Type: application/json"
```

## API 签名操作

### 签名接口

使用 API Key 进行消息签名是最常用的操作：

```http
POST https://wallet.test.superstack.xyz/api/sign
Authorization: Bearer sk_550e8400e29b41d4a716************_Kx9mP2nQ7vR8sT1w
Content-Type: application/json

{
  "address": "******************************************",
  "user_id": "ignored_in_api_mode",
  "message": "SGVsbG8gV29ybGQ=",
  "network": "Ethereum",
  "request_id": "req_123456789"
}
```

### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `address` | string | 是 | 要签名的钱包地址 |
| `user_id` | string | 否 | API 模式下忽略，从 API Key 获取用户信息 |
| `message` | string | 是 | 要签名的消息（Base58 编码） |
| `network` | string | 是 | 网络类型：`Ethereum` 或 `Solana` |
| `request_id` | string | 是 | 请求唯一标识符 |

### 响应示例

```json
{
  "signature": "0x1234567890abcdef..."
}
```

### 权限验证

签名操作会自动验证：
- API Key 关联的用户是否拥有指定地址的钱包
- 钱包地址与网络类型是否匹配
- 用户是否有权限操作该钱包

## 管理 API Key

### 查看 API Key 列表

```http
GET https://wallet.test.superstack.xyz/api/keys
Authorization: Bearer sk_550e8400e29b41d4a716************_Kx9mP2nQ7vR8sT1w
```

响应示例：
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "My Trading Bot",
    "key_prefix": "sk_550e****_MyTr****",
    "created_at": "2024-01-15T10:30:00Z",
    "expires_at": "2024-12-31T23:59:59Z",
    "is_active": true
  }
]
```

**key_prefix 格式说明**：
- `sk_` - 固定前缀
- `550e****` - UUID 的前4位 + 星号掩码
- `MyTr****` - API Key 名称的前4位 + 星号掩码

这种格式既保护了密钥安全，又便于用户识别和管理不同的 API Key。

### 删除 API Key

```http
DELETE https://wallet.test.superstack.xyz/api/keys/{key_id}
Authorization: Bearer sk_550e8400e29b41d4a716************_Kx9mP2nQ7vR8sT1w
```

删除成功返回 `204 No Content`。

### 最佳实践

1. **命名规范**：使用有意义的名称，如 "Production Trading Bot"、"Development Testing"
2. **定期清理**：删除不再使用的 API Key
3. **权限最小化**：为不同用途创建专用的 API Key
4. **监控使用**：定期检查 API Key 的使用情况
5. **及时轮换**：定期更新 API Key，特别是在安全事件后

## 错误处理

### 常见错误码

| 状态码 | 错误信息 | 原因 | 解决方案 |
|--------|----------|------|----------|
| 401 | Authentication failed | API Key 无效或格式错误 | 检查 API Key 格式和有效性 |
| 401 | Authentication failed | API Key 已过期 | 创建新的 API Key |
| 400 | API key name cannot be empty | 创建时未提供名称 | 提供有效的 API Key 名称 |
| 400 | Address mismatch | 签名地址不属于用户 | 确认地址归属和权限 |
| 404 | API key not found | 删除不存在的 API Key | 确认 API Key ID 正确 |
| 500 | Internal server error | 服务器内部错误 | 联系技术支持 |

### 错误响应格式

```json
{
  "error": "Authentication failed"
}
```

### 调试建议

1. **验证格式**：确保 API Key 格式正确，包含 `sk_` 前缀
2. **检查头部**：确认 `Authorization` 头部格式为 `Bearer {api_key}`
3. **验证权限**：确认 API Key 关联的用户有相应权限
4. **检查过期**：确认 API Key 未过期且处于活跃状态
5. **网络连接**：确保能正常访问 API 端点

## 安全建议

### 存储安全

```bash
# 推荐：使用环境变量
export CLIQUE_API_KEY="sk_550e8400e29b41d4a716************_Kx9mP2nQ7vR8sT1w"

# 在代码中使用
api_key = os.environ.get('CLIQUE_API_KEY')
```

### 传输安全

- 始终使用 HTTPS 协议
- 不要在 URL 参数中传递 API Key
- 避免在日志中记录完整的 API Key

### 访问控制

- 为不同环境（开发、测试、生产）使用不同的 API Key
- 定期审计 API Key 使用情况
- 在检测到异常活动时立即撤销相关 API Key

### 应急响应

如果怀疑 API Key 泄露：

1. 立即删除可能泄露的 API Key
2. 创建新的 API Key 替换
3. 检查相关账户的异常活动
4. 更新所有使用该 API Key 的系统和脚本

通过遵循这些安全建议，可以最大程度地保护您的 API Key 和钱包资产安全。

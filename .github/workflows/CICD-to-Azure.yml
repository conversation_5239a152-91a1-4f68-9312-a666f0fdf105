name: CICD-to-Azure
on:
  workflow_dispatch:
    inputs:
      env_name:
        required: true
        type: environment
        description: "name of the environment to deploy into"
      folder_name:
        required: true
        type: choice
        options:
          - clique-wallet-signer
          - clique-wallet-service
        description: "name of the folder to deploy"
      owner_name:
        required: true
        type: choice
        options:
          - clique
          - k4
          - yeezy
        description: "owner name to deploy for"
jobs:
  start:
    runs-on: ubuntu-latest
    outputs:
      app_name: ${{ steps.output-variable.outputs.app_name }}
    steps:
      - id: output-variable
        run: |
          echo "$GITHUB_INPUTS"
          folder_name="${{ inputs.folder_name }}"
          echo "app_name=${{inputs.owner_name}}-${folder_name#'clique-'}" >> "$GITHUB_OUTPUT"
        env:
          GITHUB_INPUTS: ${{ toJson(github.event.inputs) }}

  build-an-app:
    needs:
      - start
    environment: ${{ inputs.env_name }}
    timeout-minutes: 10
    runs-on: ubuntu-24.04-16c
    permissions:
      id-token: write
      contents: read
    steps:
      - run: |
          echo "${{ vars[format('ansible_vars_yaml__{0}', inputs.env_name)] }}" > /tmp/ansible_vars.yaml
          
          cat >> /tmp/ansible_vars.yaml << EOF
          image_name: "${{ needs.start.outputs.app_name }}"
          git_dir: "${{ github.workspace }}"
          git_ref: "${{ github.head_ref || github.ref_name }}"
          github_repo: "${{ github.repository }}"
          github_token: "${{ secrets.PAT }}"
          configuration_providers:
          - type: "OP"
            token: "${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}"
            repo: "key--${{ needs.start.outputs.app_name }}--${{ inputs.env_name }}"
          dockerfile_name: services/${{ inputs.folder_name }}/Dockerfile
          dockerbuild_dir: services/${{ inputs.folder_name }}/
          dockerbuild_arg: "--secret id=enclave-key,src=enclave-key.pem"
          pre_commands: |
            echo ${{ secrets.DOCKERHUB_TOKEN_PUBLIC_READONLY }} | docker login -u ${{ vars.DOCKERHUB_USERNAME }} --password-stdin
            docker pull stuart2024/gramine:1.7-jammy-patched-v2
          EOF
      - id: build_image
        uses: CliqueOfficial/clique-github-actions/.github/actions/build-an-app-to-acr@v1.0.1
        with:
          ansible_vars_yaml_file_path: /tmp/ansible_vars.yaml
          ansible_arg: ${{ runner.debug == '1' && '-vvv' || '' }}
    outputs:
      image_tag: ${{ steps.build_image.outputs.image_tag }}

  deploy-an-app:
    if: ${{ needs.build-an-app.result == 'success' }}
    needs:
      - start
      - build-an-app
    environment: ${{ inputs.env_name }}
    timeout-minutes: 10
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - run: |
          echo "${{ vars[format('ansible_vars_yaml__{0}', inputs.env_name)] }}" > /tmp/ansible_vars.yaml
          
          cat >> /tmp/ansible_vars.yaml << EOF
          image_name: "${{ needs.start.outputs.app_name }}"
          app_name: "${{ needs.start.outputs.app_name }}"
          app_namespace: "${{ needs.start.outputs.app_name }}"
          git_dir: "${{ github.workspace }}"
          git_ref: "${{ github.head_ref || github.ref_name }}"
          github_repo: "${{ github.repository }}"
          github_token: "${{ secrets.GITHUB_TOKEN }}"
          configuration_providers:
          - type: "OP"
            token: "${{ secrets.OP_SERVICE_ACCOUNT_TOKEN }}"
            repo: "app--${{ needs.start.outputs.app_name }}--${{ inputs.env_name }}"
          k8s_dir: "services/${{ inputs.folder_name }}/K8S/Azure"
          jinja2_dirs:
            - services/${{ inputs.folder_name }}/K8S/Azure
            - services/${{ inputs.folder_name }}/K8S/base
          image_tag: ${{ needs.build-an-app.outputs.image_tag }}
          jinja2_vars_yaml_file_path: jinja2_vars
          pre_commands: |
            cat .env >> services/${{ inputs.folder_name }}/K8S/base/.env
      - uses: CliqueOfficial/clique-github-actions/.github/actions/deploy-an-app-to-aks@v1.0.1
        with:
          ansible_vars_yaml_file_path: /tmp/ansible_vars.yaml
          ansible_arg: ${{ runner.debug == '1' && '-vvv' || '' }}

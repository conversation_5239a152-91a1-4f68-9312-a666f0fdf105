interface EnvConfig {
  WALLET_SERVICE_URL: string;
  SOLANA_RPC_URL: string;
}

export const env: EnvConfig = {
  WALLET_SERVICE_URL: import.meta.env.VITE_WALLET_SERVICE_URL || 'https://localhost:4000',
  SOLANA_RPC_URL: import.meta.env.VITE_SOLANA_RPC_URL || 'https://api.devnet.solana.com',
};

// Helper function to construct API endpoints
export const getWalletServiceEndpoint = (path: string): string => {
  return `${env.WALLET_SERVICE_URL}${path}`;
}; 
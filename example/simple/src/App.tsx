import React, { useState, useEffect } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Header from './components/Header';
import Home from './pages/Home';
import Transfer from './pages/Transfer';
import { WalletInfo } from './services/types';
import { getWalletServiceEndpoint } from './config/env';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  const [walletInfo, setWalletInfo] = useState<WalletInfo | null>(null);
  const [currentPage, setCurrentPage] = useState('home');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {

    const checkSession = async () => {
      try {
        const response = await fetch(getWalletServiceEndpoint('/session'), {
          credentials: 'include'
        });
        
        if (response.ok) {
          const data = await response.json();
          setWalletInfo(data);
        }
      } catch (error) {
        console.error('Session check error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkSession();
  }, []);

  const handleLogout = () => {
    fetch(getWalletServiceEndpoint('/logout'), {
      method: 'POST',
      credentials: 'include'
    }).finally(() => {
      setWalletInfo(null);
    });
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'home':
        return <Home walletInfo={walletInfo} onLogout={handleLogout} />;
      case 'transfer':
        return <Transfer walletInfo={walletInfo} />;
      default:
        return <Home walletInfo={walletInfo} onLogout={handleLogout} />;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen w-full bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <div className="min-h-screen w-full bg-gray-50 flex flex-col">
          <Header
            walletInfo={walletInfo}
            onLoginSuccess={setWalletInfo}
            onLogout={handleLogout}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
          />
          
          <main className="flex-1 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {renderPage()}
          </main>
        </div>
    </QueryClientProvider>
  );
}

export default App;

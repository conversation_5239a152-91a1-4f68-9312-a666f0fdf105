import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  Connection, 
  PublicKey, 
  LAMPORTS_PER_SOL, 
  SystemProgram,
  TransactionMessage,
  VersionedTransaction
} from '@solana/web3.js';
import bs58 from 'bs58';
import { env, getWalletServiceEndpoint } from '../config/env';
import { WalletInfo, SignRequest, SignResponse, TransactionHistory } from '../services/types';

interface TransferParams {
  walletInfo: WalletInfo;
  recipient: string;
  amount: string;
  onAddTransaction: (tx: TransactionHistory) => void;
  onUpdateTransaction: (signature: string, updates: Partial<TransactionHistory>) => void;
}

interface TransferContext {
  txSignature: string;
}

export const useTransferMutation = () => {
  const queryClient = useQueryClient();

  return useMutation<string, Error, TransferParams, TransferContext>({
    mutationFn: async ({ 
      walletInfo, 
      recipient, 
      amount,
      onAddTransaction,
      onUpdateTransaction
    }: TransferParams) => {
      // Validate recipient address
      const recipientPubKey = new PublicKey(recipient);
      const amountLamports = parseFloat(amount) * LAMPORTS_PER_SOL;
      const senderPubKey = new PublicKey(walletInfo.address);

      // Create a connection to the Solana network
      const connection = new Connection(env.SOLANA_RPC_URL, 'confirmed');

      // Get the latest blockhash
      const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash();

      // Create instruction to send SOL
      const transferInstruction = SystemProgram.transfer({
        fromPubkey: senderPubKey,
        toPubkey: recipientPubKey,
        lamports: amountLamports
      });

      // Create a versioned transaction message
      const message = new TransactionMessage({
        payerKey: senderPubKey,
        recentBlockhash: blockhash,
        instructions: [transferInstruction]
      }).compileToV0Message();

      // Create a versioned transaction

      // Serialize the message to base58
      const serializedMessage = bs58.encode(message.serialize());

      // Prepare sign request
      const signRequest: SignRequest = {
        address: walletInfo.address,
        userId: walletInfo.id,
        message: serializedMessage,
        network: 'Solana',
        requestId: Math.random().toString(36).substring(7) // Generate a random ID
      };

      // Send to sign endpoint
      const response = await fetch(getWalletServiceEndpoint('/sign'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(signRequest),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Sign request failed: ${response.statusText}`);
      }

      const signResponse: SignResponse = await response.json();
      const txSignature = signResponse.signature;

      // Decode the signature and add it to the transaction
      const signature = bs58.decode(txSignature);
      const transaction = new VersionedTransaction(message, [signature]);
      // Add transaction to history as pending
      onAddTransaction({
        signature: txSignature,
        timestamp: Date.now(),
        amount: parseFloat(amount),
        recipient: recipient,
        sender: walletInfo.address,
        status: 'pending'
      });

      // Send transaction
      await connection.sendRawTransaction(transaction.serialize());

      // Wait for confirmation
      const confirmation = await connection.confirmTransaction({
        signature: txSignature,
        blockhash: blockhash,
        lastValidBlockHeight: lastValidBlockHeight
      });

      if (confirmation.value.err) {
        throw new Error('Transaction failed to confirm');
      }

      // Update transaction status
      onUpdateTransaction(txSignature, { status: 'confirmed' });

      // Invalidate balance query to trigger a refresh
      queryClient.invalidateQueries({ queryKey: ['solanaBalance', walletInfo.address] });

      return txSignature;
    },
    onError: (error, variables, context) => {
      if (context?.txSignature) {
        variables.onUpdateTransaction(context.txSignature, {
          status: 'failed',
          error: error instanceof Error ? error.message : 'Failed to process transfer'
        });
      }
    }
  });
}; 
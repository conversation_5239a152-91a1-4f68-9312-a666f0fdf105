import { useQuery } from '@tanstack/react-query';
import { Connection, PublicKey, LAMPORTS_PER_SOL } from '@solana/web3.js';
import { env } from '../config/env';

const solanaConnection = new Connection(
  env.SOLANA_RPC_URL,
  'confirmed'
);

export const useSolanaBalance = (address: string | null) => {
  return useQuery({
    queryKey: ['solanaBalance', address],
    queryFn: async () => {
      if (!address) return null;
      
      try {
        const publicKey = new PublicKey(address);
        const balance = await solanaConnection.getBalance(publicKey);
        return balance / LAMPORTS_PER_SOL; // Convert lamports to SOL
      } catch (error) {
        console.error('Error fetching Solana balance:', error);
        throw error;
      }
    },
    enabled: !!address,
    refetchInterval: 10000, // Refetch every 10 seconds
  });
}; 
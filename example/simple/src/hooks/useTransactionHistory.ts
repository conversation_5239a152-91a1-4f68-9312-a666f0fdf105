import { useState, useEffect } from 'react';
import { TransactionHistory } from '../services/types';

const STORAGE_KEY = 'solana_transaction_history';

export const useTransactionHistory = (address: string | null) => {
  const [transactions, setTransactions] = useState<TransactionHistory[]>([]);

  useEffect(() => {
    if (!address) {
      setTransactions([]);
      return;
    }

    // Load transactions from localStorage
    const storedData = localStorage.getItem(STORAGE_KEY);
    if (storedData) {
      try {
        const allTransactions: Record<string, TransactionHistory[]> = JSON.parse(storedData);
        setTransactions(allTransactions[address] || []);
      } catch (error) {
        console.error('Error parsing transaction history:', error);
        setTransactions([]);
      }
    }
  }, [address]);

  const addTransaction = (transaction: TransactionHistory) => {
    if (!address) return;

    setTransactions(prev => {
      const newTransactions = [transaction, ...prev];
      
      // Save to localStorage
      const storedData = localStorage.getItem(STORAGE_KEY);
      const allTransactions: Record<string, TransactionHistory[]> = storedData ? JSON.parse(storedData) : {};
      allTransactions[address] = newTransactions;
      localStorage.setItem(STORAGE_KEY, JSON.stringify(allTransactions));
      
      return newTransactions;
    });
  };

  const updateTransaction = (signature: string, updates: Partial<TransactionHistory>) => {
    if (!address) return;

    setTransactions(prev => {
      const newTransactions = prev.map(tx => 
        tx.signature === signature ? { ...tx, ...updates } : tx
      );

      // Save to localStorage
      const storedData = localStorage.getItem(STORAGE_KEY);
      const allTransactions: Record<string, TransactionHistory[]> = storedData ? JSON.parse(storedData) : {};
      allTransactions[address] = newTransactions;
      localStorage.setItem(STORAGE_KEY, JSON.stringify(allTransactions));

      return newTransactions;
    });
  };

  return {
    transactions,
    addTransaction,
    updateTransaction
  };
}; 
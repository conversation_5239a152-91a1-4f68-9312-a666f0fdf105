import { useMutation } from '@tanstack/react-query';
import { getWalletServiceEndpoint } from '../config/env';
import { WalletInfo } from '../services/types';
import { getTwitterOAuth2Link } from '../utils/twitter';
import { getGoogleOAuth2Link } from '../utils/google';
import { buildCodeVerifierAndChallenge, buildOAuthState, retrieveAndClearCodeVerifierAndChallenge, retrieveAndClearState } from '../utils/oauth';
import { useEffect } from 'react';


const socialLogin = async (provider: string): Promise<void> => {
  console.log('provider', provider);

  provider = provider.toLowerCase();

  if (provider !== 'twitter' && provider !== 'google') {
    throw new Error('Unsupported provider');
  }

  const state = buildOAuthState(provider);
  const { codeVerifier, codeChallenge } = await buildCodeVerifierAndChallenge(provider);

  const authInitResponse = await fetch(getWalletServiceEndpoint('/oauth/init'), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      provider,
      redirect_uri: window.location.href,
      state,
      code_challenge: codeChallenge,
    }),
  });

  if (!authInitResponse.ok) {
    const errorData = await authInitResponse.json();
    console.error('Auth init error:', errorData);
    throw new Error(`Failed to login with ${provider}: ${authInitResponse.statusText}`);
  }

  const authInitData = await authInitResponse.json();
  console.log('authInitData', authInitData);

  let callbackUrl = authInitData.url;
  let clientId = authInitData.client_id;

  let redirectUrl;
  if (provider === 'twitter') {
    const oauth2Link = await getTwitterOAuth2Link({
      client_id: clientId,
      redirect_uri: callbackUrl,
      state,
      code_challenge: codeChallenge,
    });
    redirectUrl = oauth2Link.url;
  } else if (provider === 'google') {
    const oauth2Link = await getGoogleOAuth2Link({
      client_id: clientId,
      redirect_uri: callbackUrl,
      state,
      code_challenge: codeChallenge,
    });
    redirectUrl = oauth2Link.url;
  } else {
    throw new Error('Unsupported provider');
  }

  window.location.href = redirectUrl;
};

export const useSocialLogin = () => {
  const mutation = useMutation({
    mutationFn: socialLogin,
    onError: (error) => {
      console.error('Social login error:', error);
    },
  });

  useEffect(() => {

    const checkCallback = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      
      const walletOauthProvider = urlParams.get('wallet_oauth_provider');
      const walletOauthState = urlParams.get('wallet_oauth_state');
      const walletOauthCode = urlParams.get('wallet_oauth_code');

      if (walletOauthProvider && walletOauthState && walletOauthCode) {
        // Preserve existing query params except OAuth ones
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('wallet_oauth_provider');
        newUrl.searchParams.delete('wallet_oauth_state');
        newUrl.searchParams.delete('wallet_oauth_code');
        history.replaceState(null, '', newUrl.toString());

        const result = retrieveAndClearCodeVerifierAndChallenge(walletOauthProvider);
        if (!result) {
          throw new Error('No code verifier found');
        }
        const { codeVerifier, codeChallenge } = result;

        let type;
        if (walletOauthProvider === 'twitter') {
          type = 'TwitterOAuth';
        } else if (walletOauthProvider === 'google') {
          type = 'GoogleOAuth';
        } else {
          throw new Error('Unsupported provider');
        }

        const loginResponse = await fetch(getWalletServiceEndpoint('/login'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            type,
            data: {
              network: 'Solana',
              state: walletOauthState,
              code: walletOauthCode,
              code_verifier: codeVerifier,
            }
          }),
          credentials: 'include',
        });
      
        if (!loginResponse.ok) {
          const errorData = await loginResponse.json();
          console.error('Login error:', errorData);
          throw new Error(`Failed to login with ${walletOauthProvider}: ${loginResponse.statusText}`);
        }

        const user = await loginResponse.json();
        console.log('user', user);

        if (!user) {
          throw new Error('No user received from login');
        }
        
        window.location.reload();
      }
    };

    // Initial check
    checkCallback();

    // Add event listener for URL changes
    window.addEventListener('popstate', checkCallback);

    // Cleanup
    return () => {
      window.removeEventListener('popstate', checkCallback);
    };
  }, []);

  return {
    ...mutation,
  };
}; 
import axios, { AxiosResponse } from 'axios';
import * as qs from 'qs';
import { v4 as uuidv4 } from 'uuid';
import { getWalletServiceEndpoint } from '../config/env';


export class AuthLinkQueryParams {
    client_id!: string;
    redirect_uri!: string;
    state!: string;
    code_challenge!: string;
}

export class OAuthLinkRes {
  url!: string;
}

export async function getGoogleOAuth2Link(params: AuthLinkQueryParams): Promise<OAuthLinkRes> {
    const scope = ['openid', 'email', 'profile'];
    const queryParams: Record<string, any> = {
      response_type: 'code',
      client_id: params.client_id,
      redirect_uri: params.redirect_uri,
      scope: scope.join('%20'),
      code_challenge: params.code_challenge, //	A PKCE parameter, a random secret for each request you make.
      code_challenge_method: 'S256',
      state: params.state, // A random string you provide to verify against CSRF attacks.
    };

    const url = `https://accounts.google.com/o/oauth2/v2/auth?${qs.stringify(
      queryParams,
      {
        encode: false,
      },
    )}`;
    return { url };
  }

import { getWalletServiceEndpoint } from "../config/env";
import { v4 as uuidv4 } from 'uuid';
import { encode } from 'base64-encode-decode';


// Store state in sessionStorage
export function buildOAuthState(provider: string): string {
  const lowerProvider = provider.toLowerCase();
  const secureState = uuidv4();
  sessionStorage.setItem(`${lowerProvider}_oauthState`, JSON.stringify({
    state: secureState,
    timestamp: Date.now()
  }));
  return secureState;
}

export async function buildCodeVerifierAndChallenge(provider: string): Promise<{ codeVerifier: string, codeChallenge: string }> {
  const lowerProvider = provider.toLowerCase();
  const codeVerifier = uuidv4();
  const codeChallenge = await sha256(codeVerifier);
  sessionStorage.setItem(`${lowerProvider}_oauthCodeVerifier`, JSON.stringify({
    code_verifier: codeVerifier,
    code_challenge: codeChallenge,
    timestamp: Date.now()
  }));
  return { codeVerifier, codeChallenge };
}

export function retrieveAndClearCodeVerifierAndChallenge(provider: string): { codeVerifier: string, codeChallenge: string } | null {
  const lowerProvider = provider.toLowerCase();
  const storedCodeVerifier = sessionStorage.getItem(`${lowerProvider}_oauthCodeVerifier`);
  if (!storedCodeVerifier) return null;
  const parsedCodeVerifier = JSON.parse(storedCodeVerifier);
  sessionStorage.removeItem(`${lowerProvider}_oauthCodeVerifier`);

  // Optional: Add expiration check (e.g., 10 minutes)
  const isExpired = Date.now() - parsedCodeVerifier.timestamp > 10 * 60 * 1000;
  if (isExpired) {
      return null;
  }

  return { codeVerifier: parsedCodeVerifier.code_verifier, codeChallenge: parsedCodeVerifier.code_challenge };
}

export function retrieveAndClearState(provider: string): string | null {
  const lowerProvider = provider.toLowerCase();
  const storedState = sessionStorage.getItem(`${lowerProvider}_oauthState`);
  if (!storedState) return null;
    
  const parsedState = JSON.parse(storedState);
    
  sessionStorage.removeItem(`${lowerProvider}_oauthState`);

  // Optional: Add expiration check (e.g., 10 minutes)
  const isExpired = Date.now() - parsedState.timestamp > 10 * 60 * 1000;
  if (isExpired) {
      return null;
  }
  
  return parsedState.state;
}

async function sha256(input: string): Promise<string> {
  // Convert string to ArrayBuffer
  const encoder = new TextEncoder();
  const data = encoder.encode(input);
  
  // Hash with SHA-256
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  
  // Convert to Base64 URL-safe string
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashBase64 = btoa(String.fromCharCode(...hashArray));
  return hashBase64
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');
}
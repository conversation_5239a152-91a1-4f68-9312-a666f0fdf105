export interface WalletInfo {
  address: string;
  balance: string;
  network: string;
}

export interface SignRequest {
  address: string;
  userId: string;
  message: string;
  network: string;
  requestId: string;
}

export interface SignResponse {
  signature: string;
}

export interface TransactionHistory {
  signature: string;
  timestamp: number;
  amount: number;
  recipient: string;
  sender: string;
  status: 'confirmed' | 'failed' | 'pending';
  error?: string;
} 
import React from 'react';
import { WalletInfo } from '../services/types';
import { useSolanaBalance } from '../hooks/useSolanaBalance';

interface UserPanelProps {
  walletInfo: WalletInfo;
  onLogout: () => void;
}

const UserPanel: React.FC<UserPanelProps> = ({ walletInfo, onLogout }) => {
  const { data: balance, isLoading: isBalanceLoading, error: balanceError } = useSolanaBalance(walletInfo.address);

  return (
    <div className="bg-[#1a1a2f] rounded-2xl shadow-xl overflow-hidden ring-1 ring-white/10">
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-3xl"></div>
        <div className="relative p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-white/5 backdrop-blur-lg rounded-xl p-3 ring-1 ring-white/10">
                <svg
                  className="w-8 h-8 text-purple-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Wallet Balance</h3>
                {isBalanceLoading ? (
                  <div className="flex items-center space-x-2 text-purple-300">
                    <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                    </svg>
                    <span>Fetching balance...</span>
                  </div>
                ) : balanceError ? (
                  <p className="text-red-400">Error loading balance</p>
                ) : (
                  <p className="text-3xl font-bold text-white mt-1">
                    {balance?.toFixed(4)} <span className="text-purple-400">SOL</span>
                  </p>
                )}
              </div>
            </div>
            <button
              onClick={onLogout}
              className="bg-white/5 hover:bg-white/10 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 flex items-center space-x-2 backdrop-blur-lg ring-1 ring-white/10"
            >
              <svg className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              <span>Disconnect</span>
            </button>
          </div>
        </div>
      </div>
      
      <div className="p-6 bg-[#1a1a2f]/50">
        <div className="grid gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-2">Wallet Address</h4>
            <div className="flex items-center space-x-3 bg-white/5 backdrop-blur-lg rounded-lg p-3 break-all ring-1 ring-white/10">
              <svg className="w-5 h-5 text-purple-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              <span className="font-mono text-gray-300">{walletInfo.address}</span>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-2">Network</h4>
            <div className="flex items-center space-x-3 bg-white/5 backdrop-blur-lg rounded-lg p-3 ring-1 ring-white/10">
              <svg className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
              </svg>
              <span className="font-medium text-gray-300">{walletInfo.network}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserPanel; 
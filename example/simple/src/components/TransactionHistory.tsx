import React from 'react';
import { TransactionHistory as TxHistory } from '../services/types';

interface TransactionHistoryProps {
  transactions: TxHistory[];
}

const TransactionHistory: React.FC<TransactionHistoryProps> = ({ transactions }) => {
  if (transactions.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="bg-white/5 backdrop-blur-lg rounded-xl p-3 w-16 h-16 mx-auto mb-4 ring-1 ring-white/10">
          <svg className="w-10 h-10 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
        </div>
        <p className="text-gray-400">No transactions yet</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {transactions.map((tx) => (
        <div
          key={tx.signature}
          className="bg-white/5 backdrop-blur-lg rounded-lg p-4 ring-1 ring-white/10"
        >
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                tx.status === 'confirmed' ? 'bg-green-400' :
                tx.status === 'pending' ? 'bg-yellow-400' :
                'bg-red-400'
              }`} />
              <span className="text-sm font-medium text-gray-300">
                {new Date(tx.timestamp).toLocaleString()}
              </span>
            </div>
            <span className={`text-sm font-medium ${
              tx.status === 'confirmed' ? 'text-green-400' :
              tx.status === 'pending' ? 'text-yellow-400' :
              'text-red-400'
            }`}>
              {tx.status.charAt(0).toUpperCase() + tx.status.slice(1)}
            </span>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-400">To</p>
              <p className="font-mono text-sm text-gray-300 truncate max-w-[200px]">
                {tx.recipient}
              </p>
            </div>
            <div className="text-right">
              <p className="text-lg font-bold text-white">
                {tx.amount.toFixed(4)} <span className="text-purple-400">SOL</span>
              </p>
            </div>
          </div>

          {tx.error && (
            <div className="mt-2 text-sm text-red-400">
              {tx.error}
            </div>
          )}

          <div className="mt-2 pt-2 border-t border-white/10">
            <a
              href={`https://explorer.solana.com/tx/${tx.signature}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-purple-400 hover:text-purple-300 transition-colors flex items-center space-x-1"
            >
              <span>View on Explorer</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
              </svg>
            </a>
          </div>
        </div>
      ))}
    </div>
  );
};

export default TransactionHistory; 
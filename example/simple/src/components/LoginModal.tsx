import React, { useEffect, useState } from 'react';
import { useSocialLogin } from '../hooks/useSocialLogin';
import { WalletInfo } from '../services/types';
import { useMutation } from '@tanstack/react-query';
import { getWalletServiceEndpoint } from '../config/env';


interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  onLoginSuccess: (info: WalletInfo) => void;
}

const LoginModal: React.FC<LoginModalProps> = ({ isOpen, onClose, onLoginSuccess }) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const { mutate: login, isPending, error: mutationError } = useSocialLogin();
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [currentStep, setCurrentStep] = useState<'email' | 'code'>('email');
  const [emailError, setEmailError] = useState('');

  const { mutate: sendVerificationCode, isPending: isSendingCode } = useMutation({
    mutationFn: async (email: string) => {
      const response = await fetch(getWalletServiceEndpoint('/send_verification'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      if (!response.ok) {
        throw new Error('Failed to send verification code');
      }
    },
    onSuccess: () => {
      setCurrentStep('code');
      setEmailError('');
    },
    onError: () => {
      setEmailError('Failed to send verification code. Please try again.');
    },
  });

  const { mutate: verifyCode, isPending: isVerifyingCode } = useMutation({
    mutationFn: async ({ email, code }: { email: string; code: string }) => {
      const response = await fetch(getWalletServiceEndpoint('/login'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'Email',
          data: {
            network: 'Solana',
            email,
            code,
          },
        }),
        credentials: 'include',
      });
      if (!response.ok) {
        throw new Error('Invalid verification code');
      }

      const responseData = await response.json();
      console.log('Login response:', responseData);
      return responseData;
    },
    onSuccess: (data) => {
      onLoginSuccess(data);
      onClose();
    },
    onError: () => {
      setEmailError('Invalid verification code. Please try again.');
    },
  });

  useEffect(() => {
    if (isOpen) {
      setIsAnimating(true);
      setEmail('');
      setCode('');
      setCurrentStep('email');
      setEmailError('');
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const socialLogins = [
    { name: 'Google', bgColor: 'bg-red-500', hoverColor: 'hover:bg-red-600' },
    { name: 'Facebook', bgColor: 'bg-blue-600', hoverColor: 'hover:bg-blue-700' },
    { name: 'Twitter', bgColor: 'bg-sky-500', hoverColor: 'hover:bg-sky-600' },
    { name: 'GitHub', bgColor: 'bg-gray-800', hoverColor: 'hover:bg-gray-900' },
  ];

  const handleSocialLogin = (provider: string) => {
    login(provider);
  };

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      return;
    }
    sendVerificationCode(email);
  };

  const handleCodeSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    verifyCode({ email, code });
  };

  const validateEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  return (
    <div 
      className={`fixed inset-0 bg-black transition-opacity duration-300 flex items-center justify-center z-50 ${
        isAnimating ? 'bg-opacity-50' : 'bg-opacity-0'
      }`}
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <div 
        className={`bg-white rounded-lg p-8 w-full max-w-md transform transition-all duration-300 ${
          isAnimating ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4'
        }`}
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">Login</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            disabled={isPending}
          >
            <svg
              className="w-6 h-6"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>

        {mutationError && (
          <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-lg">
            {mutationError instanceof Error ? mutationError.message : 'Failed to login'}
          </div>
        )}

        <div className="space-y-4">
        {currentStep === 'email' ? (
            <form onSubmit={handleEmailSubmit} className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                  disabled={isSendingCode}
                />
              </div>
              {emailError && (
                <div className="text-sm text-red-600">{emailError}</div>
              )}
              <button
                type="submit"
                className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 disabled:opacity-50"
                disabled={isSendingCode}
              >
                {isSendingCode ? 'Sending...' : 'Send Verification Code'}
              </button>
            </form>
          ) : (
            <form onSubmit={handleCodeSubmit} className="space-y-4">
              <div>
                <label htmlFor="code" className="block text-sm font-medium text-gray-700">
                  Verification Code
                </label>
                <input
                  type="text"
                  id="code"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                  required
                  disabled={isVerifyingCode}
                />
              </div>
              {emailError && (
                <div className="text-sm text-red-600">{emailError}</div>
              )}
              <button
                type="submit"
                className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 disabled:opacity-50"
                disabled={isVerifyingCode}
              >
                {isVerifyingCode ? 'Verifying...' : 'Verify Code'}
              </button>
              <button
                type="button"
                onClick={() => setCurrentStep('email')}
                className="w-full text-indigo-600 hover:text-indigo-500 text-sm"
              >
                Back to Email Input
              </button>
            </form>
          )}


          {socialLogins.map((provider, index) => (
            <button
              key={provider.name}
              className={`w-full ${provider.bgColor} ${provider.hoverColor} text-white font-semibold py-3 px-4 rounded-lg transition duration-300 flex items-center justify-center space-x-2 transform hover:scale-102 disabled:opacity-50 disabled:cursor-not-allowed`}
              onClick={() => handleSocialLogin(provider.name)}
              disabled={isPending}
              style={{
                transitionDelay: `${index * 50}ms`,
                opacity: isAnimating ? 1 : 0,
                transform: isAnimating ? 'translateY(0)' : 'translateY(20px)',
              }}
            >
              {isPending && (
                <svg className="animate-spin h-5 w-5 mr-2" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
              )}
              <span>Continue with {provider.name}</span>
            </button>
          ))}
        </div>

        <div 
          className="mt-6 text-center text-sm text-gray-500"
          style={{
            transitionDelay: '200ms',
            opacity: isAnimating ? 1 : 0,
          }}
        >
          By continuing, you agree to our
          <a href="#" className="text-blue-500 hover:text-blue-600 ml-1">
            Terms of Service
          </a>
          {' and '}
          <a href="#" className="text-blue-500 hover:text-blue-600">
            Privacy Policy
          </a>
        </div>
      </div>
    </div>
  );
};

export default LoginModal; 
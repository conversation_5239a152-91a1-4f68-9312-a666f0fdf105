import React from 'react';
import { WalletInfo } from '../services/types';
import UserPanel from '../components/UserPanel';
import TransactionHistory from '../components/TransactionHistory';
import { useTransactionHistory } from '../hooks/useTransactionHistory';

interface HomeProps {
  walletInfo: WalletInfo | null;
  onLogout: () => void;
}

const Home: React.FC<HomeProps> = ({ walletInfo, onLogout }) => {
  const { transactions } = useTransactionHistory(walletInfo?.address ?? null);

  if (!walletInfo) {
    return (
      <div className="text-center py-12">
        <div className="bg-white/5 backdrop-blur-lg rounded-xl p-3 w-16 h-16 mx-auto mb-6 ring-1 ring-white/10">
          <svg className="w-10 h-10 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-white mb-2">Connect Your Wallet</h2>
        <p className="text-gray-400">Please connect your wallet to view your information.</p>
      </div>
    );
  }

  return (
    <div className="grid gap-6">
      <UserPanel walletInfo={walletInfo} onLogout={onLogout} />
      
      <div className="bg-[#1a1a2f] rounded-2xl shadow-xl overflow-hidden ring-1 ring-white/10">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-3xl"></div>
          <div className="relative p-6">
            <h2 className="text-2xl font-bold text-white mb-2">Transaction History</h2>
            <p className="text-purple-300">Your recent transactions</p>
          </div>
        </div>
        <div className="p-6 bg-[#1a1a2f]/50">
          <TransactionHistory transactions={transactions} />
        </div>
      </div>
    </div>
  );
};

export default Home; 
import React, { useState } from 'react';
import { WalletInfo } from '../services/types';
import { useSolanaBalance } from '../hooks/useSolanaBalance';
import { useTransactionHistory } from '../hooks/useTransactionHistory';
import { useTransferMutation } from '../hooks/useTransferMutation';

interface TransferProps {
  walletInfo: WalletInfo | null;
}

const Transfer: React.FC<TransferProps> = ({ walletInfo }) => {
  const [recipient, setRecipient] = useState('');
  const [amount, setAmount] = useState('');

  const { data: balance } = useSolanaBalance(walletInfo?.address ?? null);
  const { addTransaction, updateTransaction } = useTransactionHistory(walletInfo?.address ?? null);
  const transferMutation = useTransferMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!walletInfo) return;

    transferMutation.mutate(
      {
        walletInfo,
        recipient,
        amount,
        onAddTransaction: addTransaction,
        onUpdateTransaction: updateTransaction
      },
      {
        onSuccess: () => {
          // Clear form on success
          setRecipient('');
          setAmount('');
        }
      }
    );
  };

  if (!walletInfo) {
    return (
      <div className="text-center py-12">
        <div className="bg-white/5 backdrop-blur-lg rounded-xl p-3 w-16 h-16 mx-auto mb-6 ring-1 ring-white/10">
          <svg className="w-10 h-10 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-white mb-2">Connect Your Wallet</h2>
        <p className="text-gray-400">You need to connect your wallet to make transfers.</p>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-[#1a1a2f] rounded-2xl shadow-xl overflow-hidden ring-1 ring-white/10">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-pink-600/20 backdrop-blur-3xl"></div>
          <div className="relative p-6">
            <h2 className="text-2xl font-bold text-white mb-2">Transfer SOL</h2>
            <p className="text-purple-300">Send SOL to another wallet address</p>
          </div>
        </div>
        
        <div className="p-6 bg-[#1a1a2f]/50">
          {balance !== undefined && (
            <div className="mb-6 bg-white/5 backdrop-blur-lg rounded-lg p-4 flex items-center justify-between ring-1 ring-white/10">
              <div>
                <p className="text-sm font-medium text-gray-400">Available Balance</p>
                <p className="text-2xl font-bold text-white mt-1">
                  {balance?.toFixed(4)} <span className="text-purple-400">SOL</span>
                </p>
              </div>
              <svg className="w-12 h-12 text-purple-400/20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="recipient" className="block text-sm font-medium text-gray-400 mb-2">
                Recipient Address
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
                <input
                  id="recipient"
                  type="text"
                  value={recipient}
                  onChange={(e) => setRecipient(e.target.value)}
                  className="block w-full pl-10 pr-4 py-3 bg-white/5 border-0 rounded-lg focus:ring-2 focus:ring-purple-500 font-mono text-white backdrop-blur-lg ring-1 ring-white/10"
                  placeholder="Enter Solana address"
                  required
                  disabled={transferMutation.isPending}
                />
              </div>
            </div>

            <div>
              <label htmlFor="amount" className="block text-sm font-medium text-gray-400 mb-2">
                Amount (SOL)
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-purple-400">◎</span>
                </div>
                <input
                  id="amount"
                  type="number"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="block w-full pl-10 pr-4 py-3 bg-white/5 border-0 rounded-lg focus:ring-2 focus:ring-purple-500 text-white backdrop-blur-lg ring-1 ring-white/10"
                  placeholder="0.0"
                  step="0.000001"
                  min="0"
                  required
                  disabled={transferMutation.isPending}
                />
              </div>
            </div>

            {transferMutation.error && (
              <div className="p-4 bg-red-900/10 backdrop-blur-lg rounded-lg flex items-center space-x-3 ring-1 ring-red-900/30">
                <svg className="w-5 h-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-sm text-red-400">
                  {transferMutation.error instanceof Error 
                    ? transferMutation.error.message 
                    : 'Failed to process transfer'}
                </p>
              </div>
            )}

            {transferMutation.data && (
              <div className="p-4 bg-green-900/10 backdrop-blur-lg rounded-lg flex items-center space-x-3 ring-1 ring-green-900/30">
                <svg className="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <p className="text-sm text-green-400">
                  Transaction confirmed! Signature: {transferMutation.data}
                </p>
              </div>
            )}

            <button
              type="submit"
              disabled={transferMutation.isPending}
              className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-200 hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 shadow-lg shadow-purple-500/25"
            >
              {transferMutation.isPending ? (
                <>
                  <svg className="animate-spin h-5 w-5" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  <span>{transferMutation.variables?.amount ? 'Confirming...' : 'Processing...'}</span>
                </>
              ) : (
                <>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Send SOL</span>
                </>
              )}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Transfer; 
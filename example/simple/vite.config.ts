import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import basicSsl from '@vitejs/plugin-basic-ssl'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), basicSsl()],
  server: {
    allowedHosts: ["localhost", ".clique.tech"],
  },
  define: {
    'global': {},
    'process.env': {}
  },
  resolve: {
    alias: {
      'stream': 'stream-browserify',
      'buffer': 'buffer'
    }
  },
  optimizeDeps: {
    esbuildOptions: {
      define: {
        global: 'globalThis'
      }
    }
  }
})

{"name": "simple", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@nestjs/swagger": "^11.0.5", "@solana/web3.js": "^1.98.0", "@tanstack/react-query": "^5.66.9", "@vitejs/plugin-basic-ssl": "^1.2.0", "axios": "^1.7.9", "base64-encode-decode": "^0.0.3", "bs58": "^6.0.0", "buffer": "^6.0.3", "qs": "^6.14.0", "react": "^19.0.0", "react-dom": "^19.0.0", "stream-browserify": "^3.0.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@tailwindcss/postcss": "^4.0.8", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "10.4.17", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "8.4.35", "tailwindcss": "3.4.1", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}
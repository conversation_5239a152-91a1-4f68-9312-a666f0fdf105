apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: {{ app_name }}
  namespace: {{ app_name }}
spec:
  gateways:
    - istio-ingress/istio-ingressgateway
  hosts:
    - {{ app_name }}.{{ app_name }}.svc.cluster.local
  http:
    - route:
        - destination:
            host: {{ app_name }}
            port:
              number: 443
            subset: insecureSkipVerify
      headers:
        response:
          remove:
          - Access-Control-Allow-Headers
          - Access-Control-Allow-Methods
          - Access-Control-Allow-Origin
          - Access-Control-Expose-Headers
      corsPolicy:
        allowCredentials: false
        allowHeaders:
        - '*'
        allowMethods:
        - GET
        - POST
        - PUT
        - DELETE
        allowOrigins:
        - regex: ^(http|https)://({{ allow_hosts }})$
        exposeHeaders:
        - '*'
        maxAge: 24h

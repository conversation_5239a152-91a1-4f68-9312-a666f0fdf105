apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ app_name }}
  annotations:
    reloader.stakater.com/auto: "true"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ app_name }}
  template:
    metadata:
      labels:
        app: {{ app_name }}
    spec:
      tolerations:
      - key: "kubernetes.azure.com/scalesetpriority"
        operator: "Equal"
        value: "spot"
        effect: "NoSchedule"
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: SGX
                operator: In
                values:
                - 'TRUE'
      priorityClassName: business-app-critical
      containers:
        - name: {{ app_name }}
          image: {{ image_uri }}
          imagePullPolicy: Always
          ports:
          - name: https
            containerPort: 3000
          - name: unchecked
            containerPort: 3003
          envFrom:
          - secretRef:
              name: dotenv
          resources:
            requests:
              cpu: 1
              memory: 1G
              sgx.intel.com/epc: "1G"
              sgx.intel.com/enclave: '1'
              sgx.intel.com/provision: '1'
            limits:
              cpu: 8
              memory: 8G
              sgx.intel.com/epc: "1G"
              sgx.intel.com/enclave: '1'
              sgx.intel.com/provision: '1'
          livenessProbe:
            initialDelaySeconds: 60
            timeoutSeconds: 5
            httpGet:
              port: https
              path: /healthz
              scheme: HTTPS
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 1
          readinessProbe:
            initialDelaySeconds: 10
            timeoutSeconds: 5
            httpGet:
              port: https
              path: /healthz
              scheme: HTTPS
            periodSeconds: 5
            successThreshold: 1
            failureThreshold: 1


ARCH_LIBDIR ?= /lib/$(shell $(CC) -dumpmachine)

SELF_EXE = target/release/clique-wallet-service
SRC = $(wildcard src/*.rs) $(wildcard src/**/*.rs) ./Cargo.toml

.PHONY: all
all: $(SELF_EXE) service.manifest
ifeq ($(SGX),1)
all: service.manifest.sgx service.sig
endif

ifeq ($(DEBUG),1)
GRAMINE_LOG_LEVEL = debug
else
GRAMINE_LOG_LEVEL = error
endif

# Note that we're compiling in release mode regardless of the DEBUG setting passed
# to Make, as compiling in debug mode results in an order of magnitude's difference in
# performance that makes testing by running a benchmark with ab painful. The primary goal
# of the DEBUG setting is to control Gramine's loglevel.
-include $(SELF_EXE).d # See also: .cargo/config.toml
$(SELF_EXE): $(SRC)
	cargo build --release

# Default to 'dev' if not specified
ENV ?= dev

.PHONY: service.manifest
service.manifest: service.manifest.template.$(ENV)
	gramine-manifest \
		-Dlog_level=$(GRAMINE_LOG_LEVEL) \
		-Darch_libdir=$(ARCH_LIBDIR) \
		-Dself_exe=$(SELF_EXE) \
		-Duser_path=$(HOME) \
		$< $@

# Make on Ubuntu <= 20.04 doesn't support "Rules with Grouped Targets" (`&:`),
# see the helloworld example for details on this workaround.
service.manifest.sgx service.sig: sgx_sign
	@:

.INTERMEDIATE: sgx_sign
sgx_sign: service.manifest $(SELF_EXE)
	gramine-sgx-sign \
		--manifest $< \
		--output $<.sgx

ifeq ($(SGX),)
GRAMINE = gramine-direct
else
GRAMINE = gramine-sgx
endif

.PHONY: start-service
start-service: all
	$(GRAMINE) service

.PHONY: test-service
test-service: all
	bash -c 'set -a && source .env && $(GRAMINE) service'

.PHONY: clean
clean:
	$(RM) -rf *.sig *.manifest.sgx *.manifest result-* OUTPUT

.PHONY: distclean
distclean: clean
	$(RM) -rf target/

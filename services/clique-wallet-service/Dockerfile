FROM stuart2024/gramine:1.7-jammy-patched-v2 AS builder

COPY . /clique-wallet-service

RUN apt update && apt install -y libclang-dev

ENV PATH="/root/.cargo/bin:${PATH}"

# ENV Cargo enable net.git-fetch-with-cli
ENV CARGO_NET_GIT_FETCH_WITH_CLI=true

ENV SQLX_OFFLINE=true

RUN --mount=type=secret,id=gitconfig,target=/root/.gitconfig \
    --mount=type=secret,id=enclave-key,target=/root/.config/gramine/enclave-key.pem \
    bash -c "cd /clique-wallet-service && make clean && make SGX=1"

RUN mkdir -p /compiled/target/release
RUN cp /clique-wallet-service/service.manifest /compiled
RUN cp /clique-wallet-service/service.manifest.sgx /compiled
RUN cp /clique-wallet-service/service.sig /compiled
RUN cp /clique-wallet-service/target/release/clique-wallet-service /compiled/target/release
RUN cp /clique-wallet-service/ca-certificates.crt /compiled
RUN cp -r /clique-wallet-service/migrations /compiled
RUN cp -r /clique-wallet-service/templates /compiled

RUN gramine-sgx-sigstruct-view /compiled/service.sig

# TODO: use a light docker image
FROM stuart2024/gramine:1.7-jammy-patched-v2

COPY --from=builder /compiled /clique-wallet-service

RUN echo "#!/bin/bash" > /run.sh

RUN echo "/restart_aesm.sh && cd /clique-wallet-service && gramine-sgx-sigstruct-view service.sig && gramine-sgx service" >> /run.sh
RUN chmod +x /run.sh
ENTRYPOINT ["/run.sh"]

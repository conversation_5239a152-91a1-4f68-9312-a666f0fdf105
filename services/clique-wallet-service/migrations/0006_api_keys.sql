-- API Keys table for storing user API keys with encrypted storage
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users2(id) ON DELETE CASCADE,
    key_ciphertext BYTEA NOT NULL, -- AES-256-GCM encrypted API key
    key_nonce BYTEA NOT NULL CHECK (LENGTH(key_nonce) = 12), -- AES-GCM nonce (12 bytes)
    name TEXT NOT NULL, -- User-friendly name for the API key
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    expires_at TIMESTAMPTZ, -- NULL means never expires
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Indexes for api_keys table
CREATE INDEX idx_api_keys_user_id ON api_keys (user_id);
CREATE INDEX idx_api_keys_user_id_active ON api_keys (user_id, is_active);
CREATE INDEX idx_api_keys_active ON api_keys (is_active);
CREATE INDEX idx_api_keys_expires_at ON api_keys (expires_at);

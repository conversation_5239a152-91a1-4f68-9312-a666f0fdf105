CREATE TABLE users2 (
    id UUID PRIMARY KEY,
    wallets J<PERSON>NB NOT NULL,
    social_links JSONB NOT NULL,
    ciphertext BYTEA NOT NULL,
    nonce BYTEA NOT NULL CHECK (LENGTH(nonce) = 12)
);

CREATE INDEX idx_users2_wallets ON users2 USING GIN (wallets);
CREATE INDEX idx_users2_social ON users2 USING GIN (social_links);

CREATE TABLE user_social_links (
    user_id UUID REFERENCES users2(id) ON DELETE CASCADE,
    social_link TEXT NOT NULL UNIQUE,
    PRIMARY KEY (user_id, social_link)
);
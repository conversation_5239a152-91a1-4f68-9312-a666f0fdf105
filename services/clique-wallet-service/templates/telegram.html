<!DOCTYPE html>
<html lang='en'>
<head>
  <meta charset='UTF-8'>
  <title>{title}</title>
  <style>
    body {
      background-color: #ddd;
      margin: 0;
      padding: 0;
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    .box {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 100px;
    }
  </style>
</head>
<body>
  <h2>Telegram Authorization</h2>
  <div class='box'>
    <script async src="https://telegram.org/js/telegram-widget.js?22" data-telegram-login="{bot_name}"
      data-size="large" data-auth-url="{callback_url}" data-request-access="write">
    </script>
  </div>
</body>
</html>
[package]
name = "clique-wallet-service"
version = "0.1.0"
edition = "2024"

[dependencies]
axum = { version = "0.8.1" }
axum-server = { version = "0.7.1", features = ["tls-rustls"] }
bs58 = "0.5.1"
eyre = "0.6.12"
reqwest = { version = "0.12.12", features = ["json", "rustls-tls", "blocking"] }
serde = { version = "1.0.218", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
uuid = { version = "1.14.0", features = ["v4", "serde"] }
tower-http = { version = "0.6", features = ["cors"] }
http = "1.0"
rand = "0.8"
tower-sessions = { version = "0.14.0", features = ["private"] }
tower-sessions-core = "0.14.0"
time = "0.3.37"
hex = "0.4.3"
k256 = { version = "0.13.4", features = ["ecdh", "ecdsa"] }
sqlx = { version = "0.8", features = ["postgres", "runtime-tokio", "tls-rustls-aws-lc-rs", "migrate", "uuid", "time"] }
urlencoding = "2.1.3"
aes-gcm = "0.10.3"
pbkdf2 = "0.12.2"
bincode = "1.3.3"
async-trait = "0.1"
base64 = "0.22.1"
hmac = "0.12.1"
chrono = "0.4.40"
jsonwebtoken = { git = "https://github.com/Keats/jsonwebtoken.git", rev = "5cd1887848f7d30f11adb4ebcec6a01baffd5252" }
email_address = "0.2.9"

solana-sdk = "=2.2.1"

# Clique
clique-sibyl-commonlib = { git = "https://github.com/CliqueOfficial/clique-sibyl-commonlib.git", tag = "v2.5.2", features = ["rustls-0_23"] }

clique-wallet-signer-types = { path = "../clique-wallet-signer-types" }

[dev-dependencies]
dotenv = "0.15.0"


[features]
default = []
mock = []

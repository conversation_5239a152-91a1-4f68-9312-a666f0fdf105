{"db_name": "PostgreSQL", "query": "\n            SELECT id, ciphertext, nonce FROM oauth_requests WHERE id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Text"}, {"ordinal": 1, "name": "ciphertext", "type_info": "Bytea"}, {"ordinal": 2, "name": "nonce", "type_info": "Bytea"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false]}, "hash": "0d288b12ba8b7e2fca893cd907344fd16dc98744143ffa2af0bcd9cf570b222d"}
{"db_name": "PostgreSQL", "query": "\n            SELECT id, ciphertext, nonce FROM phones WHERE id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Text"}, {"ordinal": 1, "name": "ciphertext", "type_info": "Bytea"}, {"ordinal": 2, "name": "nonce", "type_info": "Bytea"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false]}, "hash": "a0195cf9baef59e5624ac9d75c7a935bf5793b8dcba0253604b063639d9bd517"}
{"db_name": "PostgreSQL", "query": "\n            INSERT INTO emails (id, ciphertext, nonce)\n            VALUES ($1, $2, $3)\n            ON CONFLICT (id) DO UPDATE SET\n                ciphertext = EXCLUDED.ciphertext,\n                nonce = EXCLUDED.nonce\n            ", "describe": {"columns": [], "parameters": {"Left": ["Text", "Bytea", "Bytea"]}, "nullable": []}, "hash": "6242a606aee3eccb6aabb1f356b599ec0c58e601068499ca52a576f7cddb2988"}
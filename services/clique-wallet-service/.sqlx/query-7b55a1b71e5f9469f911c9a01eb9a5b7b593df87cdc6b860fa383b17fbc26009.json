{"db_name": "PostgreSQL", "query": "\n            SELECT\n                u.id,\n                u.wallets as \"wallets: sqlx::types::<PERSON>son<Vec<MinimalWallet>>\",\n                u.social_links as \"social_links: sqlx::types::<PERSON><PERSON><Vec<SocialProvider>>\",\n                u.ciphertext,\n                u.nonce\n            FROM users2 u\n            WHERE u.social_links @> $1::jsonb\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "wallets: sqlx::types::Json<Vec<MinimalWallet>>", "type_info": "Jsonb"}, {"ordinal": 2, "name": "social_links: sqlx::types::<PERSON>son<Vec<SocialProvider>>", "type_info": "Jsonb"}, {"ordinal": 3, "name": "ciphertext", "type_info": "Bytea"}, {"ordinal": 4, "name": "nonce", "type_info": "Bytea"}], "parameters": {"Left": ["Jsonb"]}, "nullable": [false, false, false, false, false]}, "hash": "7b55a1b71e5f9469f911c9a01eb9a5b7b593df87cdc6b860fa383b17fbc26009"}
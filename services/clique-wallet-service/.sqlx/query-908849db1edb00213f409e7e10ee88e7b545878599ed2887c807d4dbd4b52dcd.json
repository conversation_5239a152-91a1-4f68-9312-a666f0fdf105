{"db_name": "PostgreSQL", "query": "\n            SELECT id, ciphertext, nonce FROM emails WHERE id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Text"}, {"ordinal": 1, "name": "ciphertext", "type_info": "Bytea"}, {"ordinal": 2, "name": "nonce", "type_info": "Bytea"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false]}, "hash": "908849db1edb00213f409e7e10ee88e7b545878599ed2887c807d4dbd4b52dcd"}
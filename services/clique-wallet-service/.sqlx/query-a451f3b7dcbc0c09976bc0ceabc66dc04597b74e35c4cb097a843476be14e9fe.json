{"db_name": "PostgreSQL", "query": "\n            INSERT INTO phones (id, ciphertext, nonce)\n            VALUES ($1, $2, $3)\n            ON CONFLICT (id) DO UPDATE SET\n                ciphertext = EXCLUDED.ciphertext,\n                nonce = EXCLUDED.nonce\n            ", "describe": {"columns": [], "parameters": {"Left": ["Text", "Bytea", "Bytea"]}, "nullable": []}, "hash": "a451f3b7dcbc0c09976bc0ceabc66dc04597b74e35c4cb097a843476be14e9fe"}
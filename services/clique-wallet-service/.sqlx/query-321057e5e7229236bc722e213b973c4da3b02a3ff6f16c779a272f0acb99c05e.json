{"db_name": "PostgreSQL", "query": "\n            SELECT u.id,\n                u.wallets as \"wallets: sqlx::types::<PERSON>son<Vec<MinimalWallet>>\",\n                u.social_links as \"social_links: sqlx::types::<PERSON><PERSON><Vec<SocialProvider>>\",\n                u.ciphertext,\n                u.nonce\n            FROM users2 u\n            WHERE u.wallets @> $1::jsonb\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "wallets: sqlx::types::Json<Vec<MinimalWallet>>", "type_info": "Jsonb"}, {"ordinal": 2, "name": "social_links: sqlx::types::<PERSON>son<Vec<SocialProvider>>", "type_info": "Jsonb"}, {"ordinal": 3, "name": "ciphertext", "type_info": "Bytea"}, {"ordinal": 4, "name": "nonce", "type_info": "Bytea"}], "parameters": {"Left": ["Jsonb"]}, "nullable": [false, false, false, false, false]}, "hash": "321057e5e7229236bc722e213b973c4da3b02a3ff6f16c779a272f0acb99c05e"}
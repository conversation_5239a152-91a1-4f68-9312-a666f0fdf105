{"db_name": "PostgreSQL", "query": "\n            SELECT id, user_id, key_ciphertext, key_nonce, name, created_at, expires_at, is_active\n            FROM api_keys\n            WHERE id = $1 AND is_active = true\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "user_id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 2, "name": "key_ciphertext", "type_info": "Bytea"}, {"ordinal": 3, "name": "key_nonce", "type_info": "Bytea"}, {"ordinal": 4, "name": "name", "type_info": "Text"}, {"ordinal": 5, "name": "created_at", "type_info": "Timestamptz"}, {"ordinal": 6, "name": "expires_at", "type_info": "Timestamptz"}, {"ordinal": 7, "name": "is_active", "type_info": "Bool"}], "parameters": {"Left": ["<PERSON><PERSON>"]}, "nullable": [false, false, false, false, false, false, true, false]}, "hash": "019c15a8a09b5bc8efbbbb77957ce2780cdfff4209eeb70627aa88d46883c61e"}
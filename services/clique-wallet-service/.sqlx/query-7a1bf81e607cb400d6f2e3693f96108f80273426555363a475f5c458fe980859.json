{"db_name": "PostgreSQL", "query": "\n            SELECT id, ciphertext, nonce FROM challenges WHERE id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Text"}, {"ordinal": 1, "name": "ciphertext", "type_info": "Bytea"}, {"ordinal": 2, "name": "nonce", "type_info": "Bytea"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false]}, "hash": "7a1bf81e607cb400d6f2e3693f96108f80273426555363a475f5c458fe980859"}
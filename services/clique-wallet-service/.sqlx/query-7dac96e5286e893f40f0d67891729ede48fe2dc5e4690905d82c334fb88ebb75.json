{"db_name": "PostgreSQL", "query": "\n            SELECT id, expiry_date, ciphertext, nonce FROM sessions WHERE id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "Text"}, {"ordinal": 1, "name": "expiry_date", "type_info": "Timestamptz"}, {"ordinal": 2, "name": "ciphertext", "type_info": "Bytea"}, {"ordinal": 3, "name": "nonce", "type_info": "Bytea"}], "parameters": {"Left": ["Text"]}, "nullable": [false, false, false, false]}, "hash": "7dac96e5286e893f40f0d67891729ede48fe2dc5e4690905d82c334fb88ebb75"}
{"db_name": "PostgreSQL", "query": "\n            SELECT \n                id, \n                wallets as \"wallets: sqlx::types::<PERSON>son<Vec<MinimalWallet>>\",\n                social_links as \"social_links: sqlx::types::<PERSON><PERSON><Vec<SocialProvider>>\",\n                ciphertext,\n                nonce\n            FROM users2\n            WHERE id = $1\n            ", "describe": {"columns": [{"ordinal": 0, "name": "id", "type_info": "<PERSON><PERSON>"}, {"ordinal": 1, "name": "wallets: sqlx::types::Json<Vec<MinimalWallet>>", "type_info": "Jsonb"}, {"ordinal": 2, "name": "social_links: sqlx::types::<PERSON>son<Vec<SocialProvider>>", "type_info": "Jsonb"}, {"ordinal": 3, "name": "ciphertext", "type_info": "Bytea"}, {"ordinal": 4, "name": "nonce", "type_info": "Bytea"}], "parameters": {"Left": ["<PERSON><PERSON>"]}, "nullable": [false, false, false, false, false]}, "hash": "537dd568e6e9e374a635fe7041c61bc02b0f5c16929426de4edaa0320b1bee0c"}
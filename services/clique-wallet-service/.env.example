SIGNER_ENDPOINT=https://localhost:3000

KMS_MR_ENCLAVE=
KMS_MR_SIGNER=
KMS_URL=https://localhost:7070

MR_SIGNER=

TRUSTED_ENCLAVES=all
TRUSTED_SIGNERS=all


# For public network
PORT=3000
# For TEE mtls (private network)
SENSITIVE_SERVICE_PORT=3001

POSTGRES_USER=user
POSTGRES_PASSWORD=password
POSTGRES_HOST=localhost:5432
POSTGRES_DB=wallet_service
POSTGRES_REQUIRE_SSL=true
POSTGRES_MAX_CONNECTIONS=200
# Only need in the dev environment to build offline sql query
# Ignore it in the production environment
DATABASE_URL=postgresql://user:password@localhost:5432/wallet_service

CERT_SUBJECT_NAME=

WALLET_NAME=Clique

ENABLE_TWITTER=false
TWITTER_CLIENT_ID=
TWITTER_CLIENT_SECRET=

ENABLE_GOOGLE=true
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

ENABLE_TELEGRAM_LOGIN=true
ENABLE_TELEGRAM_BIND=true
TELEGRAM_BOT_TOKEN=
TELEGRAM_BOT_NAME=
TELEGRAM_AUTH_PAGE_TEMPLATE_PATH=templates/telegram.html
# Telegram bot that with outer domain
TELEGRAM_OUTER_BOT_TOKEN=

ENABLE_EMAIL=true
EMAIL_TEMPLATE_PATH=templates/email.html
# Azure email service
ENABLE_AZURE_EMAIL=true
AZURE_EMAIL_SERVICE_URL=
AZURE_EMAIL_SERVICE_API_VERSION=2021-10-01-preview
AZURE_EMAIL_SERVICE_API_KEY=
AZURE_EMAIL_SERVICE_FROM_ADDRESS=
# Mailtrap email service
ENABLE_MAILTRAP_EMAIL=true
MAILTRAP_EMAIL_SERVICE_API_KEY=
MAILTRAP_EMAIL_SERVICE_FROM_ADDRESS=


ENABLE_SMS=true
SMS_PRELUDE_API_KEY=

ENABLE_PHANTOM=true

ENABLE_COINBASE=true
# organizations/{project_id}/apiKeys/{key_id}
COINBASE_API_KEY=organizations/9911a2bb-4363-44d0-b4d5-83d0ac0213c1/apiKeys/9bb5d4be-8811-48f6-b6b7-ceb0c409cb8e
# Ed25519 signature API key
COINBASE_API_SECRET=

mod coinbase;
mod config;
mod core;
mod handler;
mod master_key;
mod models;
mod sensitive_handler;
mod social;
mod storage;
mod utils;

use std::{net::SocketAddr, sync::Arc};

use axum::{
    Router,
    routing::{delete, get, post},
};
use axum_server::tls_rustls::{RustlsAcceptor, RustlsConfig};
use clique_sibyl_commonlib::tls::config::{
    create_tls_server_config_with_client_auth, create_tls_server_config_without_client_auth,
};
use http::{HeaderValue, request::Parts as RequestParts};
use reqwest::{Method, header};
use time::Duration;
use tower_http::cors::{AllowOrigin, CorsLayer};
use tower_sessions::{SessionManagerLayer, cookie::Key};

use crate::{
    config::config,
    handler::*,
    models::{PostgresClient, PostgresSessionStore},
};

const PRIVATE_COOKIE_SALT: &[u8] = b"clique-wallet-service/private-cookie";

#[tokio::main]
async fn main() {
    // Initialize tracing
    crate::utils::setup_tracing();

    // init master key
    let _ = master_key::MasterKey::open();
    tracing::info!("Master key initialized");

    let _postgres_client = PostgresClient::get().await;

    // Create CORS layer
    let headers = [
        // Required headers for session cookies
        header::CONTENT_TYPE,
        header::AUTHORIZATION,
        header::ACCEPT,
        // Custom headers your frontend might use
    ];
    let methods = [Method::GET, Method::POST];
    let cors = CorsLayer::new()
        .allow_origin(AllowOrigin::async_predicate(
            |_origin: HeaderValue, _request_parts: &RequestParts| async move {
                // TODO: check if origin is in the list of allowed origins
                return true;
            },
        ))
        .allow_methods(methods)
        .allow_headers(headers)
        .allow_credentials(true)
        .vary(vec![
            header::ORIGIN,
            header::ACCESS_CONTROL_REQUEST_HEADERS,
            header::ACCESS_CONTROL_REQUEST_METHOD,
        ]);

    // Session store
    let session_store = PostgresSessionStore::default();

    let private_cookie_key = master_key::MasterKey::open().derive_key_128(PRIVATE_COOKIE_SALT);

    // Session configuration
    let session_layer = SessionManagerLayer::new(session_store)
        .with_secure(true) // true in production
        .with_http_only(true)
        .with_same_site(tower_sessions::cookie::SameSite::None)
        .with_private(Key::from(&private_cookie_key))
        .with_expiry(tower_sessions::Expiry::OnInactivity(Duration::days(3)));
    tracing::info!("Session layer: {:?}", session_layer);

    // Non-sensitive app, for public access
    let app = Router::new()
        .route("/healthz", get(handle_healthz))
        .route("/login", post(handle_login))
        .route("/logout", post(handle_logout))
        .route("/session", get(handle_session))
        .route("/bind", post(handle_bind))
        .route("/sign", post(handle_sign))
        .route("/send_verification", post(handle_send_verification))
        .route("/oauth/init", post(handle_oauth_init))
        .route("/oauth/callback", get(handle_oauth_callback))
        .route(TELEGRAM_AUTH_PAGE_ROUTE, get(telegram_auth_page))
        .route("/address", get(handle_address))
        .route("/ramp/coinbase", post(handle_coinbase_session_token))
        .route(
            "/ramp/coinbase/offramp/status",
            get(handle_coinbase_offramp_transaction_status),
        )
        .route("/challenge", get(handle_challenge))
        // API Key management routes (require session auth)
        .route(
            "/api/keys",
            post(handle_create_api_key).get(handle_list_api_keys),
        )
        .route("/api/keys/{id}", delete(handle_delete_api_key))
        // API signing route (requires API key auth)
        .route("/api/sign", post(handle_api_sign))
        .layer(cors) // Add CORS middleware
        .layer(session_layer);

    let addr = SocketAddr::from(([0, 0, 0, 0], config().port));
    tracing::info!("Server starting on https://{}", addr);

    let tls_config =
        create_tls_server_config_without_client_auth().expect("Failed to create TLS config");
    let rustls_config = RustlsConfig::from_config(Arc::new(tls_config));

    // Sensitive app, for sensitive operations in the private network, need mtls with TEE
    // attestation
    let sensitive_app = Router::new()
        .route(
            "/admin/create",
            post(crate::sensitive_handler::admin_create_user),
        )
        .route("/admin/sign", post(crate::sensitive_handler::admin_sign))
        .route(
            "/admin/info/{user_id}",
            get(crate::sensitive_handler::handle_info),
        );

    let sensitive_addr = SocketAddr::from(([0, 0, 0, 0], config().sensitive_service_port));
    tracing::info!("Sensitive server starting on https://{}", sensitive_addr);

    let trusted_enclaves = clique_sibyl_commonlib::utils::get_trusted_enclaves_from_env()
        .expect("Failed to get trusted enclaves")
        .cloned();
    let trusted_signers = clique_sibyl_commonlib::utils::get_trusted_signers_from_env()
        .expect("Failed to get trusted signers")
        .cloned();

    let require_client_cert = true;
    let sensitive_server_config = create_tls_server_config_with_client_auth(
        trusted_enclaves,
        trusted_signers,
        require_client_cert,
    )
    .expect("Failed to create TLS server config");
    let sensitive_rustls_config = RustlsConfig::from_config(Arc::new(sensitive_server_config));

    // Start non-sensitive server and sensitive server
    let non_sensitive_server =
        tokio::spawn(axum_server::bind_rustls(addr, rustls_config).serve(app.into_make_service()));
    let sensitive_server = tokio::spawn(
        axum_server::bind(sensitive_addr)
            .acceptor(RustlsAcceptor::new(sensitive_rustls_config))
            .serve(sensitive_app.into_make_service()),
    );

    tokio::select! {
        _ = non_sensitive_server => {
            tracing::error!("Non-sensitive server exited");
        }
        _ = sensitive_server => {
            tracing::error!("Sensitive server exited");
        }
    }
}

async fn handle_healthz() -> &'static str {
    "OK"
}

use eyre::{Result, eyre};
use uuid::Uuid;

use crate::models::{
    Challenge, EmailVerification, MinimalWallet, OauthRequest, PhoneVerification, PostgresClient,
    User2, enums::SocialProvider,
};

#[derive(Debug, Clone)]
pub struct Storage {}

impl Storage {
    pub async fn add_user2(user: User2) -> Result<()> {
        let db = PostgresClient::get().await;
        db.create_user2(user).await
    }

    pub async fn update_user2(user: User2) -> Result<()> {
        let db = PostgresClient::get().await;
        db.update_user2(user).await
    }

    pub async fn add_social_links_for_user(
        user: User2,
        social_links: Vec<SocialProvider>,
    ) -> Result<()> {
        let db = PostgresClient::get().await;
        db.add_social_links_for_user(user, social_links).await
    }

    pub async fn get_user2(id: Uuid) -> Result<Option<User2>> {
        let db = PostgresClient::get().await;
        db.get_user2_by_id(id).await
    }

    pub async fn get_user2_by_social(social: &SocialProvider) -> Result<Option<User2>> {
        let db = PostgresClient::get().await;
        db.get_user2_by_social_link(social).await
    }

    pub async fn get_user2_by_wallet(wallet: &MinimalWallet) -> Result<Option<User2>> {
        let db = PostgresClient::get().await;
        db.get_user2_by_wallet(wallet).await
    }

    pub async fn remove_user2(id: Uuid) -> Result<()> {
        let db = PostgresClient::get().await;
        db.delete_user2(id).await
    }

    pub async fn add_email_verification(email: &str, verification_code: String) -> Result<()> {
        let db = PostgresClient::get().await;
        db.create_or_update_email_verification(EmailVerification::new(
            email.to_string(),
            verification_code,
        ))
        .await
    }

    pub async fn get_email_verification(email: &str) -> Result<Option<EmailVerification>> {
        let id = email.to_lowercase();
        let db = PostgresClient::get().await;
        db.get_email_verification(id).await
    }

    pub async fn verify_email(email: &str, verification_code: String) -> Result<bool> {
        let id = email.to_lowercase();
        let db = PostgresClient::get().await;
        let verification = db.get_email_verification(id).await?;
        let mut verification = verification.ok_or(eyre!("Email not found"))?;
        let res = verification.verify(verification_code);
        db.update_email_verification(verification).await?;
        res
    }

    pub async fn delete_email_verification(email: &str) -> Result<()> {
        let db = PostgresClient::get().await;
        let id = email.to_lowercase();
        db.delete_email_verification(id).await
    }

    pub async fn add_phone_verification(phone: &str, sms_id: String) -> Result<()> {
        let db = PostgresClient::get().await;
        db.create_or_update_phone_verification(PhoneVerification::new(phone.to_string(), sms_id))
            .await
    }

    pub async fn update_phone_verification(phone_verification: PhoneVerification) -> Result<()> {
        let db = PostgresClient::get().await;
        db.update_phone_verification(phone_verification).await
    }

    pub async fn get_phone_verification(phone: &str) -> Result<Option<PhoneVerification>> {
        let id = phone.to_lowercase();
        let db = PostgresClient::get().await;
        db.get_phone_verification(id).await
    }

    pub async fn delete_phone_verification(phone: &str) -> Result<()> {
        let db = PostgresClient::get().await;
        let id = phone.to_lowercase();
        db.delete_phone_verification(id).await
    }

    pub async fn create_oauth_request(oauth_request: OauthRequest) -> Result<()> {
        let db = PostgresClient::get().await;
        db.create_oauth_request(oauth_request).await
    }

    pub async fn get_oauth_request(id: String) -> Result<Option<OauthRequest>> {
        let db = PostgresClient::get().await;
        db.get_oauth_request(id).await
    }

    pub async fn update_oauth_request(oauth_request: OauthRequest) -> Result<()> {
        let db = PostgresClient::get().await;
        db.update_oauth_request(oauth_request).await
    }

    pub async fn delete_oauth_request(id: String) -> Result<()> {
        let db = PostgresClient::get().await;
        db.delete_oauth_request(id).await
    }

    pub async fn create_challenge(challenge: Challenge) -> Result<()> {
        let db = PostgresClient::get().await;
        db.create_challenge(challenge).await
    }

    pub async fn create_or_update_challenge(challenge: Challenge) -> Result<()> {
        let db = PostgresClient::get().await;
        db.create_or_update_challenge(challenge).await
    }

    pub async fn get_challenge(id: String) -> Result<Option<Challenge>> {
        let db = PostgresClient::get().await;
        db.get_challenge(id).await
    }

    pub async fn delete_challenge(id: String) -> Result<()> {
        let db = PostgresClient::get().await;
        db.delete_challenge(id).await
    }
}

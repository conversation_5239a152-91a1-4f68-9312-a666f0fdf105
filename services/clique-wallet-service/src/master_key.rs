use std::{ops::Deref, sync::OnceLock};

use clique_sibyl_commonlib::{
    attestation::generate_attestation, tls::config::create_tls_client_config_with_client_auth,
};
use k256::{ecdsa::Signing<PERSON>ey, sha2::Sha256};
use serde_json::{Value, json};

#[derive(Clone)]
pub struct MasterKey {
    inner: SigningKey,
}

impl <PERSON><PERSON><PERSON> {
    const APP_SALT: &[u8] = b"clique-wallet-service/v0.1.0";

    #[cfg(not(feature = "mock"))]
    pub fn open() -> &'static Self {
        static MASTER_KEY: OnceLock<MasterKey> = OnceLock::new();
        let kms_url = std::env::var("KMS_URL").expect("KMS_URL env var not set");
        let mr_signer = std::env::var("MR_SIGNER").expect("MR_SIGNER env var not set");
        MASTER_KEY.get_or_init(|| {
            let master_key = match get_app_key_from_kms(&kms_url, Self::APP_SALT, &mr_signer) {
                Ok(key) => key,
                Err(e) => {
                    tracing::error!("Failed to get app key from KMS: {:?}", e);
                    panic!("Failed to get app key from KMS");
                }
            };

            let public_key = master_key.verifying_key();
            let public_key_bytes = public_key.to_sec1_bytes();
            tracing::info!("Init: {}", hex::encode(public_key_bytes));

            MasterKey { inner: master_key }
        })
    }

    #[cfg(feature = "mock")]
    pub fn open() -> &'static Self {
        static MASTER_KEY: OnceLock<MasterKey> = OnceLock::new();
        MASTER_KEY.get_or_init(|| {
            let master_key = SigningKey::from_slice(&[2u8; 32]).unwrap();
            MasterKey { inner: master_key }
        })
    }

    pub fn derive_key(&self, user_id: impl AsRef<[u8]>) -> [u8; 32] {
        let user_master_key = self.user_master_key(user_id);
        Self::derive_user_key(user_master_key)
    }

    // Derive a 128-byte key specifically
    pub fn derive_key_128(&self, user_id: impl AsRef<[u8]>) -> [u8; 128] {
        let user_master_key = self.user_master_key(user_id);
        Self::derive_user_key_128(user_master_key)
    }

    fn user_master_key(&self, user_id: impl AsRef<[u8]>) -> [u8; 65] {
        // Sign the user_id
        let (sig, recid) = self
            .sign_recoverable(user_id.as_ref())
            .expect("signing operation should succeed");
        let mut sig_bytes = [0u8; 65];
        sig_bytes[..64].copy_from_slice(&sig.to_bytes());
        sig_bytes[64] = recid.to_byte();
        sig_bytes
    }

    fn derive_user_key(passwd: impl AsRef<[u8]>) -> [u8; 32] {
        let mut key = [0u8; 32];
        pbkdf2::pbkdf2_hmac::<Sha256>(passwd.as_ref(), &[], 10000, &mut key);
        key
    }

    fn derive_user_key_128(passwd: impl AsRef<[u8]>) -> [u8; 128] {
        let mut key = [0u8; 128];
        pbkdf2::pbkdf2_hmac::<Sha256>(passwd.as_ref(), &[], 10000, &mut key);
        key
    }
}

impl Deref for MasterKey {
    type Target = SigningKey;

    fn deref(&self) -> &Self::Target {
        &self.inner
    }
}

fn get_app_key_from_kms(kms_url: &str, salt: &[u8], mr_signer: &str) -> eyre::Result<SigningKey> {
    let kms_mr_signer = match std::env::var("KMS_MR_SIGNER") {
        Ok(s) => {
            if s.is_empty() {
                tracing::info!("KMS_MR_SIGNER is empty, ignore");
                None
            } else {
                tracing::info!("KMS_MR_SIGNER: {}", s);
                Some(vec![s])
            }
        }
        Err(_) => {
            tracing::info!("KMS_MR_SIGNER is not set, ignore");
            None
        }
    };
    let kms_mr_enclave = match std::env::var("KMS_MR_ENCLAVE") {
        Ok(s) => {
            if s.is_empty() {
                tracing::info!("KMS_MR_ENCLAVE is empty, ignore");
                None
            } else {
                tracing::info!("KMS_MR_ENCLAVE: {}", s);
                Some(vec![s])
            }
        }
        Err(_) => {
            tracing::info!("KMS_MR_ENCLAVE is not set, ignore");
            None
        }
    };

    let tls = create_tls_client_config_with_client_auth(kms_mr_enclave, kms_mr_signer)?;
    let client = reqwest::blocking::ClientBuilder::new()
        .use_preconfigured_tls(tls)
        .build()?;
    let attestation = generate_attestation(salt)?;

    let req = json!(
        {
            "attestation": attestation,
            "key_mode":  {
                "mode": "relaxed",
                "mr_signer": mr_signer
            }
        }
    );

    let url = format!("{kms_url}/get_key");
    tracing::info!("KMS url: {}", url);
    tracing::info!("MR_SIGNER: {}", mr_signer);
    let resp = client.post(&url).json(&req).send()?.json::<Value>()?;

    let app_key = resp["app_key"]
        .as_str()
        .ok_or(eyre::eyre!("app_key not found"))?;
    let app_key = hex::decode(app_key)?;

    tracing::info!("Get app key from KMS success");

    Ok(SigningKey::from_slice(&app_key)?)
}

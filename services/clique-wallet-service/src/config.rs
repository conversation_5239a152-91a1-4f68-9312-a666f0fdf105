use std::sync::OnceLock;

use clique_sibyl_commonlib::{
    rustls::ClientConfig,
    tls::config::create_tls_client_config_with_client_auth,
    utils::{get_trusted_enclaves_from_env, get_trusted_signers_from_env},
};

use crate::social::email::email_template::EmailTemplate;

#[derive(Debug, <PERSON>lone)]
pub struct Config {
    pub port: u16,
    pub sensitive_service_port: u16,
    pub signer_endpoint: String,
    pub postgres_url: String,
    pub postgres_max_connections: u16,
    pub tls_client_config: ClientConfig,

    pub wallet_name: String,

    pub enable_twitter: bool,
    pub twitter_client_id: String,
    pub twitter_client_secret: String,

    pub enable_google: bool,
    pub google_client_id: String,
    pub google_client_secret: String,

    pub enable_telegram_login: bool,
    pub enable_telegram_bind: bool,
    pub telegram_bot_token: String,
    pub telegram_bot_name: String,
    pub telegram_auth_page_template_str: Option<String>,
    pub telegram_outer_bot_token: String,

    pub enable_email: bool,
    pub email_template_str: Option<String>,
    // Azure email
    pub enable_azure_email: bool,
    pub azure_email_service_url: String,
    pub azure_email_service_api_version: String,
    pub azure_email_service_api_key: String,
    pub azure_email_service_from_address: String,
    // Mailtrap email
    pub enable_mailtrap_email: bool,
    pub mailtrap_email_service_api_key: String,
    pub mailtrap_email_service_from_address: String,

    pub enable_sms: bool,
    pub sms_prelude_api_key: String,

    pub enable_phantom: bool,

    pub enable_coinbase: bool,
    pub coinbase_api_key: String,
    pub coinbase_api_secret: String,
}

impl Config {
    pub fn from_env() -> Self {
        let port = std::env::var("PORT")
            .expect("PORT is not set")
            .parse::<u16>()
            .expect("PORT is not a number");
        tracing::info!("Port: {}", port);

        let sensitive_service_port = std::env::var("SENSITIVE_SERVICE_PORT")
            .expect("SENSITIVE_SERVICE_PORT is not set")
            .parse::<u16>()
            .expect("SENSITIVE_SERVICE_PORT is not a number");
        tracing::info!("Sensitive service port: {}", sensitive_service_port);

        let signer_endpoint = std::env::var("SIGNER_ENDPOINT").expect("SIGNER_ENDPOINT is not set");
        tracing::info!("Signer endpoint: {}", signer_endpoint);

        let trusted_enclaves = get_trusted_enclaves_from_env()
            .expect("Failed to get trusted enclaves")
            .cloned();
        tracing::info!("Trusted enclaves: {:?}", trusted_enclaves);

        let trusted_signers = get_trusted_signers_from_env()
            .expect("Failed to get trusted signers")
            .cloned();
        tracing::info!("Trusted signers: {:?}", trusted_signers);

        let tls_client_config =
            create_tls_client_config_with_client_auth(trusted_enclaves, trusted_signers)
                .expect("Failed to create TLS client config");

        let wallet_name = std::env::var("WALLET_NAME").expect("WALLET_NAME is not set");
        tracing::info!("Wallet name: {}", wallet_name);

        let postgres_user = std::env::var("POSTGRES_USER").expect("POSTGRES_USER is not set");
        let postgres_password =
            std::env::var("POSTGRES_PASSWORD").expect("POSTGRES_PASSWORD is not set");
        let postgres_host = std::env::var("POSTGRES_HOST").expect("POSTGRES_HOST is not set");
        let postgres_dbname = std::env::var("POSTGRES_DB").expect("POSTGRES_DB is not set");
        let postgres_require_ssl = std::env::var("POSTGRES_REQUIRE_SSL")
            .expect("POSTGRES_REQUIRE_SSL is not set")
            .parse::<bool>()
            .expect("POSTGRES_REQUIRE_SSL is not a boolean");
        let postgres_max_connections = std::env::var("POSTGRES_MAX_CONNECTIONS")
            .unwrap_or("200".to_string())
            .parse::<u16>()
            .expect("POSTGRES_MAX_CONNECTIONS is not a number");

        let postgres_url = format!(
            "postgres://{username}:{password}@{host}/{dbname}?sslmode={sslmode}",
            username = urlencoding::encode(&postgres_user),
            password = urlencoding::encode(&postgres_password),
            host = postgres_host,
            dbname = postgres_dbname,
            sslmode = if postgres_require_ssl {
                "require"
            } else {
                "prefer"
            }
        );

        let enable_twitter = std::env::var("ENABLE_TWITTER")
            .unwrap_or("false".to_string())
            .parse::<bool>()
            .expect("ENABLE_TWITTER is not a boolean");
        tracing::info!("Enable twitter: {}", enable_twitter);

        let twitter_client_id = std::env::var("TWITTER_CLIENT_ID").unwrap_or_default();
        let twitter_client_secret = std::env::var("TWITTER_CLIENT_SECRET").unwrap_or_default();
        if enable_twitter && (twitter_client_id.is_empty() || twitter_client_secret.is_empty()) {
            panic!(
                "TWITTER_CLIENT_ID and TWITTER_CLIENT_SECRET must be set if ENABLE_TWITTER is true"
            );
        }

        let enable_google = std::env::var("ENABLE_GOOGLE")
            .unwrap_or("false".to_string())
            .parse::<bool>()
            .expect("ENABLE_GOOGLE is not a boolean");
        tracing::info!("Enable google: {}", enable_google);

        let google_client_id = std::env::var("GOOGLE_CLIENT_ID").unwrap_or_default();
        let google_client_secret = std::env::var("GOOGLE_CLIENT_SECRET").unwrap_or_default();
        if enable_google && (google_client_id.is_empty() || google_client_secret.is_empty()) {
            panic!(
                "GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET must be set if ENABLE_GOOGLE is true"
            );
        }

        let enable_telegram_login = std::env::var("ENABLE_TELEGRAM_LOGIN")
            .unwrap_or("false".to_string())
            .parse::<bool>()
            .expect("ENABLE_TELEGRAM_LOGIN is not a boolean");
        tracing::info!("Enable telegram login: {}", enable_telegram_login);

        let enable_telegram_bind = std::env::var("ENABLE_TELEGRAM_BIND")
            .unwrap_or("false".to_string())
            .parse::<bool>()
            .expect("ENABLE_TELEGRAM_BIND is not a boolean");
        tracing::info!("Enable telegram bind: {}", enable_telegram_bind);

        let telegram_bot_token = std::env::var("TELEGRAM_BOT_TOKEN").unwrap_or_default();
        let telegram_bot_name = std::env::var("TELEGRAM_BOT_NAME").unwrap_or_default();
        let telegram_auth_page_template_path = std::env::var("TELEGRAM_AUTH_PAGE_TEMPLATE_PATH")
            .unwrap_or("templates/telegram.html".to_string());
        let mut telegram_auth_page_template_str = None;

        let telegram_outer_bot_token =
            std::env::var("TELEGRAM_OUTER_BOT_TOKEN").unwrap_or_default();

        if (enable_telegram_login || enable_telegram_bind)
            && (telegram_bot_token.is_empty() || telegram_bot_name.is_empty())
        {
            panic!(
                "TELEGRAM_BOT_TOKEN and TELEGRAM_BOT_NAME must be set if ENABLE_TELEGRAM_LOGIN or ENABLE_TELEGRAM_BIND is true"
            );
        }
        if enable_telegram_login || enable_telegram_bind {
            match std::fs::read_to_string(&telegram_auth_page_template_path) {
                Ok(template) => {
                    telegram_auth_page_template_str = Some(template);
                }
                Err(e) => {
                    panic!(
                        "Failed to read telegram auth page template with path {}: {}",
                        telegram_auth_page_template_path, e
                    );
                }
            }
        }

        let enable_email = std::env::var("ENABLE_EMAIL")
            .unwrap_or("false".to_string())
            .parse::<bool>()
            .expect("ENABLE_EMAIL is not a boolean");
        tracing::info!("Enable email: {}", enable_email);

        let email_template_path =
            std::env::var("EMAIL_TEMPLATE_PATH").unwrap_or("templates/email.html".to_string());
        tracing::info!("Email template path: {}", email_template_path);
        let mut email_template_str = None;

        let mut enable_azure_email = false;
        let mut azure_email_service_url = String::new();
        let mut azure_email_service_api_version = String::new();
        let mut azure_email_service_api_key = String::new();
        let mut azure_email_service_from_address = String::new();

        let mut enable_mailtrap_email = false;
        let mut mailtrap_email_service_api_key = String::new();
        let mut mailtrap_email_service_from_address = String::new();

        if enable_email {
            match std::fs::read_to_string(&email_template_path) {
                Ok(template) => {
                    EmailTemplate::verify_template(&template)
                        .expect("Failed to verify email template");
                    tracing::info!("Email template:\n{}", template);
                    email_template_str = Some(template);
                }
                Err(e) => {
                    tracing::error!(
                        "Failed to read email template with path {}: {}",
                        email_template_path,
                        e
                    );
                    panic!(
                        "Failed to read email template with path {}: {}",
                        email_template_path, e
                    );
                }
            };

            enable_azure_email = std::env::var("ENABLE_AZURE_EMAIL")
                .unwrap_or("false".to_string())
                .parse::<bool>()
                .expect("ENABLE_AZURE_EMAIL is not a boolean");
            tracing::info!("Enable azure email: {}", enable_azure_email);

            azure_email_service_url = std::env::var("AZURE_EMAIL_SERVICE_URL").unwrap_or_default();
            azure_email_service_api_version =
                std::env::var("AZURE_EMAIL_SERVICE_API_VERSION").unwrap_or_default();
            azure_email_service_api_key =
                std::env::var("AZURE_EMAIL_SERVICE_API_KEY").unwrap_or_default();
            azure_email_service_from_address =
                std::env::var("AZURE_EMAIL_SERVICE_FROM_ADDRESS").unwrap_or_default();

            if enable_azure_email && azure_email_service_api_version != "2021-10-01-preview" {
                tracing::error!(
                    "AZURE_EMAIL_SERVICE_API_VERSION only supports '2021-10-01-preview' now: current value is {}",
                    azure_email_service_api_version
                );
                panic!("AZURE_EMAIL_SERVICE_API_VERSION only supports '2021-10-01-preview' now");
            }

            if enable_azure_email
                && (azure_email_service_url.is_empty()
                    || azure_email_service_api_version.is_empty()
                    || azure_email_service_api_key.is_empty()
                    || azure_email_service_from_address.is_empty())
            {
                panic!(
                    "AZURE_EMAIL_SERVICE_URL, AZURE_EMAIL_SERVICE_API_VERSION, AZURE_EMAIL_SERVICE_API_KEY, and AZURE_EMAIL_SERVICE_FROM_ADDRESS must be set if ENABLE_EMAIL is true"
                );
            }

            enable_mailtrap_email = std::env::var("ENABLE_MAILTRAP_EMAIL")
                .unwrap_or("false".to_string())
                .parse::<bool>()
                .expect("ENABLE_MAILTRAP_EMAIL is not a boolean");
            tracing::info!("Enable mailtrap email: {}", enable_mailtrap_email);

            mailtrap_email_service_api_key =
                std::env::var("MAILTRAP_EMAIL_SERVICE_API_KEY").unwrap_or_default();
            mailtrap_email_service_from_address =
                std::env::var("MAILTRAP_EMAIL_SERVICE_FROM_ADDRESS").unwrap_or_default();

            if enable_mailtrap_email
                && (mailtrap_email_service_api_key.is_empty()
                    || mailtrap_email_service_from_address.is_empty())
            {
                panic!(
                    "MAILTRAP_EMAIL_SERVICE_API_KEY and MAILTRAP_EMAIL_SERVICE_FROM_ADDRESS must be set if ENABLE_MAILTRAP_EMAIL is true"
                );
            }
        }

        let enable_sms = std::env::var("ENABLE_SMS")
            .unwrap_or("false".to_string())
            .parse::<bool>()
            .expect("ENABLE_SMS is not a boolean");
        tracing::info!("Enable sms: {}", enable_sms);

        let sms_prelude_api_key = std::env::var("SMS_PRELUDE_API_KEY").unwrap_or_default();
        if enable_sms && sms_prelude_api_key.is_empty() {
            panic!("SMS_PRELUDE_API_KEY must be set if ENABLE_SMS is true");
        }

        let enable_coinbase = std::env::var("ENABLE_COINBASE")
            .unwrap_or("false".to_string())
            .parse::<bool>()
            .expect("ENABLE_COINBASE is not a boolean");
        tracing::info!("Enable coinbase: {}", enable_coinbase);

        let coinbase_api_key = std::env::var("COINBASE_API_KEY").unwrap_or_default();
        let coinbase_api_secret = std::env::var("COINBASE_API_SECRET").unwrap_or_default();
        if enable_coinbase && (coinbase_api_key.is_empty() || coinbase_api_secret.is_empty()) {
            panic!(
                "COINBASE_API_KEY and COINBASE_API_SECRET must be set if ENABLE_COINBASE is true"
            );
        }

        let enable_phantom = std::env::var("ENABLE_PHANTOM")
            .unwrap_or("false".to_string())
            .parse::<bool>()
            .expect("ENABLE_PHANTOM is not a boolean");
        tracing::info!("Enable phantom: {}", enable_phantom);

        Self {
            port,
            sensitive_service_port,
            signer_endpoint,
            postgres_url,
            postgres_max_connections,
            tls_client_config,

            wallet_name,

            enable_twitter,
            twitter_client_id,
            twitter_client_secret,

            enable_google,
            google_client_id,
            google_client_secret,

            enable_telegram_login,
            enable_telegram_bind,
            telegram_bot_token,
            telegram_bot_name,
            telegram_auth_page_template_str,
            telegram_outer_bot_token,

            enable_email,
            email_template_str,

            enable_azure_email,
            azure_email_service_url,
            azure_email_service_api_version,
            azure_email_service_api_key,
            azure_email_service_from_address,

            enable_mailtrap_email,
            mailtrap_email_service_api_key,
            mailtrap_email_service_from_address,

            enable_sms,
            sms_prelude_api_key,

            enable_phantom,

            enable_coinbase,
            coinbase_api_key,
            coinbase_api_secret,
        }
    }
}

pub fn config() -> &'static Config {
    static CONFIG: OnceLock<Config> = OnceLock::new();
    CONFIG.get_or_init(|| Config::from_env())
}

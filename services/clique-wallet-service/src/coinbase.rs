use std::{collections::HashMap, sync::OnceLock};

use base64::{Engine as _, engine::general_purpose};
use chrono::Utc;
use eyre::Result;
use jsonwebtoken::{Encoding<PERSON><PERSON>, Header, encode};
use rand::RngCore;
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;

use crate::config::config;

static COINBASE_JWT_BUILDER: OnceLock<CoinbaseJwtBuilder> = OnceLock::new();

const SESSION_TOKEN_API_METHOD: &str = "POST";
const SESSION_TOKEN_API_HOST: &str = "api.developer.coinbase.com";
const SESSION_TOKEN_API_PATH: &str = "/onramp/v1/token";

#[derive(Debug, Serialize, Deserialize)]
pub struct Address {
    pub address: String,
    pub blockchains: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SessionTokenApiPayload {
    pub addresses: Vec<Address>,
    pub assets: Option<Vec<String>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SessionTokenApiResponse {
    pub token: String,
    // pub channel_id: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,
    iss: String,
    nbf: i64,
    exp: i64,
    uri: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OfframpTransactionAmount {
    pub value: String,
    pub currency: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OfframpTransaction {
    pub id: String,
    pub asset: String,
    pub status: String,
    pub network: String,
    pub sell_amount: OfframpTransactionAmount,
    pub total: OfframpTransactionAmount,
    pub subtotal: OfframpTransactionAmount,
    pub coinbase_fee: OfframpTransactionAmount,
    pub exchange_rate: OfframpTransactionAmount,
    #[serde(rename = "fromAddress")]
    pub from_address: String,
    #[serde(rename = "toAddress")]
    pub to_address: String,
    pub tx_hash: String,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OfframpTransactionStatusResponse {
    pub transactions: Vec<OfframpTransaction>,
    pub next_page_key: Option<String>,
    pub total_count: Option<String>,
}

pub struct CoinbaseJwtBuilder {
    pub enable_coinbase: bool,
    pub api_key: String,
    pub api_secret: String,
    pub encoding_key: Option<EncodingKey>,
    pub jwt_for_session_token: RwLock<Option<String>>,
}

impl CoinbaseJwtBuilder {
    fn raw_ed25519_to_pkcs8_der(private_key: &[u8; 32]) -> Vec<u8> {
        // PKCS#8 DER structure for Ed25519 private key
        let mut der = Vec::new();

        // SEQUENCE tag and length
        der.push(0x30);
        der.push(0x2e); // Total length: 46 bytes

        // Version (INTEGER 0)
        der.extend_from_slice(&[0x02, 0x01, 0x00]);

        // Algorithm identifier for Ed25519
        der.extend_from_slice(&[
            0x30, 0x05, // SEQUENCE, length 5
            0x06, 0x03, 0x2b, 0x65, 0x70, // OID for Ed25519
        ]);

        // Private key (OCTET STRING containing OCTET STRING)
        der.extend_from_slice(&[
            0x04, 0x22, // OCTET STRING, length 34
            0x04, 0x20, // Inner OCTET STRING, length 32
        ]);
        der.extend_from_slice(private_key);

        der
    }

    fn load_ed25519_encoding_key(api_secret: &str) -> Result<EncodingKey> {
        let private_key_bytes = general_purpose::STANDARD.decode(api_secret)?;

        // Ed25519 keys should be exactly 32 bytes
        let key_bytes: [u8; 32] = if private_key_bytes.len() == 32 {
            private_key_bytes
                .try_into()
                .map_err(|_| eyre::eyre!("Failed to convert to 32-byte array"))?
        } else if private_key_bytes.len() == 64 {
            // Sometimes the key might be 64 bytes (32 bytes private + 32 bytes public)
            // Take only the first 32 bytes
            private_key_bytes[..32]
                .try_into()
                .map_err(|_| eyre::eyre!("Failed to extract 32 bytes from key"))?
        } else {
            return Err(eyre::eyre!(
                "Unexpected key length: {} bytes. Ed25519 keys should be 32 bytes.",
                private_key_bytes.len()
            ));
        };

        // Convert raw Ed25519 key to PKCS#8 DER format
        let der_bytes = Self::raw_ed25519_to_pkcs8_der(&key_bytes);
        let encoding_key = EncodingKey::from_ed_der(&der_bytes);

        Ok(encoding_key)
    }

    fn new(enable_coinbase: bool, api_key: String, api_secret: String) -> Self {
        let encoding_key = if enable_coinbase {
            let encoding_key = Self::load_ed25519_encoding_key(&api_secret)
                .expect("Failed to load coinbase encoding key");
            Some(encoding_key)
        } else {
            None
        };

        let jwt_for_session_token = RwLock::new(None);

        Self {
            enable_coinbase,
            api_key,
            api_secret,
            encoding_key,
            jwt_for_session_token,
        }
    }

    pub fn get() -> &'static CoinbaseJwtBuilder {
        COINBASE_JWT_BUILDER.get_or_init(|| {
            let config = config();
            CoinbaseJwtBuilder::new(
                config.enable_coinbase,
                config.coinbase_api_key.clone(),
                config.coinbase_api_secret.clone(),
            )
        })
    }

    pub fn enable_coinbase(&self) -> bool {
        self.enable_coinbase
    }

    fn build_jwt(&self, uri: String) -> Result<String> {
        if !self.enable_coinbase {
            return Err(eyre::eyre!("Coinbase is not enabled"));
        }
        if self.encoding_key.is_none() {
            return Err(eyre::eyre!("Coinbase encoding key is not loaded"));
        }
        let encoding_key = self.encoding_key.as_ref().unwrap();

        let now = Utc::now().timestamp();
        let exp = now + 120; // 2 minutes

        // Create JWT payload
        let claims = Claims {
            sub: self.api_key.clone(),
            iss: "cdp".to_string(),
            nbf: now,
            exp,
            uri,
        };

        // Generate random nonce
        let mut nonce_bytes = [0u8; 16];
        rand::thread_rng().fill_bytes(&mut nonce_bytes);
        let nonce = hex::encode(nonce_bytes);

        // Create JWT header with nonce
        let mut extras = HashMap::with_capacity(1);
        extras.insert("nonce".to_string(), nonce);
        let header = Header {
            alg: jsonwebtoken::Algorithm::EdDSA,
            typ: Some("JWT".to_string()),
            kid: Some(self.api_key.clone()),
            extras,
            ..Default::default()
        };

        // Create and return the JWT
        let token = encode(&header, &claims, encoding_key)?;
        Ok(token)
    }

    async fn get_jwt_for_session_token(&self, force_refresh: bool) -> Result<String> {
        if !self.enable_coinbase {
            return Err(eyre::eyre!("Coinbase is not enabled"));
        }

        {
            let jwt = self.jwt_for_session_token.read().await;
            if let Some(token) = jwt.as_ref() {
                if !force_refresh {
                    return Ok(token.clone());
                }
            }
        }

        let uri = Self::construct_uri_to_sign(
            SESSION_TOKEN_API_METHOD,
            SESSION_TOKEN_API_HOST,
            SESSION_TOKEN_API_PATH,
        );
        let jwt = self.build_jwt(uri)?;
        *self.jwt_for_session_token.write().await = Some(jwt.clone());
        Ok(jwt)
    }

    fn construct_uri_to_sign(method: &str, host: &str, path: &str) -> String {
        format!("{} {}{}", method, host, path)
    }

    fn construct_request_url(host: &str, path: &str) -> String {
        format!("https://{}{}", host, path)
    }

    pub async fn get_session_token(
        &self,
        payload: SessionTokenApiPayload,
    ) -> Result<SessionTokenApiResponse> {
        if !self.enable_coinbase {
            return Err(eyre::eyre!("Coinbase is not enabled"));
        }

        let jwt = self.get_jwt_for_session_token(false).await?;

        let client = crate::utils::get_reqwest_client();
        let request_url =
            Self::construct_request_url(SESSION_TOKEN_API_HOST, SESSION_TOKEN_API_PATH);
        let response = client
            .post(&request_url)
            .header("Authorization", format!("Bearer {}", jwt))
            .json(&payload)
            .send()
            .await?;
        let status = response.status();
        if !status.is_success() {
            let jwt = self.get_jwt_for_session_token(true).await?;
            let response = client
                .post(&request_url)
                .header("Authorization", format!("Bearer {}", jwt))
                .json(&payload)
                .send()
                .await?;
            let status = response.status();
            if !status.is_success() {
                let body = response.text().await.unwrap_or("Unknown body".to_string());
                return Err(eyre::eyre!(
                    "Failed to get session token: {} {}",
                    status,
                    body
                ));
            }

            let session_token: SessionTokenApiResponse = response.json().await?;
            return Ok(session_token);
        }

        let session_token: SessionTokenApiResponse = response.json().await?;
        Ok(session_token)
    }

    pub async fn get_offramp_transaction_status(
        &self,
        partner_user_id: &str,
        page_key: Option<&str>,
        page_size: Option<u32>,
    ) -> Result<OfframpTransactionStatusResponse> {
        if !self.enable_coinbase {
            return Err(eyre::eyre!("Coinbase is not enabled"));
        }

        let host = "api.developer.coinbase.com";
        let path = format!("/onramp/v1/sell/user/{}/transactions", partner_user_id);
        let uri = Self::construct_uri_to_sign("GET", host, &path);
        let jwt = self.build_jwt(uri)?;
        let url = Self::construct_request_url(host, &path);

        let mut query = vec![];
        if let Some(page_key) = page_key {
            query.push(("page_key", page_key.to_string()));
        }
        if let Some(page_size) = page_size {
            query.push(("page_size", page_size.to_string()));
        }

        let client = crate::utils::get_reqwest_client();
        let response = client
            .get(&url)
            .header("Authorization", format!("Bearer {}", jwt))
            .query(&query)
            .send()
            .await?;

        let status = response.status();
        if !status.is_success() {
            let body = response.text().await.unwrap_or("Unknown body".to_string());
            return Err(eyre::eyre!(
                "Failed to get offramp transaction status: {} {}",
                status,
                body
            ));
        }

        let tx_status: OfframpTransactionStatusResponse = response.json().await?;
        Ok(tx_status)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_build_jwt() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let builder = CoinbaseJwtBuilder::get();

        let method = "GET";
        let host = "api.coinbase.com";
        let path = "/api/v3/brokerage/accounts";
        let uri = format!("{} {}{}", method, host, path);

        if builder.enable_coinbase() {
            let jwt = builder.build_jwt(uri).unwrap();
            tracing::info!("jwt: {}", jwt);

            let client = crate::utils::get_reqwest_client();
            let url = format!("https://{}{}", host, path);
            let response = client
                .get(url)
                .header("Authorization", format!("Bearer {}", jwt))
                .send()
                .await
                .unwrap();
            let status = response.status();
            tracing::info!("status: {}", status);
            assert!(status.is_success());
            let body = response.text().await.unwrap();
            tracing::info!("response: {}", body);
        } else {
            tracing::info!("Coinbase is not enabled");
        }
    }

    #[tokio::test]
    async fn test_get_buy_options() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let builder = CoinbaseJwtBuilder::get();

        let method = "GET";
        let host = "api.developer.coinbase.com";
        let path = "/onramp/v1/buy/options";
        let uri = format!("{} {}{}", method, host, path);

        if builder.enable_coinbase() {
            let jwt = builder.build_jwt(uri).unwrap();
            tracing::info!("jwt: {}", jwt);

            let client = crate::utils::get_reqwest_client();
            let url = format!("https://{}{}", host, path);
            let response = client
                .get(url)
                .header("Authorization", format!("Bearer {}", jwt))
                .query(&[("country", "US")])
                .send()
                .await
                .unwrap();
            let status = response.status();
            tracing::info!("status: {}", status);
            // assert!(status.is_success());
            let body = response.text().await.unwrap();
            tracing::info!("response: {}", body);

            // Write response to JSON file
            let json_data: serde_json::Value = serde_json::from_str(&body)
                .unwrap_or_else(|_| serde_json::json!({ "raw_response": body }));

            let filename = format!(
                "buy_options_response_{}.json",
                Utc::now().format("%Y%m%d_%H%M%S")
            );
            let json_string = serde_json::to_string_pretty(&json_data).unwrap();
            std::fs::write(&filename, json_string).unwrap();
            tracing::info!("Response written to {}", filename);
        } else {
            tracing::info!("Coinbase is not enabled");
        }
    }

    #[tokio::test]
    async fn test_get_buy_config() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let builder = CoinbaseJwtBuilder::get();

        let method = "GET";
        let host = "api.developer.coinbase.com";
        let path = "/onramp/v1/buy/config";
        let uri = format!("{} {}{}", method, host, path);

        if builder.enable_coinbase() {
            let jwt = builder.build_jwt(uri).unwrap();
            tracing::info!("jwt: {}", jwt);

            let client = crate::utils::get_reqwest_client();
            let url = format!("https://{}{}", host, path);
            let response = client
                .get(url)
                .header("Authorization", format!("Bearer {}", jwt))
                .query(&[("country", "US")])
                .send()
                .await
                .unwrap();
            let status = response.status();
            tracing::info!("status: {}", status);
            // assert!(status.is_success());
            let body = response.text().await.unwrap();
            tracing::info!("response: {}", body);

            // Write response to JSON file
            let json_data: serde_json::Value = serde_json::from_str(&body)
                .unwrap_or_else(|_| serde_json::json!({ "raw_response": body }));

            let filename = format!(
                "buy_config_response_{}.json",
                Utc::now().format("%Y%m%d_%H%M%S")
            );
            let json_string = serde_json::to_string_pretty(&json_data).unwrap();
            std::fs::write(&filename, json_string).unwrap();
            tracing::info!("Response written to {}", filename);
        } else {
            tracing::info!("Coinbase is not enabled");
        }
    }

    #[tokio::test]
    async fn test_get_session_token() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let builder = CoinbaseJwtBuilder::get();

        let address = Address {
            address: "******************************************".to_string(),
            blockchains: vec!["ethereum".to_string(), "base".to_string()],
        };
        let payload = SessionTokenApiPayload {
            addresses: vec![address],
            assets: Some(vec!["ETH".to_string(), "USDC".to_string()]),
        };

        let session_token = builder.get_session_token(payload).await.unwrap();
        tracing::info!("session_token: {:?}", session_token);
    }

    #[tokio::test]
    async fn test_get_offramp_transaction_status() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let builder = CoinbaseJwtBuilder::get();

        let partner_user_id = "1234567890";
        let page_key = None;
        let page_size = None;

        let tx_status = builder
            .get_offramp_transaction_status(partner_user_id, page_key, page_size)
            .await
            .unwrap();
        tracing::info!("tx_status: {:?}", tx_status);
    }
}

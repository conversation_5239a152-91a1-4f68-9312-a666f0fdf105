use crate::{config::config, models::User2};
use clique_wallet_signer_types::{Network, SignPayload, SignResponse};
use eyre::Result;

use crate::utils::get_signer_client;

pub async fn sign(
    user: User2,
    address: String,
    network: Network,
    message: String,
) -> Result<SignResponse> {
    let id = bs58::encode(user.id.to_bytes_le()).into_string();

    let wallet = user
        .wallets
        .iter()
        .find(|w| w.address == address && w.network == network)
        .ok_or(eyre::eyre!("Wallet not found"))?;

    let sign_payload = SignPayload {
        user: id,
        network,
        address: wallet.address.clone(),
        wallet_set: wallet.wallet_set,
        share: user.share.clone(),
        msg: message,
    };

    let client = get_signer_client();
    let sign_url = format!("{}/sign", config().signer_endpoint);
    let response = client.post(sign_url).json(&sign_payload).send().await?;
    let status = response.status();
    if !status.is_success() {
        let body = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        return Err(eyre::eyre!(
            "Failed to sign message, status: {}, body: {}",
            status,
            body
        ));
    }

    let sign_response: SignResponse = response.json().await?;
    Ok(sign_response)
}

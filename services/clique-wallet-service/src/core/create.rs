use clique_wallet_signer_types::{
    CreatePayload, CreateResponse, CreateSingleWalletParams, Network, WalletSet,
};
use eyre::{Result, eyre};
use uuid::Uuid;

use crate::{
    config::config,
    models::{
        User2, Wallet,
        enums::{SocialInfo, SocialProvider, WalletType},
    },
    storage::Storage,
    utils::get_signer_client,
};

pub const DEFAULT_FORWARD_WALLET_SET: WalletSet = WalletSet::Forward(0);

pub struct VerifiedLoginRequest {
    pub social: SocialProvider,
    pub social_info: Option<SocialInfo>,
}

pub async fn create_user(login_request: VerifiedLoginRequest) -> Result<User2> {
    let id = Uuid::new_v4();

    let create_single_wallet_params = vec![
        CreateSingleWalletParams {
            network: Network::Solana,
            wallet_set: WalletSet::Main,
        },
        CreateSingleWalletParams {
            network: Network::Ethereum,
            wallet_set: WalletSet::Main,
        },
        CreateSingleWalletParams {
            network: Network::Solana,
            wallet_set: DEFAULT_FORWARD_WALLET_SET,
        },
    ];

    let create_response = create_wallet(id, None, create_single_wallet_params).await?;

    // If the social provider is Google, we need to add the google oauth email to the social links
    // Then user can login with email or google
    let social_links = match &login_request.social {
        SocialProvider::Google(_) => {
            if let Some(SocialInfo::Google(google_user_info)) = login_request.social_info.as_ref() {
                let email = google_user_info.email.clone();
                vec![
                    SocialProvider::GoogleAccountEmail(email),
                    login_request.social.clone(),
                ]
            } else {
                return Err(eyre!("Not found Google user info"));
            }
        }
        _ => vec![login_request.social.clone()],
    };

    let wallets = create_response
        .wallets
        .into_iter()
        .map(|w| Wallet {
            address: w.address,
            network: w.network,
            wallet_set: w.wallet_set,
            wallet_type: WalletType::Embedded,
        })
        .collect();
    let share = create_response
        .share
        .ok_or(eyre!("Failed to create initial wallet"))?;

    let mut user = User2::new(id, share, wallets, social_links);

    if let Some(social_info) = login_request.social_info {
        user.update_social_info(login_request.social, social_info);
    }

    Storage::add_user2(user.clone()).await?;

    Ok(user)
}

pub async fn create_wallet(
    user_id: Uuid,
    share: Option<String>,
    create_single_wallet_params: Vec<CreateSingleWalletParams>,
) -> Result<CreateResponse> {
    let encoded_user_id = bs58::encode(user_id.to_bytes_le()).into_string();

    let create_payload = CreatePayload {
        user: encoded_user_id,
        share,
        wallets: create_single_wallet_params,
    };

    let client = get_signer_client();
    let response = client
        .post(&format!("{}/create", config().signer_endpoint))
        .json(&create_payload)
        .send()
        .await?;

    let status = response.status();
    if !status.is_success() {
        let body = response
            .text()
            .await
            .unwrap_or_else(|_| "Unknown error".to_string());
        return Err(eyre!(
            "Failed to create wallet, status: {}, body: {}",
            status,
            body
        ));
    }

    let response: CreateResponse = response.json().await?;
    Ok(response)
}

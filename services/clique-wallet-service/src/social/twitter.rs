use eyre::{Result, eyre};
use serde::{Deserialize, Serialize};

use crate::{
    config::config,
    models::OauthRequest,
    utils::{get_reqwest_client, verify_s256_code_verifier},
};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct TwitterTokenRequest {
    pub code: String,
    pub redirect_uri: String,
    pub code_verifier: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq, Eq)]
pub struct TwitterUserInfo {
    pub id: String,
    pub name: String,
    pub username: String,
}

pub async fn fetch_twitter_token(
    oauth_request: OauthRequest,
    code: String,
    code_verifier: String,
    callback_url: String,
) -> Result<String> {
    verify_s256_code_verifier(&code_verifier, &oauth_request.code_challenge)?;

    let client_id = oauth_request.client_id;
    if client_id != config().twitter_client_id {
        return Err(eyre!("Invalid client_id"));
    }
    // TODO: support multiple client_id & client_secret
    let client_secret = config().twitter_client_secret.clone();

    let params = [
        ("code", code),
        ("grant_type", "authorization_code".to_string()),
        ("client_id", client_id.clone()),
        ("redirect_uri", callback_url),
        ("code_verifier", code_verifier),
    ];

    let client = get_reqwest_client();
    let response = client
        .post("https://api.twitter.com/2/oauth2/token")
        .header("Content-Type", "application/x-www-form-urlencoded")
        .form(&params)
        .basic_auth(&client_id, Some(&client_secret))
        .send()
        .await;

    match response {
        Ok(res) => {
            if res.status().is_success() {
                let token_data: serde_json::Value = res.json().await?;
                Ok(token_data
                    .get("access_token")
                    .and_then(|v| v.as_str())
                    .ok_or(eyre!("No access token found"))?
                    .to_string())
            } else {
                let error = res.text().await?;
                Err(eyre!("Failed to fetch Twitter token: {}", error))
            }
        }
        Err(e) => Err(eyre!("Failed to fetch Twitter token response: {}", e)),
    }
}

pub async fn fetch_twitter_user_me(token: String) -> Result<TwitterUserInfo> {
    let client = get_reqwest_client();
    let response = client
        .get("https://api.twitter.com/2/users/me")
        .header("Authorization", format!("Bearer {}", token))
        .send()
        .await;

    match response {
        Ok(res) => {
            let user_data: serde_json::Value = res.json().await?;
            let data = user_data.get("data").ok_or(eyre!("No data found"))?;
            let account_info = serde_json::from_value(data.clone())?;
            Ok(account_info)
        }
        Err(e) => Err(eyre!("Failed to fetch Twitter user me: {}", e)),
    }
}

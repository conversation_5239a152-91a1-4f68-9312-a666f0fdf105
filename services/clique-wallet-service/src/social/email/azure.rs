use std::{sync::OnceLock, time::Duration};

use base64::{Engine as _, engine::general_purpose};
use chrono::{DateTime, Utc};
use eyre::{Result, eyre};
use hmac::{Hmac, Mac};
use k256::sha2::{Digest, Sha256};
use reqwest::header;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use super::email_template::EmailTemplate;
use crate::{config::config, utils::get_reqwest_client};

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
struct EmailAddress {
    email: String,
    display_name: String,
}

#[derive(Debug, Serialize)]
struct EmailRecipients {
    to: Vec<EmailAddress>,
    #[serde(rename = "CC")]
    cc: Vec<EmailAddress>,
    #[serde(rename = "bCC")]
    bcc: Vec<EmailAddress>,
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
struct EmailContent {
    subject: String,
    plain_text: String,
    html: Option<String>,
}

#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
struct EmailMessage {
    sender: String,
    recipients: EmailRecipients,
    content: EmailContent,
    headers: Option<Vec<EmailCustomHeader>>,
}

#[derive(Debug, Serialize)]
struct EmailCustomHeader {
    name: String,
    value: String,
}

#[derive(Debug, Deserialize)]
enum SendStatus {
    #[serde(rename = "Queued")]
    Queued,
    #[serde(rename = "OutForDelivery")]
    OutForDelivery,
    #[serde(rename = "Dropped")]
    Dropped,
}

#[derive(Debug, Deserialize)]
struct SendStatusResult {
    #[serde(rename = "messageId")]
    message_id: String,
    status: SendStatus,
}

pub struct AzureEmailProvider {
    // Configuration from environment variables
    endpoint: String,
    api_key: String,
    api_version: String,
    sender_address: String,
    // Derived from configuration
    host: String,
}

impl AzureEmailProvider {
    pub fn get() -> &'static Self {
        static AZURE_EMAIL_PROVIDER: OnceLock<AzureEmailProvider> = OnceLock::new();
        AZURE_EMAIL_PROVIDER.get_or_init(|| {
            let config = config();
            let endpoint = config.azure_email_service_url.clone();
            let api_key = config.azure_email_service_api_key.clone();
            let api_version = config.azure_email_service_api_version.clone();
            let sender_address = config.azure_email_service_from_address.clone();

            let host = endpoint
                .trim_start_matches("https://")
                .trim_end_matches("/")
                .to_string();
            Self {
                endpoint,
                api_key,
                api_version,
                sender_address,
                host,
            }
        })
    }

    pub async fn send_email(&self, to: &str, code: &str) -> Result<String> {
        // Prepare the email content
        let email_template = EmailTemplate::get();
        let subject = email_template.subject(code);
        let plain_text = email_template.plain_text(code);
        let html = email_template.render(code);

        let email_message = EmailMessage {
            sender: self.sender_address.clone(),
            recipients: EmailRecipients {
                to: vec![EmailAddress {
                    email: to.to_string(),
                    display_name: "".to_string(),
                }],
                cc: vec![],
                bcc: vec![],
            },
            content: EmailContent {
                subject,
                plain_text,
                html: Some(html),
            },
            headers: None,
        };

        // Serialize the email message to JSON
        let request_body = serde_json::to_vec(&email_message)?;

        // Calculate content hash
        let content_hash = calculate_content_hash(&request_body);

        // Prepare request details for authentication
        let http_method = "POST";
        let path_and_query = format!("/emails:send?api-version={}", self.api_version);

        // Generate RFC1123 timestamp
        let now: DateTime<Utc> = Utc::now();
        let rfc1123_timestamp = generate_rfc1123_timestamp(Some(now));
        let rfc7231_timestamp = generate_rfc7231_timestamp(Some(now));

        // Generate HMAC signature
        let auth_header = self.generate_authorization_header(
            http_method,
            &path_and_query,
            &rfc1123_timestamp,
            &content_hash,
        )?;

        // Construct the full URL
        let url = format!("{}{}", self.endpoint, path_and_query);

        // Generate a unique Repeatability ID
        let repeatability_id = Uuid::new_v4().to_string();

        // Send the request with proper authentication headers
        let client = get_reqwest_client();
        let response = client
            .post(&url)
            .header("x-ms-date", &rfc1123_timestamp)
            .header("x-ms-content-sha256", &content_hash)
            .header(header::AUTHORIZATION, auth_header)
            .header(header::CONTENT_TYPE, "application/json")
            .header("repeatability-request-id", repeatability_id)
            .header("repeatability-first-sent", &rfc7231_timestamp)
            .body(request_body)
            .send()
            .await?;

        // Improved error handling
        if !response.status().is_success() {
            let status = response.status();
            let body_text = response.text().await?;

            tracing::error!(
                "Email send failed with status: {}, response body: '{}'",
                status,
                body_text
            );

            return Err(eyre!("Failed to send email: {}", body_text));
        }

        let message_id: String = response
            .headers()
            .get("x-ms-request-id")
            .ok_or(eyre!("x-ms-request-id header not found in email response"))?
            .to_str()?
            .to_string();

        Ok(message_id)
    }

    pub async fn wait_for_email_delivery(
        &self,
        message_id: &str,
        timeout: Duration,
    ) -> Result<bool> {
        let max_timeout = Duration::from_secs(10);
        let check_interval = Duration::from_secs(1);
        let timeout = timeout.min(max_timeout);

        match tokio::time::timeout(timeout, async move {
            let mut attempts = 0;
            let mut interval = tokio::time::interval(check_interval);
            loop {
                attempts += 1;
                interval.tick().await;

                match self.check_email_status(message_id).await {
                    Ok(status) => match status {
                        SendStatus::OutForDelivery => {
                            tracing::debug!("Email successfully delivered: {}", message_id);
                            return true;
                        }
                        SendStatus::Dropped => {
                            tracing::debug!(
                                "Email was dropped and could not be delivered: {}",
                                message_id
                            );
                            return false;
                        }
                        SendStatus::Queued => {
                            tracing::debug!(
                                "Email is currently queued, checking again... (attempt {})",
                                attempts,
                            );
                        }
                    },
                    Err(e) => {
                        tracing::warn!("Failed to check email status: {}", e);
                    }
                }
            }
        })
        .await
        {
            Ok(result) => Ok(result),
            Err(_) => Err(eyre!(
                "Email delivery status check timed out after {} seconds",
                timeout.as_secs()
            )),
        }
    }

    async fn check_email_status(&self, message_id: &str) -> Result<SendStatus> {
        // Generate RFC1123 timestamp
        let timestamp = generate_rfc1123_timestamp(None);

        // Prepare request details for authentication
        let http_method = "GET";
        let path_and_query = format!(
            "/emails/{}/status?api-version={}",
            message_id, self.api_version
        );

        // No content for GET request, but we need an empty content hash
        let content_hash = calculate_content_hash(&[]);

        let auth_header = self.generate_authorization_header(
            http_method,
            &path_and_query,
            &timestamp,
            &content_hash,
        )?;

        // Construct the full URL
        let url = format!("{}{}", self.endpoint, path_and_query);

        // Send the GET request
        let client = get_reqwest_client();
        let response = client
            .get(&url)
            .header("x-ms-date", &timestamp)
            .header("x-ms-content-sha256", &content_hash)
            .header(header::AUTHORIZATION, auth_header)
            .send()
            .await?;

        // Handle error responses
        if !response.status().is_success() {
            let status = response.status();
            let body_text = response.text().await?;

            tracing::error!(
                "Email status check failed with status: {}, response body: '{}'",
                status,
                body_text
            );

            return Err(eyre!("Failed to check email status: {}", body_text));
        }

        // Parse the successful response
        let status_result: SendStatusResult = response.json().await?;
        tracing::debug!("Email status: {:?}", status_result.status);

        Ok(status_result.status)
    }

    fn generate_authorization_header(
        &self,
        http_method: &str,
        path_and_query: &str,
        timestamp: &str,
        content_hash: &str,
    ) -> Result<String> {
        let signature = generate_hmac_signature(
            http_method,
            path_and_query,
            timestamp,
            &self.host,
            content_hash,
            &self.api_key,
        )?;

        Ok(format!(
            "HMAC-SHA256 SignedHeaders=x-ms-date;host;x-ms-content-sha256&Signature={}",
            signature
        ))
    }
}

fn generate_rfc1123_timestamp(timestamp: Option<DateTime<Utc>>) -> String {
    if let Some(timestamp) = timestamp {
        timestamp.format("%a, %d %b %Y %H:%M:%S GMT").to_string()
    } else {
        let now: DateTime<Utc> = Utc::now();
        now.format("%a, %d %b %Y %H:%M:%S GMT").to_string()
    }
}

fn generate_rfc7231_timestamp(timestamp: Option<DateTime<Utc>>) -> String {
    if let Some(timestamp) = timestamp {
        timestamp.format("%a, %d %b %Y %H:%M:%S GMT").to_string()
    } else {
        let now: DateTime<Utc> = Utc::now();
        now.format("%a, %d %b %Y %H:%M:%S GMT").to_string()
    }
}

// Generate HMAC-SHA256 signature for Azure Communication Services
fn generate_hmac_signature(
    http_method: &str,
    path_and_query: &str,
    timestamp: &str,
    host: &str,
    content_hash: &str,
    access_key: &str,
) -> Result<String> {
    // 1. Construct the string to sign
    let string_to_sign = format!(
        "{}\n{}\n{};{};{}",
        http_method, path_and_query, timestamp, host, content_hash
    );
    tracing::debug!("string_to_sign:\n{}", string_to_sign);

    // 2. Decode the access key from Base64
    let key = general_purpose::STANDARD.decode(access_key)?;

    // 3. Create HMAC-SHA256 instance
    let mut mac = Hmac::<Sha256>::new_from_slice(&key).map_err(|e| eyre!("HMAC error: {}", e))?;

    // 4. Update with the string to sign
    mac.update(string_to_sign.as_bytes());

    // 5. Get the result and encode as Base64
    let result = mac.finalize();
    let signature = general_purpose::STANDARD.encode(result.into_bytes());

    Ok(signature)
}

// Calculate SHA256 hash of content and encode as Base64
fn calculate_content_hash(content: &[u8]) -> String {
    let mut hasher = Sha256::new();
    hasher.update(content);
    let hash = hasher.finalize();
    general_purpose::STANDARD.encode(hash)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio;

    fn setup() {
        dotenv::dotenv().ok();

        tracing_subscriber::fmt()
            .with_max_level(tracing::Level::INFO)
            .try_init()
            .ok(); // Ignore errors if logger is already initialized
    }

    #[tokio::test]
    #[ignore] // This test is ignored by default as it makes a real API call
    async fn test_send_email() {
        setup();

        // Check if all required environment variables are set
        let test_recipient = match std::env::var("TEST_RECIPIENT_EMAIL") {
            Ok(email) => email,
            Err(_) => {
                println!("Skipping test_send_email: TEST_RECIPIENT_EMAIL not set");
                return;
            }
        };

        let provider = AzureEmailProvider::get();

        let code = "123456";
        let result = provider.send_email(&test_recipient, &code).await;

        assert!(result.is_ok(), "send_email failed: {:?}", result.err());

        let message_id = result.unwrap();
        let result = provider
            .wait_for_email_delivery(&message_id, Duration::from_secs(10))
            .await;
        assert!(
            result.is_ok(),
            "wait_for_email_delivery failed: {:?}",
            result.err()
        );
        assert!(result.unwrap(), "Email was not delivered");
    }

    #[test]
    fn test_generate_hmac_signature() {
        setup();

        let http_method = "POST";
        let uri_path_and_query = "/emails:send?api-version=2023-03-31";
        let timestamp = "Wed, 21 Oct 2023 07:28:00 GMT";
        let host = "example.communication.azure.com";
        let content_hash = "hBJLt1VpGdRv24a4SJMAG2obrGtab8RYf8mzKZQYXzE=";
        let access_key = "dGVzdGtleQ=="; // Base64 encoded "testkey"

        let result = generate_hmac_signature(
            http_method,
            uri_path_and_query,
            timestamp,
            host,
            content_hash,
            access_key,
        );

        assert!(
            result.is_ok(),
            "generate_hmac_signature failed: {:?}",
            result.err()
        );
        // We can't easily predict the exact signature without reimplementing the algorithm,
        // so we just check that it returns a non-empty string
        let signature = result.unwrap();
        assert!(!signature.is_empty());
    }
}

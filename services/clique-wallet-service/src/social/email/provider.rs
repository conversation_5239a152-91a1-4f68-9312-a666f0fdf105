use std::{str::FromStr, time::Duration};

use email_address::EmailAddress;
use eyre::Result;
use rand::Rng;

use super::{azure::AzureEmailProvider, mailtrap::MailtrapEmailProvider};
use crate::config::config;

pub struct EmailProvider {}

impl EmailProvider {
    pub async fn send_email(to: &str, code: &str, preferred_backup_provider: bool) -> Result<()> {
        let config = config();
        if !config.enable_email {
            return Err(eyre::eyre!("Email is not enabled"));
        }

        let timeout = Duration::from_secs(10);
        if preferred_backup_provider {
            if let Ok((is_success, should_retry_other_providers)) =
                Self::send_email_with_mailtrap(to, code).await
            {
                if is_success {
                    return Ok(());
                }
                if !should_retry_other_providers {
                    return Err(eyre::eyre!("Unsupported or invalid email domain"));
                }
            }

            if let Ok(true) = Self::send_email_with_azure(to, code, timeout).await {
                return Ok(());
            }
        } else {
            if let Ok(true) = Self::send_email_with_azure(to, code, timeout).await {
                return Ok(());
            }
            if let Ok((true, _)) = Self::send_email_with_mailtrap(to, code).await {
                return Ok(());
            }
        }

        Err(eyre::eyre!("Send email failed"))
    }

    async fn send_email_with_mailtrap(to: &str, code: &str) -> Result<(bool, bool)> {
        if config().enable_mailtrap_email {
            let mailtrap_provider = MailtrapEmailProvider::get();
            let result = mailtrap_provider.send_email(to, code).await?;
            if result.is_success() {
                return Ok((true, false));
            }
            if result.should_retry_self() {
                // only retry once after a delay
                tokio::time::sleep(Duration::from_secs(1)).await;
                let result = mailtrap_provider.send_email(to, code).await?;
                if result.is_success() {
                    return Ok((true, false));
                }
                return Ok((false, result.should_retry_other_providers()));
            }
            return Ok((false, result.should_retry_other_providers()));
        }

        Ok((false, true))
    }

    async fn send_email_with_azure(to: &str, code: &str, timeout: Duration) -> Result<bool> {
        if config().enable_azure_email {
            let azure_provider = AzureEmailProvider::get();
            let message_id = azure_provider.send_email(to, code).await?;
            let success = azure_provider
                .wait_for_email_delivery(&message_id, timeout)
                .await?;
            Ok(success)
        } else {
            Ok(false)
        }
    }

    pub fn generate_verification_code() -> String {
        let mut rng = rand::thread_rng();
        format!("{:06}", rng.gen_range(0..999999))
    }

    pub fn validate_email_address(email: &str) -> bool {
        match EmailAddress::from_str(email) {
            Ok(_) => true,
            Err(_) => false,
        }
    }
}

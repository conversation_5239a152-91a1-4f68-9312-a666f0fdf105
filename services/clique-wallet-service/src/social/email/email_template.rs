use std::sync::OnceLock;

use eyre::Result;

use crate::config::config;

#[derive(Debug, <PERSON>lone)]
pub struct EmailTemplate {
    pub wallet_name: String,
    pub html_template: String,
}

impl EmailTemplate {
    pub fn new(wallet_name: &str, html_template: String) -> Result<Self> {
        Self::verify_template(&html_template)?;

        let template = Self {
            wallet_name: wallet_name.to_string(),
            html_template,
        };
        Ok(template)
    }

    pub fn get() -> &'static Self {
        static EMAIL_TEMPLATE: OnceLock<EmailTemplate> = OnceLock::new();
        EMAIL_TEMPLATE.get_or_init(|| {
            let wallet_name = config().wallet_name.clone();
            let email_template_str = config()
                .email_template_str
                .clone()
                .expect("Email template is not set");
            Self::new(&wallet_name, email_template_str).expect("Failed to create email template")
        })
    }

    pub fn wallet_name(&self) -> &str {
        &self.wallet_name
    }

    pub fn subject(&self, code: &str) -> String {
        format!("{} is your login code for {}", code, self.wallet_name())
    }

    pub fn plain_text(&self, code: &str) -> String {
        format!("Your login code is: {}", code)
    }

    pub fn render(&self, code: &str) -> String {
        self.html_template
            .replace("{{wallet_name}}", &self.wallet_name)
            .replace("{{verification_code}}", code)
    }

    pub fn verify_template(html_template: &str) -> Result<()> {
        // TODO: use a html parser to verify the template
        if !html_template.contains("{{wallet_name}}") {
            return Err(eyre::eyre!(
                "HTML template does not contain {{wallet_name}}"
            ));
        }
        if !html_template.contains("{{verification_code}}") {
            return Err(eyre::eyre!(
                "HTML template does not contain {{verification_code}}"
            ));
        }

        Ok(())
    }
}

use std::sync::OnceLock;

use eyre::Result;
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};

use crate::{config, social::email::email_template::EmailTemplate, utils::get_reqwest_client};

#[derive(Serialize, Deserialize)]
struct EmailAddress {
    email: String,
    name: Option<String>,
}

#[derive(Serialize, Deserialize)]
struct SendEmailRequest {
    from: EmailAddress,
    to: Vec<EmailAddress>,
    subject: String,
    text: String,
    html: String,
}

#[derive(Serialize, Deserialize, Debug)]
struct SendEmailResponse {
    success: bool,
    message_ids: Option<Vec<String>>,
    errors: Option<Vec<String>>,
}

#[derive(Debug)]
pub struct SendEmailResult {
    pub success: bool,
    pub status_code: StatusCode,
}

impl SendEmailResult {
    pub fn is_success(&self) -> bool {
        self.success
    }

    pub fn should_retry_self(&self) -> bool {
        match self.status_code {
            StatusCode::BAD_REQUEST => false,
            StatusCode::UNAUTHORIZED => false,
            StatusCode::FORBIDDEN => false,
            StatusCode::INTERNAL_SERVER_ERROR => true,
            _ => false,
        }
    }

    pub fn should_retry_other_providers(&self) -> bool {
        match self.status_code {
            // Bad request. Fix errors listed in response before retrying.
            StatusCode::BAD_REQUEST => true,
            // Unauthorized. Make sure you are sending correct credentials with the request before
            // retrying.
            StatusCode::UNAUTHORIZED => true,
            // Forbidden. Make sure domain verification process is completed.
            StatusCode::FORBIDDEN => false,
            // Internal error. Mail was not delivered. Retry later or contact support.
            StatusCode::INTERNAL_SERVER_ERROR => true,
            _ => false,
        }
    }
}

pub struct MailtrapEmailProvider {
    api_key: String,
    from_address: String,
    from_name: String,
}

impl MailtrapEmailProvider {
    pub fn get() -> &'static Self {
        static MAILTRAP_EMAIL_PROVIDER: OnceLock<MailtrapEmailProvider> = OnceLock::new();
        MAILTRAP_EMAIL_PROVIDER.get_or_init(|| {
            let config = config();
            let api_key = config.mailtrap_email_service_api_key.clone();
            let from_address = config.mailtrap_email_service_from_address.clone();
            let from_name = config.wallet_name.clone();
            Self {
                api_key,
                from_address,
                from_name,
            }
        })
    }

    pub async fn send_email(&self, to: &str, code: &str) -> Result<SendEmailResult> {
        // Prepare the email content
        let email_template = EmailTemplate::get();
        let subject = email_template.subject(code);
        let plain_text = email_template.plain_text(code);
        let html = email_template.render(code);

        let from_address = EmailAddress {
            email: self.from_address.clone(),
            name: Some(self.from_name.clone()),
        };

        let to_address = EmailAddress {
            email: to.to_string(),
            name: None,
        };

        let send_email_request = SendEmailRequest {
            from: from_address,
            to: vec![to_address],
            subject,
            text: plain_text,
            html,
        };

        let client = get_reqwest_client();
        let response = client
            .post("https://send.api.mailtrap.io/api/send")
            .header("Content-Type", "application/json")
            .header("Accept", "application/json")
            .header("Api-Token", self.api_key.clone())
            .json(&send_email_request)
            .send()
            .await?;

        let status = response.status();
        let response_body = response.json::<SendEmailResponse>().await?;

        Ok(SendEmailResult {
            success: response_body.success,
            status_code: status,
        })
    }
}

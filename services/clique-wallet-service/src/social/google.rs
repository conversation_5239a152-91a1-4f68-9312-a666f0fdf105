use base64::{Engine, engine::general_purpose};
use eyre::{Result, eyre};
use serde::{Deserialize, Serialize};

use crate::{
    config::config,
    models::OauthRequest,
    utils::{get_reqwest_client, verify_s256_code_verifier},
};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct GoogleTokenRequest {
    pub code: String,
    pub redirect_uri: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct GoogleClaims {
    email: String,
    email_verified: bool,
    name: String,
    picture: Option<String>,
    given_name: Option<String>,
    family_name: Option<String>,
    hd: Option<String>,
    sub: String, /* This is the unique Google ID
                  * Add other fields as needed */
}

#[derive(Debug, Serialize, Deserialize)]
pub struct GoogleTokenResponse {
    pub access_token: String,
    pub id_token: String,
    pub scope: String,
    pub token_type: String,
    pub expires_in: u64,
}

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize, PartialEq, Eq)]
pub struct GoogleUserInfo {
    pub id: String,
    pub name: String,
    pub email: String,
    pub picture: Option<String>,
    pub given_name: Option<String>,
    pub family_name: Option<String>,
}

pub async fn fetch_google_token(
    oauth_request: OauthRequest,
    code: String,
    code_verifier: String,
    callback_url: String,
) -> Result<GoogleUserInfo> {
    verify_s256_code_verifier(&code_verifier, &oauth_request.code_challenge)?;

    let client_id = oauth_request.client_id;
    if client_id != config().google_client_id {
        return Err(eyre!("Invalid client_id"));
    }
    // TODO: support multiple client_id & client_secret
    let client_secret = config().google_client_secret.clone();

    let params = [
        ("code", code),
        ("client_id", client_id.clone()),
        ("client_secret", client_secret.clone()),
        ("grant_type", "authorization_code".to_string()),
        ("redirect_uri", callback_url),
        ("code_verifier", code_verifier),
    ];

    let client = get_reqwest_client();
    let response = client
        .post("https://oauth2.googleapis.com/token")
        .header("Content-Type", "application/x-www-form-urlencoded")
        .form(&params)
        .send()
        .await;

    match response {
        Ok(res) => {
            if res.status().is_success() {
                let token_data: GoogleTokenResponse = res.json().await?;
                tracing::debug!("Google token data: {:?}", token_data);

                let id_token = token_data.id_token.clone();

                // Note: In production, you should verify the token signature
                // This is a simplified example that just decodes the payload
                let parts: Vec<&str> = id_token.split('.').collect();
                if parts.len() != 3 {
                    return Err(eyre!("Invalid JWT format"));
                }

                let payload = general_purpose::URL_SAFE_NO_PAD.decode(parts[1])?;
                let claims: GoogleClaims = serde_json::from_slice(&payload)?;

                Ok(GoogleUserInfo {
                    id: claims.sub,
                    name: claims.name,
                    email: claims.email,
                    picture: claims.picture,
                    given_name: claims.given_name,
                    family_name: claims.family_name,
                })
            } else {
                let error = res.text().await?;
                Err(eyre!("Failed to fetch Google token: {}", error))
            }
        }
        Err(e) => Err(eyre!("Failed to fetch Google token response: {}", e)),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_decode_google_jwt() {
        // Sample JWT from the logs
        let id_token = "eyJhbGciOiJSUzI1NiIsImtpZCI6IjI1ZjgyMTE3MTM3ODhiNjE0NTQ3NGI1MDI5YjAxNDFiZDViM2RlOWMiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cxd_ZC5qIsOJXBRuZzCXs3BrWIUztf3IEKa9_0edU8I-uIleG9OlxiWmF1HQH2jmUzNkX6_xyW-URPonEOxsoSXKGu_HowguqtZk9SK_5qFCmpwYPnYOxQpmJZO9hbn-bqsdQkBa5Opm6LY5Wb2_aGuhMvIwV0QTqOXbp0LezgaTUmytiZXd5qUuO-FCh-MHBWqXCh34atMKOXV6zyO33Ai0JNPsmsiCWhR5LOKP3CLUjnxbmk2knwhfsBdr-xLmNfq8Qe0mhBqwk3a8Vkitu3zqq27KWUJN2AjFpNSExxGSTwvz-AiGU9wrnDGwA1WDnQ09-GSWICr2exIT3SlYkA";

        // Split the token to get parts
        let parts: Vec<&str> = id_token.split('.').collect();
        assert_eq!(parts.len(), 3, "JWT should have 3 parts");

        // Decode the payload
        let payload = general_purpose::URL_SAFE_NO_PAD
            .decode(parts[1])
            .expect("Failed to decode payload");
        let claims: GoogleClaims =
            serde_json::from_slice(&payload).expect("Failed to parse claims");

        // Verify extracted claims
        assert_eq!(claims.email, "<EMAIL>");
        assert_eq!(claims.name, "Stuart Wang");
        assert!(claims.email_verified);
        assert_eq!(claims.sub, "116755846230499577598");

        // Log the decoded information
        println!("Successfully decoded JWT:");
        println!("Email: {}", claims.email);
        println!("Name: {}", claims.name);
        println!("Picture URL: {:?}", claims.picture);
    }
}

use std::sync::OnceLock;

use eyre::{Result, eyre};
use reqwest::header;
use serde::{Deserialize, Serialize};

use crate::utils::get_reqwest_client;

pub const PRELUDE_API_URL: &str = "https://api.prelude.dev/v2";

#[derive(Debug, Serialize)]
pub struct VerificationRequest {
    pub target: Target,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub options: Option<VerificationOptions>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<Metadata>,
}

#[derive(Debug, Serialize)]
pub struct Target {
    #[serde(rename = "type")]
    pub target_type: String,
    pub value: String,
}

#[derive(Debug, Serialize)]
pub struct VerificationOptions {
    pub code_size: u8,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Metadata {
    pub correlation_id: Option<String>,
}

#[derive(Debug, Deserialize, PartialEq, Eq, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum VerificationStatus {
    #[serde(rename = "success")]
    Success,
    #[serde(rename = "retry")]
    Retry,
    #[serde(rename = "blocked")]
    Blocked,
}

#[derive(Debug, Deserialize, PartialEq, Eq, Clone, Copy)]
pub enum VerificationMethod {
    #[serde(rename = "message")]
    Message,
    #[serde(rename = "silent")]
    Silent,
    #[serde(rename = "voice")]
    Voice,
}

#[derive(Debug, Deserialize, PartialEq, Eq, Clone, Copy)]
pub enum VerificationReason {
    #[serde(rename = "suspicious")]
    Suspicious,
    #[serde(rename = "repeated_attempts")]
    RepeatedAttempts,
    #[serde(rename = "invalid_phone_line")]
    InvalidPhoneLine,
    #[serde(rename = "invalid_phone_number")]
    InvalidPhoneNumber,
    #[serde(rename = "in_block_list")]
    InBlockList,
}

#[derive(Debug, Deserialize)]
pub struct VerificationResponse {
    pub id: String,
    pub status: VerificationStatus,
    pub method: VerificationMethod,
    pub reason: Option<String>,
    pub channels: Option<Vec<String>>,
    pub metadata: Option<Metadata>,
    pub request_id: Option<String>,
}

impl VerificationResponse {
    pub fn is_success(&self) -> bool {
        self.status == VerificationStatus::Success
    }

    pub fn is_blocked(&self) -> bool {
        self.status == VerificationStatus::Blocked
    }

    pub fn is_retry(&self) -> bool {
        self.status == VerificationStatus::Retry
    }

    pub fn error_message(&self) -> Option<String> {
        match self.status {
            VerificationStatus::Success => None,
            VerificationStatus::Retry => Some("Please try again later".to_string()),
            VerificationStatus::Blocked => Some("Your phone number has been blocked".to_string()),
        }
    }
}

#[derive(Debug, Serialize)]
pub struct CheckCodeRequest {
    pub target: Target,
    pub code: String,
}

#[derive(Debug, Deserialize, PartialEq, Eq, Clone, Copy)]
pub enum CheckCodeStatus {
    #[serde(rename = "success")]
    Success,
    #[serde(rename = "failure")]
    Failure,
    #[serde(rename = "expired_or_not_found")]
    ExpiredOrNotFound,
}

#[derive(Debug, Deserialize)]
pub struct CheckCodeResponse {
    pub id: String,
    pub status: CheckCodeStatus,
    pub metadata: Option<Metadata>,
    pub request_id: Option<String>,
}

impl CheckCodeResponse {
    pub fn is_verified(&self) -> bool {
        self.status == CheckCodeStatus::Success
    }

    pub fn error_message(&self) -> Option<String> {
        match self.status {
            CheckCodeStatus::Success => None,
            CheckCodeStatus::Failure => Some("Invalid verification code".to_string()),
            CheckCodeStatus::ExpiredOrNotFound => {
                Some("Verification code expired or not found".to_string())
            }
        }
    }
}

pub struct PreludeProvider {
    pub enabled: bool,
    pub api_key: String,
}

impl PreludeProvider {
    pub fn get() -> &'static Self {
        static INSTANCE: OnceLock<PreludeProvider> = OnceLock::new();
        INSTANCE.get_or_init(|| {
            let config = crate::config::config();
            PreludeProvider::new(config.sms_prelude_api_key.clone(), config.enable_sms)
        })
    }

    pub fn new(api_key: String, enabled: bool) -> Self {
        Self { api_key, enabled }
    }

    pub fn is_enabled(&self) -> bool {
        self.enabled
    }

    async fn send_sms_verification_request(
        &self,
        client: &reqwest::Client,
        request: &VerificationRequest,
    ) -> Result<VerificationResponse> {
        let response = client
            .post(format!("{}/verification", PRELUDE_API_URL))
            .header(header::AUTHORIZATION, format!("Bearer {}", self.api_key))
            .header(header::CONTENT_TYPE, "application/json")
            .json(request)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(eyre!("Failed to send SMS verification: {}", error_text));
        }

        let verification: VerificationResponse = response.json().await?;
        Ok(verification)
    }

    /// Send SMS verification to a phone number
    /// Returns the verification ID that can be used to check the OTP code
    pub async fn send_sms_verification(&self, phone_number: &str) -> Result<VerificationResponse> {
        if !self.enabled {
            return Err(eyre!("SMS verification is not enabled"));
        }

        let request = VerificationRequest {
            target: Target {
                target_type: "phone_number".to_string(),
                value: phone_number.to_string(),
            },
            options: Some(VerificationOptions { code_size: 6 }),
            metadata: None,
        };

        let client = get_reqwest_client();
        let mut verification = self
            .send_sms_verification_request(&client, &request)
            .await?;

        let mut retry_count = 0;
        while verification.is_retry() {
            tokio::time::sleep(std::time::Duration::from_millis(100)).await;
            verification = self
                .send_sms_verification_request(&client, &request)
                .await?;

            retry_count += 1;
            if retry_count > 3 {
                return Err(eyre!("Failed to send SMS verification: too many retries"));
            }
        }

        Ok(verification)
    }

    /// Check the OTP code for a phone number
    /// Returns true if the code is valid, false otherwise
    pub async fn check_otp_code(
        &self,
        phone_number: &str,
        code: &str,
    ) -> Result<CheckCodeResponse> {
        if !self.enabled {
            return Err(eyre!("SMS verification is not enabled"));
        }

        let request = CheckCodeRequest {
            target: Target {
                target_type: "phone_number".to_string(),
                value: phone_number.to_string(),
            },
            code: code.to_string(),
        };

        let client = get_reqwest_client();
        let response = client
            .post(format!("{}/verification/check", PRELUDE_API_URL))
            .header(header::AUTHORIZATION, format!("Bearer {}", self.api_key))
            .header(header::CONTENT_TYPE, "application/json")
            .json(&request)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(eyre!("Failed to check OTP code: {}", error_text));
        }

        let check_response: CheckCodeResponse = response.json().await?;
        Ok(check_response)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_send_sms_verification() {
        dotenv::dotenv().ok();
        crate::utils::setup_tracing();

        let config = crate::config::Config::from_env();
        if !config.enable_sms {
            return;
        }

        let mut phone_number = String::new();
        tracing::info!("Enter phone number:");
        std::io::stdin()
            .read_line(&mut phone_number)
            .expect("Failed to read input");
        let phone_number = phone_number.trim();

        let provider = PreludeProvider::get();
        let verification_response = provider.send_sms_verification(phone_number).await.unwrap();
        tracing::info!("Response: {:?}", verification_response);

        let mut code = String::new();
        tracing::info!("Enter OTP code:");
        std::io::stdin()
            .read_line(&mut code)
            .expect("Failed to read input");
        let code = code.trim();

        let check_response = provider.check_otp_code(phone_number, &code).await.unwrap();
        tracing::info!("Check response: {:?}", check_response);
        assert_eq!(check_response.status, CheckCodeStatus::Success);
        assert_eq!(check_response.id, verification_response.id);
    }
}

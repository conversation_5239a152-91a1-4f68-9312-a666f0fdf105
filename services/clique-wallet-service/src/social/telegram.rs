use std::sync::OnceLock;

use eyre::Result;
use hex;
use hmac::{Hmac, Mac};
use k256::sha2::{Digest, Sha256};
use serde::{Deserialize, Serialize};

use crate::{config::config, utils::get_current_timestamp};

#[derive(<PERSON><PERSON>, Debug, Serialize, Deserialize, PartialEq, Eq)]
pub struct TelegramUserInfo {
    pub id: u64,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub username: Option<String>,
    pub photo_url: Option<String>,
}

impl From<TelegramAuthData> for TelegramUserInfo {
    fn from(auth_data: TelegramAuthData) -> Self {
        Self {
            id: auth_data.id,
            first_name: auth_data.first_name,
            last_name: auth_data.last_name,
            username: auth_data.username,
            photo_url: auth_data.photo_url,
        }
    }
}

#[derive(Debug, Deserialize, Serialize)]
pub struct TelegramAuthData {
    pub id: u64,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub username: Option<String>,
    pub photo_url: Option<String>,
    pub auth_date: u64,
    pub hash: String,
}

impl TelegramAuthData {
    pub fn data_check_string(&self) -> String {
        let mut data_check_string = String::new();
        // auth_date
        data_check_string.push_str(format!("auth_date={}\n", self.auth_date).as_str());
        // first_name
        if let Some(first_name) = &self.first_name {
            data_check_string.push_str(format!("first_name={}\n", first_name).as_str());
        }
        // id
        data_check_string.push_str(format!("id={}\n", self.id).as_str());
        // last_name
        if let Some(last_name) = &self.last_name {
            data_check_string.push_str(format!("last_name={}\n", last_name).as_str());
        }
        // photo_url
        if let Some(photo_url) = &self.photo_url {
            data_check_string.push_str(format!("photo_url={}\n", photo_url).as_str());
        }
        // username
        if let Some(username) = &self.username {
            data_check_string.push_str(format!("username={}\n", username).as_str());
        }

        // remove the last \n
        data_check_string.pop();

        data_check_string
    }
}

#[derive(Debug, Clone)]
pub struct TelegramAdapter {
    pub enabled: bool,
    pub bot_token: String,
    pub bot_name: String,
    pub secret_key: [u8; 32],

    pub outer_bot_token: String,
    pub outer_secret_key: [u8; 32],
}

impl TelegramAdapter {
    const MAX_AUTH_AGE: u64 = 10 * 60; // 10 minutes in seconds

    pub fn get() -> &'static Self {
        static INSTANCE: OnceLock<TelegramAdapter> = OnceLock::new();
        INSTANCE.get_or_init(|| {
            let enabled = config().enable_telegram_login || config().enable_telegram_bind;
            let bot_token = config().telegram_bot_token.clone();
            let bot_name = config().telegram_bot_name.clone();

            let mut hasher = Sha256::new();
            hasher.update(bot_token.as_bytes());
            let secret_key = hasher.finalize().into();

            let outer_bot_token = config().telegram_outer_bot_token.clone();
            let mut hasher = Sha256::new();
            hasher.update(outer_bot_token.as_bytes());
            let outer_secret_key = hasher.finalize().into();

            Self {
                enabled,
                bot_token,
                bot_name,
                secret_key,
                outer_bot_token,
                outer_secret_key,
            }
        })
    }

    pub fn is_enabled(&self) -> bool {
        self.enabled && !self.bot_token.is_empty()
    }

    pub fn bot_name(&self) -> &str {
        &self.bot_name
    }

    /// Verifies that the hash in the Telegram auth object is valid.
    ///
    /// The algorithm from the Telegram docs:
    ///
    ///  - secret_key = SHA256(<bot_token>)
    ///  - hex(HMAC_SHA256(data_check_string, secret_key)) == hash
    /// ref: https://core.telegram.org/widgets/login#checking-authorization
    pub async fn verify_telegram_auth(
        &self,
        auth_data: &TelegramAuthData,
        use_outer_bot: bool,
    ) -> Result<bool> {
        // Step 1: Create data_check_string
        let data_check_string = auth_data.data_check_string();

        // Step 2: Calculate HMAC-SHA256(data_check_string, secret_key)
        let mut mac = Hmac::<Sha256>::new_from_slice(if use_outer_bot {
            &self.outer_secret_key
        } else {
            &self.secret_key
        })
        .map_err(|e| eyre::eyre!("Failed to create HMAC: {}", e))?;

        mac.update(data_check_string.as_bytes());
        let hmac_result = mac.finalize();

        // Step 3: Convert to hex and compare with hash
        let calculated_hash = hex::encode(hmac_result.into_bytes());

        if calculated_hash != auth_data.hash {
            return Ok(false);
        }

        // Step 4: Check if auth_date is not too old (e.g., within 10 minutes)
        let current_time = get_current_timestamp();

        let auth_age = current_time.saturating_sub(auth_data.auth_date);

        if auth_data.auth_date + Self::MAX_AUTH_AGE < current_time {
            return Err(eyre::eyre!(
                "Auth data is too old. Age: {} seconds, Max allowed: {} seconds",
                auth_age,
                Self::MAX_AUTH_AGE
            ));
        }

        Ok(true)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_data_check_string() {
        let auth_data = TelegramAuthData {
            id: 123456789,
            first_name: Some("John".to_string()),
            last_name: Some("Doe".to_string()),
            username: Some("johndoe".to_string()),
            photo_url: Some("https://t.me/i/userpic/320/johndoe.jpg".to_string()),
            auth_date: 1640995200,
            hash: "test_hash".to_string(),
        };

        let expected = "auth_date=1640995200\nfirst_name=John\nid=123456789\nlast_name=Doe\nphoto_url=https://t.me/i/userpic/320/johndoe.jpg\nusername=johndoe";
        assert_eq!(auth_data.data_check_string(), expected);
    }
}

use std::sync::OnceLock;

use eyre::{Result, eyre};
use serde::{Deserialize, Serialize};
use solana_sdk::{pubkey::Pubkey, signature::Signature};
use uuid::Uuid;

use crate::config::config;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PhantomChallenge {
    pub challenge: String,
    pub nonce: String,
    pub expires_at: i64,
}

pub struct PhantomAdapter {
    enabled: bool,
    wallet_name: String,
}

impl PhantomAdapter {
    pub fn get() -> &'static Self {
        static INSTANCE: OnceLock<PhantomAdapter> = OnceLock::new();
        INSTANCE.get_or_init(|| {
            let enable_phantom = config().enable_phantom;
            let wallet_name = config().wallet_name.clone();
            PhantomAdapter::new(enable_phantom, wallet_name)
        })
    }

    pub fn new(enable_phantom: bool, wallet_name: String) -> Self {
        Self {
            enabled: enable_phantom,
            wallet_name,
        }
    }

    pub fn is_enabled(&self) -> bool {
        self.enabled
    }

    pub async fn generate_auth_challenge(
        &self,
        wallet_address: Pubkey,
    ) -> Result<PhantomChallenge> {
        if !self.enabled {
            return Err(eyre!("Phantom login is not enabled"));
        }

        // Generate unique nonce
        let nonce = Uuid::new_v4().to_string();

        let timestamp = chrono::Utc::now();
        let timestamp_str = timestamp.format("%Y-%m-%d %H:%M:%S GMT").to_string();
        let timestamp_secs = timestamp.timestamp();

        // Generate challenge message
        let challenge = format!(
            "Welcome to {} Wallet!
    
Please sign this message to authenticate with your phantom wallet.

Wallet: {}
Nonce: {}
Timestamp: {}

This signature will be used to authenticate you to the application.",
            self.wallet_name, wallet_address, nonce, timestamp_str,
        );

        // Set expiration time (5 minutes from now)
        let expires_at = timestamp_secs + 300; // 5 minutes

        Ok(PhantomChallenge {
            challenge,
            nonce,
            expires_at,
        })
    }

    pub async fn verify_phantom_signature(
        &self,
        wallet_address: Pubkey,
        challenge: &str,
        signature: Signature,
    ) -> Result<String> {
        // Verify the signature
        if !signature.verify(wallet_address.as_ref(), challenge.as_bytes()) {
            return Err(eyre!("Invalid signature"));
        }

        // Verify wallet address in challenge
        if !challenge.contains(&wallet_address.to_string()) {
            return Err(eyre!("Wallet address mismatch in challenge"));
        }

        Ok(wallet_address.to_string())
    }
}

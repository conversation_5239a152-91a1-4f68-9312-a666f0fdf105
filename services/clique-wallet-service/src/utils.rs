use std::{
    sync::OnceLock,
    time::{SystemTime, UNIX_EPOCH},
};

use base64::Engine;
use eyre::eyre;
use k256::sha2::{Digest, Sha256};
use reqwest::Client;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

use crate::config::config;

pub fn setup_tracing() {
    let _ = tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new(
            std::env::var("RUST_LOG").unwrap_or_else(|_| "info".into()),
        ))
        .with(tracing_subscriber::fmt::layer())
        .try_init();
}

pub fn get_current_timestamp() -> u64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .expect("Time went backwards")
        .as_secs()
}

pub fn is_unique_violation(error: &str) -> bool {
    // e.g. "duplicate key value violates unique constraint \"sessions_pkey\""
    error.contains("23505") || error.contains("duplicate key value")
}

pub fn get_reqwest_client() -> &'static Client {
    static CLIENT: OnceLock<Client> = OnceLock::new();
    CLIENT.get_or_init(|| Client::new())
}

pub fn get_signer_client() -> &'static Client {
    static CLIENT: OnceLock<Client> = OnceLock::new();
    CLIENT.get_or_init(|| {
        let tls_config = config().tls_client_config.clone();
        Client::builder()
            .use_preconfigured_tls(tls_config)
            .build()
            .expect("Failed to build signer client")
    })
}

pub fn verify_s256_code_verifier(
    code_verifier: &str,
    code_challenge: &str,
) -> Result<(), eyre::Report> {
    let mut hasher = Sha256::new();
    hasher.update(code_verifier.as_bytes());
    let calculated_challenge =
        base64::engine::general_purpose::URL_SAFE_NO_PAD.encode(hasher.finalize());

    if calculated_challenge != code_challenge {
        return Err(eyre!("Code verifier does not match challenge"));
    }

    Ok(())
}

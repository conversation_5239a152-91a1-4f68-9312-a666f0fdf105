use axum::{<PERSON><PERSON>, response::IntoResponse};
use reqwest::StatusCode;
use uuid::Uuid;

use crate::{
    core::sign::sign,
    handler::{ErrorResponse, SignRequest},
    storage::Storage,
};

pub async fn admin_sign(Json(admin_sign_request): <PERSON><PERSON><SignRequest>) -> impl IntoResponse {
    let user_id = match Uuid::parse_str(&admin_sign_request.user_id) {
        Ok(user_id) => user_id,
        Err(_) => {
            return (
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "Invalid user ID".to_string(),
                }),
            )
                .into_response();
        }
    };

    let user = Storage::get_user2(user_id).await;
    let user = match user {
        Ok(Some(user)) => user,
        _ => {
            return (
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "User not found".to_string(),
                }),
            )
                .into_response();
        }
    };

    let has_corresbonding_wallet = user.wallets.iter().any(|w| {
        w.address == admin_sign_request.address && w.network == admin_sign_request.network
    });
    if !has_corresbonding_wallet {
        return (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Address mismatch".to_string(),
            }),
        )
            .into_response();
    }

    match sign(
        user,
        admin_sign_request.address,
        admin_sign_request.network,
        admin_sign_request.message,
    )
    .await
    {
        Ok(response) => (StatusCode::OK, Json(response)).into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: e.to_string(),
            }),
        )
            .into_response(),
    }
}

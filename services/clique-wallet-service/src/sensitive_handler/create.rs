use axum::{<PERSON><PERSON>, response::IntoResponse};
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};

use crate::{
    core::create::VerifiedLoginRequest,
    models::{UserResponse, enums::SocialProvider},
};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AdminCreateUserRequest {
    pub identifier: String,
}

pub async fn admin_create_user(Json(payload): J<PERSON><AdminCreateUserRequest>) -> impl IntoResponse {
    let user = match crate::core::create::create_user(VerifiedLoginRequest {
        social: SocialProvider::Admin(payload.identifier),
        social_info: None,
    })
    .await
    {
        Ok(user) => user,
        Err(e) => {
            tracing::error!("Failed to create user: {}", e);
            return (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json("Failed to create user"),
            )
                .into_response();
        }
    };
    let user_response = UserResponse::from(user);

    (StatusCode::OK, <PERSON><PERSON>(user_response)).into_response()
}

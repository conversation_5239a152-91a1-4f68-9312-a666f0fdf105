use axum::{<PERSON><PERSON>, extract::Path};
use eyre::Result;
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::{handler::ErrorResponse, models::WalletResponse, storage::Storage};

#[derive(Debug, Serialize, Deserialize)]
pub struct InfoResponse {
    pub id: Uuid,
    pub wallets: Vec<WalletResponse>,
}

pub async fn handle_info(
    Path(user_id): Path<Uuid>,
) -> Result<Json<InfoResponse>, (StatusCode, Json<ErrorResponse>)> {
    let user = Storage::get_user2(user_id).await.map_err(|_| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "Database error".to_string(),
            }),
        )
    })?;

    if let Some(user) = user {
        Ok(Json(InfoResponse {
            id: user.id,
            wallets: user.wallets.into_iter().map(Into::into).collect(),
        }))
    } else {
        Err((
            StatusCode::NOT_FOUND,
            <PERSON><PERSON>(ErrorResponse {
                error: "Wallet not found".to_string(),
            }),
        ))
    }
}

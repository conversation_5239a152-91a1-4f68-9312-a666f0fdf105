use sqlx::{Pool, Postgres, postgres::PgPoolOptions};
use tokio::sync::OnceCell;

use crate::config::config;

pub struct PostgresClient {
    pub(super) pool: Pool<Postgres>,
}

impl PostgresClient {
    pub async fn get() -> &'static PostgresClient {
        static INSTANCE: OnceCell<PostgresClient> = OnceCell::const_new();
        INSTANCE
            .get_or_init(|| async {
                let config = config();
                let max_connections = config.postgres_max_connections as u32;
                let min_connections = (max_connections / 4).max(20) as u32;
                let pool = PgPoolOptions::new()
                    .max_connections(max_connections)
                    .min_connections(min_connections)
                    .connect(&config.postgres_url)
                    .await
                    .expect("Cannot connect to Postgres.");

                tracing::info!(
                    "Connected to Postgres with {} max connections, {} min connections",
                    max_connections,
                    min_connections
                );

                sqlx::migrate!("./migrations")
                    .run(&pool)
                    .await
                    .expect("Failed to run migrations");

                let client = Self { pool };

                client
            })
            .await
    }
}

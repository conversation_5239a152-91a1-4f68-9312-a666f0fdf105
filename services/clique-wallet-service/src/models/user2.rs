use std::sync::OnceLock;

use aes_gcm::{
    Aes256Gcm, Nonce,
    aead::{A<PERSON>, A<PERSON><PERSON><PERSON>, KeyInit, OsRng, generic_array::GenericArray},
};
use clique_wallet_signer_types::{Network, WalletSet};
use eyre::{Result, eyre};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use super::{
    PostgresClient,
    enums::{SocialInfo, SocialProvider, WalletType},
    hex_uuid,
};
use crate::master_key::<PERSON><PERSON><PERSON>;

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct Wallet {
    pub address: String,
    pub network: Network,
    pub wallet_type: WalletType,
    pub wallet_set: WalletSet,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct MinimalWallet {
    pub address: String,
    pub network: Network,
}

impl From<Wallet> for MinimalWallet {
    fn from(wallet: Wallet) -> Self {
        Self {
            address: wallet.address,
            network: wallet.network,
        }
    }
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct WalletResponse {
    pub id: String,
    pub address: String,
    pub network: Network,
    pub wallet_type: WalletType,
    pub wallet_set: String,
}

impl From<Wallet> for WalletResponse {
    fn from(wallet: Wallet) -> Self {
        Self {
            id: wallet.address.clone(),
            address: wallet.address,
            network: wallet.network,
            wallet_type: wallet.wallet_type,
            wallet_set: wallet.wallet_set.to_string(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct UserSocialLink {
    pub user_id: Uuid,
    pub social_link: String,
}

impl UserSocialLink {
    pub fn new(user_id: Uuid, social_link: &SocialProvider) -> Self {
        Self {
            user_id,
            social_link: serde_json::to_string(social_link).unwrap(),
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct User2 {
    #[serde(with = "hex_uuid")]
    pub id: Uuid,
    pub share: String,
    pub wallets: Vec<Wallet>,
    pub social_links: Vec<SocialProvider>,
    pub social_infos: Vec<(SocialProvider, SocialInfo)>,
}

impl User2 {
    pub fn new(
        id: Uuid,
        share: String,
        wallets: Vec<Wallet>,
        social_links: Vec<SocialProvider>,
    ) -> Self {
        Self {
            id,
            share,
            wallets,
            social_links,
            social_infos: vec![],
        }
    }

    pub fn update_social_info(
        &mut self,
        social_provider: SocialProvider,
        social_info: SocialInfo,
    ) -> bool {
        if let Some(index) = self
            .social_infos
            .iter()
            .position(|(provider, _)| provider == &social_provider)
        {
            if self.social_infos[index].1 != social_info {
                self.social_infos[index].1 = social_info;
                return true;
            }
        } else {
            self.social_infos.push((social_provider, social_info));
            return true;
        }

        false
    }

    pub fn get_user_social_links(&self) -> Vec<UserSocialLink> {
        self.social_links
            .iter()
            .map(|s| UserSocialLink::new(self.id, s))
            .collect()
    }
}

pub struct UserCipher {
    cipher: Aes256Gcm,
}

impl UserCipher {
    const SALT: &[u8] = b"clique-wallet-user";

    pub fn get() -> &'static Self {
        let master_key = MasterKey::open();
        let key = master_key.derive_key(Self::SALT);
        let key = GenericArray::from_slice(&key);
        static USER_CIPHER: OnceLock<UserCipher> = OnceLock::new();
        USER_CIPHER.get_or_init(|| Self {
            cipher: Aes256Gcm::new(key),
        })
    }

    pub fn encrypt(&self, data: &[u8]) -> (Vec<u8>, Vec<u8>) {
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        let ciphertext = self
            .cipher
            .encrypt(&nonce, data)
            .expect("Encryption failure");
        (ciphertext, nonce.to_vec())
    }

    pub fn decrypt(&self, ciphertext: &[u8], nonce: &[u8]) -> Result<Vec<u8>> {
        let nonce = Nonce::from_slice(nonce);
        self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|_| eyre!("Decryption failed - tampered data"))
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct EncryptedUser2 {
    #[serde(with = "hex_uuid")]
    pub id: Uuid,
    pub wallets: sqlx::types::Json<Vec<MinimalWallet>>, // Wrap in sqlx::Json
    pub social_links: sqlx::types::Json<Vec<SocialProvider>>, // Wrap in sqlx::Json
    pub ciphertext: Vec<u8>,
    pub nonce: Vec<u8>,
}

impl From<User2> for EncryptedUser2 {
    fn from(user: User2) -> Self {
        let cipher = UserCipher::get();

        // Use serde_json to serialize user to get best compatibility
        let data = serde_json::to_vec(&user).expect("Failed to serialize user");

        let (ciphertext, nonce) = cipher.encrypt(&data);
        Self {
            id: user.id,
            wallets: sqlx::types::Json(user.wallets.into_iter().map(|w| w.into()).collect()),
            social_links: sqlx::types::Json(user.social_links),
            ciphertext,
            nonce,
        }
    }
}

impl TryFrom<EncryptedUser2> for User2 {
    type Error = eyre::Report;

    fn try_from(encrypted: EncryptedUser2) -> Result<Self, Self::Error> {
        let cipher = UserCipher::get();
        let data = cipher.decrypt(&encrypted.ciphertext, &encrypted.nonce)?;

        let user: User2 = match serde_json::from_slice(&data) {
            Ok(user) => user,
            Err(e) => {
                return Err(eyre::eyre!("Failed to deserialize user: {}", e));
            }
        };

        if user.id != encrypted.id {
            return Err(eyre::eyre!("User ID mismatch"));
        }
        let minimal_wallets: Vec<MinimalWallet> =
            user.wallets.clone().into_iter().map(|w| w.into()).collect();
        if minimal_wallets != *encrypted.wallets {
            return Err(eyre::eyre!("User wallets mismatch"));
        }
        if user.social_links != *encrypted.social_links {
            return Err(eyre::eyre!("User social links mismatch"));
        }

        Ok(user)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserResponse {
    pub id: Uuid,
    pub wallets: Vec<WalletResponse>,
    pub social_links: Vec<SocialProvider>,
    pub social_infos: Vec<SocialInfo>,
}

impl From<User2> for UserResponse {
    fn from(user: User2) -> Self {
        Self {
            id: user.id,
            wallets: user.wallets.into_iter().map(|w| w.into()).collect(),
            social_links: user.social_links,
            social_infos: user
                .social_infos
                .into_iter()
                .map(|(_, info)| info)
                .collect(),
        }
    }
}

impl PostgresClient {
    pub async fn create_user2(&self, user: User2) -> Result<()> {
        let social_links = user.get_user_social_links();

        let encrypted_user: EncryptedUser2 = user.into();

        // Start a transaction
        let mut tx = self.pool.begin().await?;

        let row_effected = sqlx::query!(
            r#"
            INSERT INTO users2 (id, wallets, social_links, ciphertext, nonce)
            VALUES ($1, $2, $3, $4, $5)
            "#,
            encrypted_user.id,
            encrypted_user.wallets as _,
            encrypted_user.social_links as _,
            encrypted_user.ciphertext,
            encrypted_user.nonce,
        )
        .execute(&mut *tx)
        .await?
        .rows_affected();

        if row_effected != 1 {
            return Err(eyre::eyre!("Failed to store user"));
        }

        for social_link in social_links {
            let row_effected = sqlx::query!(
                r#"
                INSERT INTO user_social_links (user_id, social_link)
                VALUES ($1, $2)
                "#,
                social_link.user_id,
                social_link.social_link,
            )
            .execute(&mut *tx)
            .await?
            .rows_affected();

            if row_effected != 1 {
                return Err(eyre::eyre!("Failed to store user social link"));
            }
        }

        tx.commit().await?;

        Ok(())
    }

    pub async fn update_user2(&self, user: User2) -> Result<()> {
        let encrypted_user: EncryptedUser2 = user.into();

        sqlx::query!(
            r#"
            UPDATE users2 SET wallets = $1, social_links = $2, ciphertext = $3, nonce = $4 WHERE id = $5
            "#,
            encrypted_user.wallets as _,
            encrypted_user.social_links as _,
            encrypted_user.ciphertext,
            encrypted_user.nonce,
            encrypted_user.id,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn add_social_links_for_user(
        &self,
        user: User2,
        social_links: Vec<SocialProvider>,
    ) -> Result<()> {
        let new_social_links: Vec<UserSocialLink> = social_links
            .iter()
            .map(|s| UserSocialLink::new(user.id, s))
            .collect();

        let mut tx = self.pool.begin().await?;

        for social_link in new_social_links {
            let row_effected = sqlx::query!(
                r#"
                INSERT INTO user_social_links (user_id, social_link)
                VALUES ($1, $2)
                "#,
                social_link.user_id,
                social_link.social_link,
            )
            .execute(&mut *tx)
            .await?
            .rows_affected();

            if row_effected != 1 {
                return Err(eyre::eyre!("Failed to store user social link"));
            }
        }

        let encrypted_user: EncryptedUser2 = user.into();

        sqlx::query!(
            r#"
            UPDATE users2 SET wallets = $1, social_links = $2, ciphertext = $3, nonce = $4 WHERE id = $5
            "#,
            encrypted_user.wallets as _,
            encrypted_user.social_links as _,
            encrypted_user.ciphertext,
            encrypted_user.nonce,
            encrypted_user.id,
        )
        .execute(&mut *tx)
        .await?;

        tx.commit().await?;

        Ok(())
    }

    pub async fn get_user2_by_id(&self, user_id: Uuid) -> Result<Option<User2>> {
        let encrypted_user = sqlx::query_as!(
            EncryptedUser2,
            r#"
            SELECT 
                id, 
                wallets as "wallets: sqlx::types::Json<Vec<MinimalWallet>>",
                social_links as "social_links: sqlx::types::Json<Vec<SocialProvider>>",
                ciphertext,
                nonce
            FROM users2
            WHERE id = $1
            "#,
            user_id
        )
        .fetch_optional(&self.pool)
        .await?;

        match encrypted_user {
            Some(encrypted_user) => {
                let user = encrypted_user.try_into()?;
                Ok(Some(user))
            }
            None => Ok(None),
        }
    }

    pub async fn get_user2_by_social_link(
        &self,
        social_link: &SocialProvider,
    ) -> Result<Option<User2>> {
        let encrypted_user = sqlx::query_as!(
            EncryptedUser2,
            r#"
            SELECT
                u.id,
                u.wallets as "wallets: sqlx::types::Json<Vec<MinimalWallet>>",
                u.social_links as "social_links: sqlx::types::Json<Vec<SocialProvider>>",
                u.ciphertext,
                u.nonce
            FROM users2 u
            WHERE u.social_links @> $1::jsonb
            "#,
            sqlx::types::Json(vec![social_link]) as _
        )
        .fetch_all(&self.pool)
        .await?;

        let mut users = encrypted_user
            .into_iter()
            .map(|e| e.try_into())
            .collect::<Result<Vec<User2>>>()?;

        match users.len() {
            0 => Ok(None),
            1 => {
                let user = users.remove(0);
                Ok(Some(user))
            }
            _ => {
                let ids = users.iter().map(|u| u.id).collect::<Vec<_>>();
                tracing::error!(
                    "[FIXME] Found multiple users with the same social link: {:?}, ids: {:?}",
                    social_link,
                    ids
                );
                return Err(eyre::eyre!(
                    "Found multiple users with the same social link"
                ));
            }
        }
    }

    pub async fn get_user2_by_wallet(&self, wallet: &MinimalWallet) -> Result<Option<User2>> {
        let encrypted_user = sqlx::query_as!(
            EncryptedUser2,
            r#"
            SELECT u.id,
                u.wallets as "wallets: sqlx::types::Json<Vec<MinimalWallet>>",
                u.social_links as "social_links: sqlx::types::Json<Vec<SocialProvider>>",
                u.ciphertext,
                u.nonce
            FROM users2 u
            WHERE u.wallets @> $1::jsonb
            "#,
            sqlx::types::Json(vec![wallet]) as _
        )
        .fetch_optional(&self.pool)
        .await?;

        match encrypted_user {
            Some(encrypted_user) => {
                let user = encrypted_user.try_into()?;
                Ok(Some(user))
            }
            None => Ok(None),
        }
    }

    pub async fn delete_user2(&self, user_id: Uuid) -> Result<()> {
        sqlx::query!("DELETE FROM users2 WHERE id = $1", user_id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_serialization() {
        crate::utils::setup_tracing();

        let id = Uuid::new_v4();
        dbg!(&id.to_string());

        let user2 = User2::new(id, "123".to_string(), vec![], vec![]);
        let serialized2 = serde_json::to_string(&user2).unwrap();
        let deserialized2: User2 = serde_json::from_str(&serialized2).unwrap();
        assert_eq!(user2, deserialized2);
        dbg!(&serialized2);

        let user2 = User2 {
            id,
            share: "123".to_string(),
            wallets: vec![Wallet {
                address: "123".to_string(),
                network: Network::Solana,
                wallet_type: WalletType::Embedded,
                wallet_set: WalletSet::Main,
            }],
            social_links: vec![SocialProvider::Twitter("123".to_string())],
            social_infos: vec![(
                SocialProvider::Twitter("123".to_string()),
                SocialInfo::Twitter(crate::social::twitter::TwitterUserInfo {
                    id: "123".to_string(),
                    username: "123".to_string(),
                    name: "123".to_string(),
                }),
            )],
        };
        let serialized2 = serde_json::to_string(&user2).unwrap();
        let deserialized2: User2 = serde_json::from_str(&serialized2).unwrap();
        assert_eq!(user2, deserialized2);
        tracing::info!("serialized2: {}", serialized2);
    }
}

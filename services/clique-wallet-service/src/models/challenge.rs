use std::sync::OnceLock;

use aes_gcm::{
    Aes256Gcm, Nonce,
    aead::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, KeyInit, OsRng, generic_array::GenericArray},
};
use eyre::{Result, eyre};
use serde::{Deserialize, Serialize};

use super::{PostgresClient, enums::ChallengeMethod};
use crate::{master_key::Master<PERSON>ey, utils::get_current_timestamp};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Challenge {
    pub method: ChallengeMethod,
    pub id: String,
    pub challenge: String,
    pub nonce: String,
    pub expires_at: i64,
}

impl Challenge {
    pub fn is_expired(&self) -> bool {
        let now = get_current_timestamp() as i64;
        now > self.expires_at
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, sqlx::FromRow)]
pub struct EncryptedChallenge {
    pub id: String,
    pub ciphertext: Vec<u8>,
    pub nonce: Vec<u8>,
}

impl From<Challenge> for EncryptedChallenge {
    fn from(challenge: Challenge) -> Self {
        let cipher = ChallengeCipher::get();
        let data = bincode::serialize(&challenge).expect("Failed to serialize challenge");
        let (ciphertext, nonce) = cipher.encrypt(&data);
        Self {
            id: challenge.id,
            ciphertext,
            nonce,
        }
    }
}

impl TryFrom<EncryptedChallenge> for Challenge {
    type Error = eyre::Report;

    fn try_from(encrypted: EncryptedChallenge) -> Result<Self, Self::Error> {
        let cipher = ChallengeCipher::get();
        let data = cipher.decrypt(&encrypted.ciphertext, &encrypted.nonce)?;
        let challenge: Challenge = bincode::deserialize(&data)?;

        if challenge.id != encrypted.id {
            return Err(eyre!("Challenge ID mismatch"));
        }

        Ok(challenge)
    }
}

pub struct ChallengeCipher {
    cipher: Aes256Gcm,
}

impl ChallengeCipher {
    const SALT: &[u8] = b"clique-wallet-challenge";

    pub fn get() -> &'static Self {
        let master_key = MasterKey::open();
        let key = master_key.derive_key(Self::SALT);
        let key = GenericArray::from_slice(&key);
        static CHALLENGE_CIPHER: OnceLock<ChallengeCipher> = OnceLock::new();
        CHALLENGE_CIPHER.get_or_init(|| Self {
            cipher: Aes256Gcm::new(key),
        })
    }

    pub fn encrypt(&self, data: &[u8]) -> (Vec<u8>, Vec<u8>) {
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        let ciphertext = self
            .cipher
            .encrypt(&nonce, data)
            .expect("Encryption failure");
        (ciphertext, nonce.to_vec())
    }

    pub fn decrypt(&self, ciphertext: &[u8], nonce: &[u8]) -> Result<Vec<u8>> {
        let nonce = Nonce::from_slice(nonce);
        self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|_| eyre!("Decryption failed - tampered data"))
    }
}

impl PostgresClient {
    pub async fn create_challenge(&self, challenge: Challenge) -> Result<()> {
        let encrypted_challenge: EncryptedChallenge = challenge.into();

        sqlx::query!(
            r#"
            INSERT INTO challenges (id, ciphertext, nonce)
            VALUES ($1, $2, $3)
            "#,
            encrypted_challenge.id,
            encrypted_challenge.ciphertext,
            encrypted_challenge.nonce,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn create_or_update_challenge(&self, challenge: Challenge) -> Result<()> {
        let encrypted_challenge: EncryptedChallenge = challenge.into();

        sqlx::query!(
            r#"
            INSERT INTO challenges (id, ciphertext, nonce)
            VALUES ($1, $2, $3)
            ON CONFLICT (id) DO UPDATE SET
            ciphertext = $2,
            nonce = $3
            "#,
            encrypted_challenge.id,
            encrypted_challenge.ciphertext,
            encrypted_challenge.nonce,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_challenge(&self, id: String) -> Result<Option<Challenge>> {
        let encrypted_challenge = sqlx::query_as!(
            EncryptedChallenge,
            r#"
            SELECT id, ciphertext, nonce FROM challenges WHERE id = $1
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        match encrypted_challenge {
            Some(encrypted_challenge) => {
                let challenge = encrypted_challenge.try_into()?;
                Ok(Some(challenge))
            }
            None => Ok(None),
        }
    }

    pub async fn delete_challenge(&self, id: String) -> Result<()> {
        sqlx::query!("DELETE FROM challenges WHERE id = $1", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

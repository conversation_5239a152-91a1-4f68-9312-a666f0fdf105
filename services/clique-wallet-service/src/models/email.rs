use std::sync::OnceLock;

use aes_gcm::{
    Aes256Gcm, <PERSON><PERSON>,
    aead::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, KeyInit, OsRng, generic_array::GenericArray},
};
use eyre::{Result, eyre};
use serde::{Deserialize, Serialize};

use super::PostgresClient;
use crate::{master_key::<PERSON><PERSON><PERSON>, utils::get_current_timestamp};

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, Serialize, Deserialize)]
pub struct EmailVerification {
    pub id: String, // lowercased email
    pub email: String,
    pub code: String,
    pub timestamp: u64,
    pub attempts: u8,
    pub is_verified: bool,
}

impl EmailVerification {
    pub fn new(email: String, code: String) -> Self {
        let id = email.to_lowercase();
        Self {
            id,
            email,
            code,
            timestamp: get_current_timestamp(),
            attempts: 0,
            is_verified: false,
        }
    }

    pub fn is_expired(&self) -> bool {
        get_current_timestamp() >= self.timestamp + 60 * 5
    }

    pub fn can_resend(&self) -> bool {
        get_current_timestamp() >= self.timestamp + 30
    }

    pub fn update_code(&mut self, code: String) {
        self.code = code;
        self.timestamp = get_current_timestamp();
        self.attempts = 0;
    }

    pub fn is_max_attempts(&self) -> bool {
        self.attempts >= 3
    }

    pub fn verify(&mut self, code: String) -> Result<bool> {
        if self.is_verified {
            return Err(eyre!("Email already verified"));
        }

        if self.is_expired() {
            return Err(eyre!("Verification code expired"));
        }

        if self.is_max_attempts() {
            return Err(eyre!("Max verification attempts reached"));
        }

        if self.code != code {
            self.attempts += 1;
            return Ok(false);
        }

        self.is_verified = true;

        Ok(true)
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct EncryptedEmailVerification {
    pub id: String,
    pub ciphertext: Vec<u8>,
    pub nonce: Vec<u8>,
}

impl From<EmailVerification> for EncryptedEmailVerification {
    fn from(email_verification: EmailVerification) -> Self {
        let cipher = EmailCipher::get();
        let data = bincode::serialize(&email_verification)
            .expect("Failed to serialize email verification");
        let (ciphertext, nonce) = cipher.encrypt(&data);
        Self {
            id: email_verification.id,
            ciphertext,
            nonce,
        }
    }
}

impl TryFrom<EncryptedEmailVerification> for EmailVerification {
    type Error = eyre::Report;

    fn try_from(encrypted: EncryptedEmailVerification) -> Result<Self, Self::Error> {
        let cipher = EmailCipher::get();
        let data = cipher.decrypt(&encrypted.ciphertext, &encrypted.nonce)?;
        let email_verification: EmailVerification = bincode::deserialize(&data)?;

        if email_verification.id != encrypted.id {
            return Err(eyre!("Email ID mismatch"));
        }

        Ok(email_verification)
    }
}

pub struct EmailCipher {
    cipher: Aes256Gcm,
}

impl EmailCipher {
    const SALT: &[u8] = b"clique-wallet-email";

    pub fn get() -> &'static Self {
        let master_key = MasterKey::open();
        let key = master_key.derive_key(Self::SALT);
        let key = GenericArray::from_slice(&key);
        static EMAIL_CIPHER: OnceLock<EmailCipher> = OnceLock::new();
        EMAIL_CIPHER.get_or_init(|| Self {
            cipher: Aes256Gcm::new(key),
        })
    }

    pub fn encrypt(&self, data: &[u8]) -> (Vec<u8>, Vec<u8>) {
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        let ciphertext = self
            .cipher
            .encrypt(&nonce, data)
            .expect("Encryption failure");
        (ciphertext, nonce.to_vec())
    }

    pub fn decrypt(&self, ciphertext: &[u8], nonce: &[u8]) -> Result<Vec<u8>> {
        let nonce = Nonce::from_slice(nonce);
        self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|_| eyre!("Decryption failed - tampered data"))
    }
}

impl PostgresClient {
    pub async fn create_or_update_email_verification(
        &self,
        email_verification: EmailVerification,
    ) -> Result<()> {
        let encrypted_email_verification: EncryptedEmailVerification = email_verification.into();

        sqlx::query!(
            r#"
            INSERT INTO emails (id, ciphertext, nonce)
            VALUES ($1, $2, $3)
            ON CONFLICT (id) DO UPDATE SET
                ciphertext = EXCLUDED.ciphertext,
                nonce = EXCLUDED.nonce
            "#,
            encrypted_email_verification.id,
            encrypted_email_verification.ciphertext,
            encrypted_email_verification.nonce,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn update_email_verification(
        &self,
        email_verification: EmailVerification,
    ) -> Result<()> {
        let encrypted_email_verification: EncryptedEmailVerification = email_verification.into();

        sqlx::query!(
            r#"
            UPDATE emails SET ciphertext = $1, nonce = $2 WHERE id = $3
            "#,
            encrypted_email_verification.ciphertext,
            encrypted_email_verification.nonce,
            encrypted_email_verification.id,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_email_verification(&self, id: String) -> Result<Option<EmailVerification>> {
        let encrypted_email_verification = sqlx::query_as!(
            EncryptedEmailVerification,
            r#"
            SELECT id, ciphertext, nonce FROM emails WHERE id = $1
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        match encrypted_email_verification {
            Some(encrypted_email_verification) => {
                let email_verification = encrypted_email_verification.try_into()?;
                Ok(Some(email_verification))
            }
            None => Ok(None),
        }
    }

    pub async fn delete_email_verification(&self, id: String) -> Result<()> {
        sqlx::query!("DELETE FROM emails WHERE id = $1", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

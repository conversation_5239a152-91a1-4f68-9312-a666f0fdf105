use std::sync::OnceLock;

use aes_gcm::{
    Aes256Gcm, Nonce,
    aead::{A<PERSON>, A<PERSON><PERSON><PERSON>, KeyInit, OsRng, generic_array::GenericArray},
};
use eyre::{Result, eyre};
use serde::{Deserialize, Serialize};

use super::{PostgresClient, enums::OauthProvider};
use crate::master_key::<PERSON><PERSON><PERSON>;

#[derive(Debug, Clone, Serialize, Deserialize)]
struct OauthRequestV1 {
    pub provider: OauthProvider,
    pub redirect_uri: String,
    pub state: String,
    pub code_challenge: String,
    pub client_id: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OauthRequest {
    pub provider: OauthProvider,
    pub redirect_uri: String,
    pub state: String,
    pub code_challenge: String,
    pub client_id: String,
    #[serde(default)]
    pub auth_data: Option<serde_json::Value>,
}

impl From<OauthRequestV1> for OauthRequest {
    fn from(oauth_request: OauthRequestV1) -> Self {
        OauthRequest {
            provider: oauth_request.provider,
            redirect_uri: oauth_request.redirect_uri,
            state: oauth_request.state,
            code_challenge: oauth_request.code_challenge,
            client_id: oauth_request.client_id,
            auth_data: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct EncryptedOauthRequest {
    pub id: String,
    pub ciphertext: Vec<u8>,
    pub nonce: Vec<u8>,
}

impl From<OauthRequest> for EncryptedOauthRequest {
    fn from(oauth_request: OauthRequest) -> Self {
        let cipher = OauthCipher::get();
        // Use serde_json to get better compatibility with old oauth requests
        let data = serde_json::to_vec(&oauth_request).expect("Failed to serialize oauth request");
        let (ciphertext, nonce) = cipher.encrypt(&data);
        Self {
            id: oauth_request.state,
            ciphertext,
            nonce,
        }
    }
}

impl TryFrom<EncryptedOauthRequest> for OauthRequest {
    type Error = eyre::Report;

    fn try_from(encrypted: EncryptedOauthRequest) -> Result<Self, Self::Error> {
        let cipher = OauthCipher::get();
        let data = cipher.decrypt(&encrypted.ciphertext, &encrypted.nonce)?;
        let oauth_request: OauthRequest = match serde_json::from_slice(&data) {
            Ok(oauth_request) => oauth_request,
            Err(_) => {
                // Compatibility with old oauth requests
                let oauth_request: OauthRequestV1 = bincode::deserialize(&data)?;
                oauth_request.into()
            }
        };

        if oauth_request.state != encrypted.id {
            return Err(eyre!("Oauth request ID mismatch"));
        }

        Ok(oauth_request)
    }
}

pub struct OauthCipher {
    cipher: Aes256Gcm,
}

impl OauthCipher {
    const SALT: &[u8] = b"clique-wallet-oauth";

    pub fn get() -> &'static Self {
        let master_key = MasterKey::open();
        let key = master_key.derive_key(Self::SALT);
        let key = GenericArray::from_slice(&key);
        static OAUTH_CIPHER: OnceLock<OauthCipher> = OnceLock::new();
        OAUTH_CIPHER.get_or_init(|| Self {
            cipher: Aes256Gcm::new(key),
        })
    }

    pub fn encrypt(&self, data: &[u8]) -> (Vec<u8>, Vec<u8>) {
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        let ciphertext = self
            .cipher
            .encrypt(&nonce, data)
            .expect("Encryption failure");
        (ciphertext, nonce.to_vec())
    }

    pub fn decrypt(&self, ciphertext: &[u8], nonce: &[u8]) -> Result<Vec<u8>> {
        let nonce = Nonce::from_slice(nonce);
        self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|_| eyre!("Decryption failed - tampered data"))
    }
}

impl PostgresClient {
    pub async fn create_oauth_request(&self, oauth_request: OauthRequest) -> Result<()> {
        let encrypted_oauth_request: EncryptedOauthRequest = oauth_request.into();

        sqlx::query!(
            r#"
            INSERT INTO oauth_requests (id, ciphertext, nonce)
            VALUES ($1, $2, $3)
            "#,
            encrypted_oauth_request.id,
            encrypted_oauth_request.ciphertext,
            encrypted_oauth_request.nonce,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_oauth_request(&self, id: String) -> Result<Option<OauthRequest>> {
        let encrypted_oauth_request = sqlx::query_as!(
            EncryptedOauthRequest,
            r#"
            SELECT id, ciphertext, nonce FROM oauth_requests WHERE id = $1
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        match encrypted_oauth_request {
            Some(encrypted_oauth_request) => {
                let oauth_request = encrypted_oauth_request.try_into()?;
                Ok(Some(oauth_request))
            }
            None => Ok(None),
        }
    }

    pub async fn update_oauth_request(&self, oauth_request: OauthRequest) -> Result<()> {
        let encrypted_oauth_request: EncryptedOauthRequest = oauth_request.into();
        sqlx::query!(
            r#"
            UPDATE oauth_requests SET ciphertext = $2, nonce = $3 WHERE id = $1
            "#,
            encrypted_oauth_request.id,
            encrypted_oauth_request.ciphertext,
            encrypted_oauth_request.nonce,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn delete_oauth_request(&self, id: String) -> Result<()> {
        sqlx::query!("DELETE FROM oauth_requests WHERE id = $1", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

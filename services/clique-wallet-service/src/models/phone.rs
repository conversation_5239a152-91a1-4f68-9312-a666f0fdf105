use std::sync::OnceLock;

use aes_gcm::{
    Aes256Gcm, Nonce,
    aead::{A<PERSON>, A<PERSON><PERSON><PERSON>, KeyInit, OsRng, generic_array::GenericArray},
};
use eyre::{Result, eyre};
use serde::{Deserialize, Serialize};

use super::PostgresClient;
use crate::{master_key::<PERSON><PERSON><PERSON>, utils::get_current_timestamp};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SingleVerification {
    pub code: Option<String>,
    pub sms_id: String,
    pub timestamp: i64,
    pub is_verified: bool,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PhoneVerification {
    pub phone: String,
    pub verifications: Vec<SingleVerification>,
    pub latest_timestamp: i64,
    pub total_attempts: u64,
}

impl PhoneVerification {
    const VERIFICATION_EXPIRATION_TIME: i64 = 60 * 5; // 5 minutes
    const VERIFICATION_RESEND_INTERVAL: i64 = 30; // 30 seconds

    pub fn new(phone: String, sms_id: String) -> Self {
        let timestamp = get_current_timestamp() as i64;
        Self {
            phone,
            verifications: vec![SingleVerification {
                code: None,
                sms_id,
                timestamp,
                is_verified: false,
            }],
            latest_timestamp: timestamp,
            total_attempts: 1,
        }
    }

    pub fn is_expired(&self) -> bool {
        get_current_timestamp() as i64 - self.latest_timestamp > Self::VERIFICATION_EXPIRATION_TIME
    }

    pub fn has_unverified_verification(&self) -> bool {
        let now = get_current_timestamp() as i64;
        let oldest_timestamp = now - Self::VERIFICATION_EXPIRATION_TIME;
        self.verifications
            .iter()
            .filter(|v| v.timestamp > oldest_timestamp)
            .any(|v| !v.is_verified)
    }

    pub fn can_resend(&self) -> bool {
        get_current_timestamp() as i64 - self.latest_timestamp > Self::VERIFICATION_RESEND_INTERVAL
    }

    pub fn verify(&mut self, sms_id: String) -> Result<bool> {
        if self.is_expired() {
            return Err(eyre!("Verification code expired"));
        }

        let verification = self.verifications.iter_mut().find(|v| v.sms_id == sms_id);
        if let Some(verification) = verification {
            if verification.is_verified {
                return Err(eyre!("SMS already verified"));
            }
            verification.is_verified = true;
            return Ok(true);
        }

        Ok(false)
    }

    pub fn add_verification(&mut self, sms_id: String) {
        let timestamp = get_current_timestamp() as i64;

        // keep only the last 24 hours
        let oldest_timestamp = timestamp - 60 * 60 * 24;
        self.verifications = self
            .verifications
            .iter()
            .filter(|v| v.timestamp > oldest_timestamp)
            .cloned()
            .collect();

        self.verifications.push(SingleVerification {
            code: None,
            sms_id,
            timestamp,
            is_verified: false,
        });
        self.total_attempts += 1;
        self.latest_timestamp = timestamp;
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct EncryptedPhoneVerification {
    pub id: String,
    pub ciphertext: Vec<u8>,
    pub nonce: Vec<u8>,
}

impl From<PhoneVerification> for EncryptedPhoneVerification {
    fn from(phone_verification: PhoneVerification) -> Self {
        let cipher = PhoneCipher::get();
        let data = bincode::serialize(&phone_verification)
            .expect("Failed to serialize phone verification");
        let (ciphertext, nonce) = cipher.encrypt(&data);
        Self {
            id: phone_verification.phone,
            ciphertext,
            nonce,
        }
    }
}

impl TryFrom<EncryptedPhoneVerification> for PhoneVerification {
    type Error = eyre::Report;

    fn try_from(encrypted: EncryptedPhoneVerification) -> Result<Self, Self::Error> {
        let cipher = PhoneCipher::get();
        let data = cipher.decrypt(&encrypted.ciphertext, &encrypted.nonce)?;
        let phone_verification: PhoneVerification = bincode::deserialize(&data)?;

        if phone_verification.phone != encrypted.id {
            return Err(eyre!("Phone ID mismatch"));
        }

        Ok(phone_verification)
    }
}

pub struct PhoneCipher {
    cipher: Aes256Gcm,
}

impl PhoneCipher {
    const SALT: &[u8] = b"clique-wallet-phone";

    pub fn get() -> &'static Self {
        let master_key = MasterKey::open();
        let key = master_key.derive_key(Self::SALT);
        let key = GenericArray::from_slice(&key);
        static PHONE_CIPHER: OnceLock<PhoneCipher> = OnceLock::new();
        PHONE_CIPHER.get_or_init(|| Self {
            cipher: Aes256Gcm::new(key),
        })
    }

    pub fn encrypt(&self, data: &[u8]) -> (Vec<u8>, Vec<u8>) {
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        let ciphertext = self
            .cipher
            .encrypt(&nonce, data)
            .expect("Encryption failure");
        (ciphertext, nonce.to_vec())
    }

    pub fn decrypt(&self, ciphertext: &[u8], nonce: &[u8]) -> Result<Vec<u8>> {
        let nonce = Nonce::from_slice(nonce);
        self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|_| eyre!("Decryption failed - tampered data"))
    }
}

impl PostgresClient {
    pub async fn create_or_update_phone_verification(
        &self,
        phone_verification: PhoneVerification,
    ) -> Result<()> {
        let encrypted_phone_verification: EncryptedPhoneVerification = phone_verification.into();

        sqlx::query!(
            r#"
            INSERT INTO phones (id, ciphertext, nonce)
            VALUES ($1, $2, $3)
            ON CONFLICT (id) DO UPDATE SET
                ciphertext = EXCLUDED.ciphertext,
                nonce = EXCLUDED.nonce
            "#,
            encrypted_phone_verification.id,
            encrypted_phone_verification.ciphertext,
            encrypted_phone_verification.nonce,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn update_phone_verification(
        &self,
        phone_verification: PhoneVerification,
    ) -> Result<()> {
        let encrypted_phone_verification: EncryptedPhoneVerification = phone_verification.into();

        sqlx::query!(
            r#"
            UPDATE phones SET ciphertext = $1, nonce = $2 WHERE id = $3
            "#,
            encrypted_phone_verification.ciphertext,
            encrypted_phone_verification.nonce,
            encrypted_phone_verification.id,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_phone_verification(&self, id: String) -> Result<Option<PhoneVerification>> {
        let encrypted_phone_verification = sqlx::query_as!(
            EncryptedPhoneVerification,
            r#"
            SELECT id, ciphertext, nonce FROM phones WHERE id = $1
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        match encrypted_phone_verification {
            Some(encrypted_phone_verification) => {
                let phone_verification = encrypted_phone_verification.try_into()?;
                Ok(Some(phone_verification))
            }
            None => Ok(None),
        }
    }

    pub async fn delete_phone_verification(&self, id: String) -> Result<()> {
        sqlx::query!("DELETE FROM phones WHERE id = $1", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

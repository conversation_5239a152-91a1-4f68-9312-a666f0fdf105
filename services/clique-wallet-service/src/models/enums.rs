use serde::{Deserialize, Serialize};

use crate::social::{google::GoogleUserInfo, telegram::TelegramUserInfo, twitter::TwitterUserInfo};

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum SocialProvider {
    Email(String),
    Google(String),
    GoogleAccountEmail(String),
    Apple(String),
    Twitter(String),
    Admin(String),
    Phone(String),
    Phantom(String),
    Telegram(u64),
}

impl SocialProvider {
    pub fn is_email(&self) -> bool {
        match self {
            Self::Email(_) => true,
            _ => false,
        }
    }

    pub fn is_google(&self) -> bool {
        match self {
            Self::Google(_) => true,
            _ => false,
        }
    }

    pub fn is_google_account_email(&self) -> bool {
        match self {
            Self::GoogleAccountEmail(_) => true,
            _ => false,
        }
    }
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq, Eq)]
pub enum SocialInfo {
    Google(GoogleUserInfo),
    Twitter(TwitterUserInfo),
    Telegram(TelegramUserInfo),
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum WalletType {
    Embedded,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum OauthProvider {
    Google,
    Twitter,
    Telegram,
}

impl OauthProvider {
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "google" => Some(Self::Google),
            "twitter" => Some(Self::Twitter),
            "telegram" => Some(Self::Telegram),
            _ => None,
        }
    }

    pub fn to_str(&self) -> &str {
        match self {
            Self::Google => "google",
            Self::Twitter => "twitter",
            Self::Telegram => "telegram",
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum ChallengeMethod {
    #[serde(rename = "phantom")]
    Phantom,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_social_info() {
        crate::utils::setup_tracing();

        let social_info1 = SocialInfo::Google(GoogleUserInfo {
            id: "123".to_string(),
            name: "John Doe".to_string(),
            email: "<EMAIL>".to_string(),
            picture: None,
            given_name: None,
            family_name: None,
        });

        let social_info2 = SocialInfo::Twitter(TwitterUserInfo {
            id: "123".to_string(),
            name: "John Doe".to_string(),
            username: "john.doe".to_string(),
        });

        let social_info3 = SocialInfo::Telegram(TelegramUserInfo {
            id: 123,
            first_name: None,
            last_name: None,
            username: None,
            photo_url: None,
        });

        let social_info = vec![social_info1, social_info2, social_info3];
        let serialized = serde_json::to_string(&social_info).unwrap();
        tracing::info!("serialized social_info: {}", serialized);
    }
}

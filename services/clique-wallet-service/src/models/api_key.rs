use aes_gcm::{
    Aes256Gcm, <PERSON><PERSON>,
    aead::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, KeyInit, generic_array::GenericArray},
};
use axum::{
    <PERSON><PERSON>,
    http::{HeaderMap, StatusCode},
};
use eyre::{Result, eyre};
use rand::{Rng, distributions::Alphanumeric, rngs::OsRng};
use serde::{Deserialize, Serialize};
use std::sync::OnceLock;
use time::OffsetDateTime;
use uuid::Uuid;

use super::PostgresClient;
use crate::{
    handler::ErrorResponse, master_key::MasterKey, models::User2, storage::Storage,
    utils::is_unique_violation,
};

const API_KEY_PREFIX: &str = "sk_";
const RANDOM_PART_LENGTH: usize = 16;
const UUID_COMPACT_LENGTH: usize = 32;

/// Complete API key data stored in encrypted form for integrity protection
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
struct ApiKeyData {
    record_id: Uuid,
    user_id: Uuid,
    random_part: String,
    created_at: OffsetDateTime,
    name: String,
}

/// Constant-time comparison to prevent timing attacks
fn constant_time_eq(a: &[u8], b: &[u8]) -> bool {
    if a.len() != b.len() {
        return false;
    }

    let mut result = 0u8;
    for (x, y) in a.iter().zip(b.iter()) {
        result |= x ^ y;
    }
    result == 0
}

pub struct ApiKeyCipher {
    cipher: Aes256Gcm,
}

impl ApiKeyCipher {
    const SALT: &[u8] = b"clique-wallet-api-key";

    pub fn get() -> &'static Self {
        let master_key = MasterKey::open();
        let key = master_key.derive_key(Self::SALT);
        let key = GenericArray::from_slice(&key);
        static API_KEY_CIPHER: OnceLock<ApiKeyCipher> = OnceLock::new();
        API_KEY_CIPHER.get_or_init(|| Self {
            cipher: Aes256Gcm::new(key),
        })
    }

    pub fn encrypt(&self, data: &[u8]) -> (Vec<u8>, Vec<u8>) {
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        let ciphertext = self
            .cipher
            .encrypt(&nonce, data)
            .expect("Encryption failure");
        (ciphertext, nonce.to_vec())
    }

    pub fn decrypt(&self, ciphertext: &[u8], nonce: &[u8]) -> Result<Vec<u8>> {
        let nonce = Nonce::from_slice(nonce);
        self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|_| eyre!("Decryption failed - tampered data"))
    }
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct ApiKey {
    pub id: Uuid,
    pub user_id: Uuid,
    pub key_ciphertext: Vec<u8>,
    pub key_nonce: Vec<u8>,
    pub name: String,
    pub created_at: OffsetDateTime,
    pub expires_at: Option<OffsetDateTime>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiKeyResponse {
    pub id: Uuid,
    pub name: String,
    pub key_prefix: String, // Only show first few characters for security
    pub created_at: OffsetDateTime,
    pub expires_at: Option<OffsetDateTime>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateApiKeyRequest {
    pub name: String,
    pub expires_at: Option<OffsetDateTime>, // None means never expires
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateApiKeyResponse {
    pub id: Uuid,
    pub name: String,
    pub api_key: String, // Full API key - only returned once
    pub created_at: OffsetDateTime,
    pub expires_at: Option<OffsetDateTime>,
}

impl ApiKey {
    /// Parse API key into components (record_id, random_part)
    fn parse_api_key(api_key: &str) -> Result<(Uuid, String)> {
        if !api_key.starts_with(API_KEY_PREFIX) {
            return Err(eyre!("Invalid API key prefix"));
        }

        let without_prefix = &api_key[API_KEY_PREFIX.len()..];
        let parts: Vec<&str> = without_prefix.split('_').collect();

        if parts.len() != 2
            || parts[0].len() != UUID_COMPACT_LENGTH
            || parts[1].len() != RANDOM_PART_LENGTH
        {
            return Err(eyre!("Invalid API key format"));
        }

        // Reconstruct UUID format with hyphens
        let id_str = parts[0];
        let formatted_uuid = format!(
            "{}-{}-{}-{}-{}",
            &id_str[0..8],
            &id_str[8..12],
            &id_str[12..16],
            &id_str[16..20],
            &id_str[20..32]
        );

        let uuid = Uuid::parse_str(&formatted_uuid).map_err(|_| eyre!("Invalid UUID format"))?;
        let random_part = parts[1].to_string();

        Ok((uuid, random_part))
    }

    /// Encrypt an API key for storage
    pub fn encrypt_key(api_key: &str) -> (Vec<u8>, Vec<u8>) {
        let cipher = ApiKeyCipher::get();
        cipher.encrypt(api_key.as_bytes())
    }

    /// Decrypt an API key from storage
    pub fn decrypt_key(ciphertext: &[u8], nonce: &[u8]) -> Result<String> {
        let cipher = ApiKeyCipher::get();
        let data = cipher.decrypt(ciphertext, nonce)?;
        String::from_utf8(data).map_err(|_| eyre!("Invalid UTF-8 in decrypted API key"))
    }

    /// Create a new API key in the database
    pub async fn create(
        user_id: Uuid,
        name: String,
        expires_at: Option<OffsetDateTime>,
    ) -> Result<CreateApiKeyResponse> {
        let id = Uuid::new_v4();

        // Generate only the random part
        let random_part: String = rand::thread_rng()
            .sample_iter(&Alphanumeric)
            .take(RANDOM_PART_LENGTH)
            .map(char::from)
            .collect();

        let created_at = OffsetDateTime::now_utc();

        // Create complete API key data for integrity protection
        let api_key_data = ApiKeyData {
            record_id: id,
            user_id,
            random_part: random_part.clone(),
            created_at,
            name: name.clone(),
        };

        // Serialize and encrypt the complete data
        let serialized = serde_json::to_string(&api_key_data)
            .map_err(|e| eyre!("Failed to serialize API key data: {}", e))?;
        let (key_ciphertext, key_nonce) = Self::encrypt_key(&serialized);

        // Construct the full API key for the response
        let id_str = id.to_string().replace("-", "");
        let api_key = format!("{}{}_{}", API_KEY_PREFIX, id_str, random_part);

        let client = PostgresClient::get().await;

        let result = sqlx::query!(
            r#"
            INSERT INTO api_keys (id, user_id, key_ciphertext, key_nonce, name, created_at, expires_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            "#,
            id,
            user_id,
            &key_ciphertext,
            &key_nonce,
            &name,
            created_at,
            expires_at
        )
        .execute(&client.pool)
        .await;

        match result {
            Ok(_) => Ok(CreateApiKeyResponse {
                id,
                name,
                api_key,
                created_at,
                expires_at,
            }),
            Err(e) => {
                if is_unique_violation(&e.to_string()) {
                    Err(eyre!("API key already exists"))
                } else {
                    Err(eyre!("Failed to create API key: {}", e))
                }
            }
        }
    }

    /// Find API key using O(1) primary key lookup
    pub async fn find_by_key(api_key: &str) -> Result<Option<Self>> {
        // 1. Parse API key once (optimized - single parsing operation)
        let (record_id, random_part) = match Self::parse_api_key(api_key) {
            Ok(parts) => parts,
            Err(_) => return Ok(None), // Invalid format, return None
        };

        // 2. Direct primary key query (O(1))
        let client = PostgresClient::get().await;
        let row = sqlx::query_as!(
            ApiKey,
            r#"
            SELECT id, user_id, key_ciphertext, key_nonce, name, created_at, expires_at, is_active
            FROM api_keys
            WHERE id = $1 AND is_active = true
            "#,
            record_id
        )
        .fetch_optional(&client.pool)
        .await?;

        // 3. Verify complete API key data integrity
        if let Some(record) = row {
            if let Ok(decrypted_data) = Self::decrypt_key(&record.key_ciphertext, &record.key_nonce)
            {
                // Deserialize the complete API key data
                if let Ok(stored_data) = serde_json::from_str::<ApiKeyData>(&decrypted_data) {
                    // Verify data integrity - all fields must match
                    if stored_data.record_id == record.id
                        && stored_data.user_id == record.user_id
                        && stored_data.created_at == record.created_at
                        && stored_data.name == record.name
                        && constant_time_eq(
                            random_part.as_bytes(),
                            stored_data.random_part.as_bytes(),
                        )
                    {
                        return Ok(Some(record));
                    }
                }
            }
        }

        Ok(None)
    }

    /// Get all API keys for a user
    pub async fn find_by_user_id(user_id: Uuid) -> Result<Vec<ApiKeyResponse>> {
        let client = PostgresClient::get().await;

        let rows = sqlx::query_as!(
            ApiKey,
            r#"
            SELECT id, user_id, key_ciphertext, key_nonce, name, created_at, expires_at, is_active
            FROM api_keys
            WHERE user_id = $1
            ORDER BY created_at DESC
            "#,
            user_id
        )
        .fetch_all(&client.pool)
        .await?;

        let responses = rows
            .into_iter()
            .map(|key| {
                // Show prefix in the new format: sk_xxxx****_xxxx
                // This gives users a hint about the structure without revealing the full key
                let id_str = key.id.to_string().replace("-", "");
                let key_prefix = format!(
                    "{}{}****_{}****",
                    &API_KEY_PREFIX,
                    &id_str[..4], // First 4 chars of UUID
                    &key.name.chars().take(4).collect::<String>() // First 4 chars of name for identification
                );

                ApiKeyResponse {
                    id: key.id,
                    name: key.name,
                    key_prefix,
                    created_at: key.created_at,
                    expires_at: key.expires_at,
                    is_active: key.is_active,
                }
            })
            .collect();

        Ok(responses)
    }

    /// Deactivate an API key
    pub async fn deactivate(id: Uuid, user_id: Uuid) -> Result<bool> {
        let client = PostgresClient::get().await;

        let result = sqlx::query!(
            r#"
            UPDATE api_keys
            SET is_active = false
            WHERE id = $1 AND user_id = $2
            "#,
            id,
            user_id
        )
        .execute(&client.pool)
        .await?;

        Ok(result.rows_affected() > 0)
    }

    /// Check if API key is expired
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            OffsetDateTime::now_utc() > expires_at
        } else {
            false // Never expires if expires_at is None
        }
    }

    /// Check if API key is valid (active and not expired)
    pub fn is_valid(&self) -> bool {
        self.is_active && !self.is_expired()
    }
}

/// Authenticate a user by API key - now with O(1) performance!
pub async fn authenticate_api_key(api_key: &str) -> Result<Option<Uuid>> {
    // find_by_key handles format validation internally
    if let Some(api_key_record) = ApiKey::find_by_key(api_key).await? {
        if api_key_record.is_valid() {
            return Ok(Some(api_key_record.user_id));
        }
    }

    Ok(None)
}

/// Authenticate user by API key and return user info
pub async fn authenticate_user_by_api_key(
    headers: &HeaderMap,
) -> Result<User2, (StatusCode, Json<ErrorResponse>)> {
    // Extract API key from Authorization header
    let api_key = headers
        .get("authorization")
        .and_then(|h| h.to_str().ok())
        .and_then(|s| s.strip_prefix("Bearer "))
        .map(|s| s.to_string())
        .ok_or_else(|| {
            (
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    error: "Authentication failed".to_string(),
                }),
            )
        })?;

    let user_id = authenticate_api_key(&api_key)
        .await
        .map_err(|e| {
            tracing::error!("API key authentication error: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Authentication error".to_string(),
                }),
            )
        })?
        .ok_or_else(|| {
            (
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    error: "Authentication failed".to_string(),
                }),
            )
        })?;

    let user = Storage::get_user2(user_id)
        .await
        .map_err(|e| {
            tracing::error!("Failed to get user: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get user".to_string(),
                }),
            )
        })?
        .ok_or_else(|| {
            (
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    error: "Authentication failed".to_string(),
                }),
            )
        })?;

    Ok(user)
}

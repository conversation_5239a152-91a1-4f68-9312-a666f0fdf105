use std::sync::OnceLock;

use aes_gcm::{
    Aes256Gcm, Nonce,
    aead::{A<PERSON>, A<PERSON><PERSON><PERSON>, KeyInit, OsRng, generic_array::GenericArray},
};
use async_trait::async_trait;
use eyre::{Result, eyre};
use serde::{Deserialize, Serialize};
use time::OffsetDateTime;
use tower_sessions_core::{
    SessionStore,
    session::{Id, Record as SessionRecord},
    session_store,
};

use super::PostgresClient;
use crate::{master_key::MasterKey, utils::is_unique_violation};

#[derive(Clone, Debug, Serialize, Deserialize, sqlx::FromRow)]
pub struct EncryptedSessionRecord {
    pub id: String,
    pub expiry_date: OffsetDateTime,
    pub ciphertext: Vec<u8>,
    pub nonce: Vec<u8>,
}

impl From<SessionRecord> for EncryptedSessionRecord {
    fn from(record: SessionRecord) -> Self {
        let cipher = SessionCipher::get();
        let data = serde_json::to_vec(&record).expect("Failed to serialize record");
        let (ciphertext, nonce) = cipher.encrypt(&data);
        EncryptedSessionRecord {
            id: record.id.to_string(),
            expiry_date: record.expiry_date,
            ciphertext,
            nonce,
        }
    }
}

impl TryFrom<EncryptedSessionRecord> for SessionRecord {
    type Error = eyre::Report;

    fn try_from(encrypted_record: EncryptedSessionRecord) -> Result<Self, Self::Error> {
        let cipher = SessionCipher::get();
        let data = cipher.decrypt(&encrypted_record.ciphertext, &encrypted_record.nonce)?;
        let record: SessionRecord = serde_json::from_slice(&data)?;

        if encrypted_record.id != record.id.to_string() {
            return Err(eyre::eyre!("Session ID mismatch"));
        }

        Ok(record)
    }
}

#[derive(Clone, Debug, Default)]
pub struct PostgresSessionStore;

#[async_trait]
impl SessionStore for PostgresSessionStore {
    async fn create(&self, record: &mut SessionRecord) -> session_store::Result<()> {
        let postgres_client = PostgresClient::get().await;

        loop {
            match postgres_client.create_session(record.clone()).await {
                Ok(_) => return Ok(()),
                Err(e) => {
                    if !is_unique_violation(&e.to_string()) {
                        return Err(session_store::Error::Backend(e.to_string()));
                    }
                }
            }
            tracing::warn!("Failed to create session, retrying...");
            record.id = Id::default();
        }
    }

    async fn save(&self, record: &SessionRecord) -> session_store::Result<()> {
        let postgres_client = PostgresClient::get().await;
        postgres_client
            .update_session(record.clone())
            .await
            .map_err(|e| session_store::Error::Backend(e.to_string()))?;
        Ok(())
    }

    async fn load(&self, session_id: &Id) -> session_store::Result<Option<SessionRecord>> {
        let postgres_client = PostgresClient::get().await;
        let session = postgres_client
            .get_session(session_id.to_string())
            .await
            .map_err(|e| session_store::Error::Backend(e.to_string()))?;
        Ok(session)
    }

    async fn delete(&self, session_id: &Id) -> session_store::Result<()> {
        let postgres_client = PostgresClient::get().await;
        postgres_client
            .delete_session(session_id.to_string())
            .await
            .map_err(|e| session_store::Error::Backend(e.to_string()))?;
        Ok(())
    }
}

pub struct SessionCipher {
    cipher: Aes256Gcm,
}

impl SessionCipher {
    const SALT: &[u8] = b"clique-wallet-session";

    pub fn get() -> &'static Self {
        let master_key = MasterKey::open();
        let key = master_key.derive_key(Self::SALT);
        let key = GenericArray::from_slice(&key);
        static SESSION_CIPHER: OnceLock<SessionCipher> = OnceLock::new();
        SESSION_CIPHER.get_or_init(|| Self {
            cipher: Aes256Gcm::new(key),
        })
    }

    pub fn encrypt(&self, data: &[u8]) -> (Vec<u8>, Vec<u8>) {
        let nonce = Aes256Gcm::generate_nonce(&mut OsRng);
        let ciphertext = self
            .cipher
            .encrypt(&nonce, data)
            .expect("Encryption failure");
        (ciphertext, nonce.to_vec())
    }

    pub fn decrypt(&self, ciphertext: &[u8], nonce: &[u8]) -> Result<Vec<u8>> {
        let nonce = Nonce::from_slice(nonce);
        self.cipher
            .decrypt(nonce, ciphertext)
            .map_err(|_| eyre!("Decryption failed - tampered data"))
    }
}

impl PostgresClient {
    pub async fn create_session(&self, session: SessionRecord) -> Result<()> {
        let encrypted_session_record: EncryptedSessionRecord = session.into();

        sqlx::query!(
            r#"
            INSERT INTO sessions (id, expiry_date, ciphertext, nonce)
            VALUES ($1, $2, $3, $4)
            "#,
            encrypted_session_record.id,
            encrypted_session_record.expiry_date,
            encrypted_session_record.ciphertext,
            encrypted_session_record.nonce,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn update_session(&self, session: SessionRecord) -> Result<()> {
        let encrypted_session_record: EncryptedSessionRecord = session.into();

        sqlx::query!(
            r#"
            UPDATE sessions 
            SET expiry_date = $1, ciphertext = $2, nonce = $3 
            WHERE id = $4
            "#,
            encrypted_session_record.expiry_date,
            encrypted_session_record.ciphertext,
            encrypted_session_record.nonce,
            encrypted_session_record.id,
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_session(&self, id: String) -> Result<Option<SessionRecord>> {
        let encrypted_session_record = sqlx::query_as!(
            EncryptedSessionRecord,
            r#"
            SELECT id, expiry_date, ciphertext, nonce FROM sessions WHERE id = $1
            "#,
            id
        )
        .fetch_optional(&self.pool)
        .await?;

        match encrypted_session_record {
            Some(encrypted_session_record) => {
                let session_record = encrypted_session_record.try_into()?;
                Ok(Some(session_record))
            }
            None => Ok(None),
        }
    }

    pub async fn contains_session(&self, id: String) -> Result<bool> {
        let result = sqlx::query!("SELECT EXISTS(SELECT 1 FROM sessions WHERE id = $1)", id)
            .fetch_one(&self.pool)
            .await?;
        Ok(result.exists.unwrap_or(false))
    }

    pub async fn delete_session(&self, id: String) -> Result<()> {
        sqlx::query!("DELETE FROM sessions WHERE id = $1", id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn cleanup_expired_sessions(&self) -> Result<()> {
        sqlx::query!("DELETE FROM sessions WHERE expiry_date <= NOW()")
            .execute(&self.pool)
            .await?;

        Ok(())
    }
}

use std::str::FromStr;

use axum::{Json, http::header::HeaderMap, response::IntoResponse};
use eyre::Result;
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};
use solana_sdk::{pubkey::Pubkey, signature::Signature};
use tower_sessions::Session;

use super::ErrorResponse;
use crate::{
    config::config,
    core::create::{VerifiedLoginRequest, create_user},
    models::{
        UserResponse,
        enums::{SocialInfo, SocialProvider},
    },
    social::{
        email::EmailProvider,
        google::fetch_google_token,
        phantom::PhantomAdapter,
        sms::PreludeProvider,
        telegram::{TelegramAdapter, TelegramAuthData},
        twitter::{fetch_twitter_token, fetch_twitter_user_me},
    },
    storage::Storage,
};

pub const SESSION_KEY: &str = "user_id";

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum LoginRequest {
    TwitterOAuth {
        state: String,
        code: String,
        code_verifier: String,
    },
    GoogleOAuth {
        state: String,
        code: String,
        code_verifier: String,
    },
    Phantom {
        wallet_address: String,
        challenge: String,
        signature: String,
    },
    Email {
        email: String,
        code: String,
    },
    Phone {
        phone: String,
        code: String,
    },
    Telegram {
        id: u64,
        first_name: Option<String>,
        last_name: Option<String>,
        username: Option<String>,
        photo_url: Option<String>,
        auth_date: u64,
        hash: String,
    },
    TelegramOAuth {
        state: String,
        code: String,
    },
}

pub async fn verify_login_request(
    headers: HeaderMap,
    login_request: LoginRequest,
) -> Result<VerifiedLoginRequest, (StatusCode, Json<ErrorResponse>)> {
    let domain = super::oauth::get_domain(headers).map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: e.to_string(),
            }),
        )
    })?;
    let callback_url = super::oauth::get_callback_url(domain);

    let verified_login_request = match login_request {
        LoginRequest::TwitterOAuth {
            state,
            code,
            code_verifier,
        } => {
            if !config().enable_twitter {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Twitter OAuth is not enabled".to_string(),
                    }),
                ));
            }

            let oauth_request = match Storage::get_oauth_request(state.clone()).await {
                Ok(Some(oauth_request)) => oauth_request,
                Ok(None) => {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: "Invalid state".to_string(),
                        }),
                    ));
                }
                Err(_) => {
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse {
                            error: "Failed to get oauth request".to_string(),
                        }),
                    ));
                }
            };

            let token =
                match fetch_twitter_token(oauth_request, code, code_verifier, callback_url).await {
                    Ok(token) => token,
                    Err(e) => {
                        tracing::error!("Twitter token error: {}", e);
                        return Err((
                            StatusCode::BAD_REQUEST,
                            Json(ErrorResponse {
                                error: "Failed to fetch twitter token".to_string(),
                            }),
                        ));
                    }
                };

            let user_info = match fetch_twitter_user_me(token).await {
                Ok(user_info) => user_info,
                Err(e) => {
                    tracing::error!("Twitter user info error: {}", e);
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: "Failed to fetch twitter user info".to_string(),
                        }),
                    ));
                }
            };

            if let Err(e) = Storage::delete_oauth_request(state.clone()).await {
                tracing::error!("Failed to delete twitter oauth request: {}", e);
            };

            VerifiedLoginRequest {
                social: SocialProvider::Twitter(user_info.id.clone()),
                social_info: Some(SocialInfo::Twitter(user_info)),
            }
        }
        LoginRequest::GoogleOAuth {
            state,
            code,
            code_verifier,
        } => {
            if !config().enable_google {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Google OAuth is not enabled".to_string(),
                    }),
                ));
            }

            let oauth_request = match Storage::get_oauth_request(state.clone()).await {
                Ok(Some(oauth_request)) => oauth_request,
                Ok(None) => {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: "Invalid state".to_string(),
                        }),
                    ));
                }
                Err(_) => {
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse {
                            error: "Failed to get oauth request".to_string(),
                        }),
                    ));
                }
            };

            // TODO: revoke google oauth token after login success
            let user_info =
                match fetch_google_token(oauth_request, code, code_verifier, callback_url).await {
                    Ok(user_info) => user_info,
                    Err(e) => {
                        tracing::error!("Google token error: {}", e);
                        return Err((
                            StatusCode::BAD_REQUEST,
                            Json(ErrorResponse {
                                error: "Failed to fetch google token".to_string(),
                            }),
                        ));
                    }
                };

            if let Err(e) = Storage::delete_oauth_request(state.clone()).await {
                tracing::error!("Failed to delete google oauth request: {}", e);
            };

            VerifiedLoginRequest {
                social: SocialProvider::Google(user_info.id.clone()),
                social_info: Some(SocialInfo::Google(user_info)),
            }
        }
        LoginRequest::Phantom {
            wallet_address,
            challenge,
            signature,
        } => {
            if !config().enable_phantom {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Phantom is not enabled".to_string(),
                    }),
                ));
            }

            let wallet_address = Pubkey::from_str(&wallet_address).map_err(|_| {
                (
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Invalid wallet address format".to_string(),
                    }),
                )
            })?;
            let signature = Signature::from_str(&signature).map_err(|_| {
                (
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Invalid signature format".to_string(),
                    }),
                )
            })?;

            let challenge_in_db = match Storage::get_challenge(wallet_address.to_string()).await {
                Ok(Some(challenge)) => challenge,
                Ok(None) => {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: "Challenge not found, please generate a new challenge"
                                .to_string(),
                        }),
                    ));
                }
                Err(e) => {
                    tracing::error!("Failed to get challenge: {}", e);
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse {
                            error: "Failed to get challenge".to_string(),
                        }),
                    ));
                }
            };

            if challenge_in_db.is_expired() {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Challenge has expired, please generate a new challenge".to_string(),
                    }),
                ));
            }

            if challenge_in_db.challenge != challenge {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Challenge mismatch, please generate a new challenge".to_string(),
                    }),
                ));
            }

            let phantom_adapter = PhantomAdapter::get();
            let verified_wallet_address = match phantom_adapter
                .verify_phantom_signature(wallet_address, &challenge, signature)
                .await
            {
                Ok(wallet_address) => wallet_address,
                Err(e) => {
                    tracing::error!("Failed to verify phantom signature: {}", e);
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: "Invalid signature".to_string(),
                        }),
                    ));
                }
            };

            if let Err(e) = Storage::delete_challenge(wallet_address.to_string()).await {
                tracing::error!("Failed to delete challenge: {}", e);
            }

            VerifiedLoginRequest {
                social: SocialProvider::Phantom(verified_wallet_address),
                social_info: None,
            }
        }
        LoginRequest::Email { email, code } => {
            if !config().enable_email {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Email is not enabled".to_string(),
                    }),
                ));
            }

            let email = email.trim();
            if !EmailProvider::validate_email_address(email) {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Invalid email address format".to_string(),
                    }),
                ));
            }

            let lower_email = email.to_lowercase();
            let verified = match Storage::verify_email(&lower_email, code).await {
                Ok(verified) => verified,
                Err(e) => {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: e.to_string(),
                        }),
                    ));
                }
            };
            if !verified {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Invalid verification code".to_string(),
                    }),
                ));
            }

            // Delete verification code
            if let Err(e) = Storage::delete_email_verification(&lower_email).await {
                tracing::error!("Failed to delete email verification: {}", e);
            };

            VerifiedLoginRequest {
                social: SocialProvider::Email(lower_email),
                social_info: None,
            }
        }
        LoginRequest::Phone { phone, code } => {
            if !config().enable_sms {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "SMS Login is not enabled".to_string(),
                    }),
                ));
            }

            let phone = phone.trim();
            let mut phone_verification = match Storage::get_phone_verification(phone).await {
                Ok(Some(phone_verification)) => phone_verification,
                Ok(None) => {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse { error: "You haven't sent a verification code to this phone number before login with phone, please send a verification code first".to_string() }),
                    ));
                }
                Err(e) => {
                    tracing::error!("Failed to get phone verification: {}", e);
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse {
                            error: "Failed to get phone verification from storage".to_string(),
                        }),
                    ));
                }
            };

            if phone_verification.is_expired() {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse { error: "Phone verification code has expired, please send a new verification code".to_string() }),
                ));
            }

            if !phone_verification.has_unverified_verification() {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse { error: "There is no unverified verification code, please send a new verification code first".to_string() }),
                ));
            }

            let sms_provider = PreludeProvider::get();
            let check_code_response = match sms_provider.check_otp_code(&phone, &code).await {
                Ok(check_code_response) => check_code_response,
                Err(e) => {
                    tracing::warn!("Failed to check verification code: {}", e);
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: "Failed to check verification code".to_string(),
                        }),
                    ));
                }
            };
            if !check_code_response.is_verified() {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Failed to verify sms verification code".to_string(),
                    }),
                ));
            }

            match phone_verification.verify(check_code_response.id.clone()) {
                Ok(true) => {
                    if let Err(e) = Storage::update_phone_verification(phone_verification).await {
                        tracing::error!("Failed to update phone verification during login: {}", e);
                    }
                }
                Ok(false) => {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse { error: "Can not found corresponding sms verification code, please send a new verification code".to_string() }),
                    ));
                }
                Err(_) => {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse { error: "The sms verification code already verified before, please send a new verification code".to_string() }),
                    ));
                }
            };

            VerifiedLoginRequest {
                social: SocialProvider::Phone(phone.to_string()),
                social_info: None,
            }
        }
        LoginRequest::Telegram {
            id,
            first_name,
            last_name,
            username,
            photo_url,
            auth_date,
            hash,
        } => {
            if !config().enable_telegram_login {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Telegram is not enabled".to_string(),
                    }),
                ));
            }

            let auth_data = TelegramAuthData {
                id,
                first_name,
                last_name,
                username,
                photo_url,
                auth_date,
                hash,
            };

            let telegram_adapter = TelegramAdapter::get();
            match telegram_adapter
                .verify_telegram_auth(&auth_data, true)
                .await
            {
                Ok(true) => VerifiedLoginRequest {
                    social: SocialProvider::Telegram(id),
                    social_info: Some(SocialInfo::Telegram(auth_data.into())),
                },
                Ok(false) => {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: "Telegram auth mismatch".to_string(),
                        }),
                    ));
                }
                Err(e) => {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: e.to_string(),
                        }),
                    ));
                }
            }
        }
        LoginRequest::TelegramOAuth { state, code } => {
            if !config().enable_telegram_login {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Telegram OAuth is not enabled".to_string(),
                    }),
                ));
            }

            let oauth_request = match Storage::get_oauth_request(state.clone()).await {
                Ok(Some(oauth_request)) => oauth_request,
                Ok(None) => {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: "Invalid state".to_string(),
                        }),
                    ));
                }
                Err(_) => {
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse {
                            error: "Failed to get oauth request".to_string(),
                        }),
                    ));
                }
            };

            if let Some(auth_data) = oauth_request.auth_data {
                let telegram_auth_data = serde_json::from_value::<TelegramAuthData>(auth_data)
                    .map_err(|_| {
                        (
                            StatusCode::BAD_REQUEST,
                            Json(ErrorResponse {
                                error: "Invalid telegram auth data in oauth request".to_string(),
                            }),
                        )
                    })?;
                if telegram_auth_data.hash != code {
                    return Err((
                        StatusCode::BAD_REQUEST,
                        Json(ErrorResponse {
                            error: "Invalid telegram auth code".to_string(),
                        }),
                    ));
                }

                let telegram_adapter = TelegramAdapter::get();
                match telegram_adapter
                    .verify_telegram_auth(&telegram_auth_data, false)
                    .await
                {
                    Ok(true) => {
                        if let Err(e) = Storage::delete_oauth_request(state.clone()).await {
                            tracing::error!("Failed to delete telegram oauth request: {}", e);
                        };
                        VerifiedLoginRequest {
                            social: SocialProvider::Telegram(telegram_auth_data.id),
                            social_info: Some(SocialInfo::Telegram(telegram_auth_data.into())),
                        }
                    }
                    Ok(false) => {
                        return Err((
                            StatusCode::BAD_REQUEST,
                            Json(ErrorResponse {
                                error: "Telegram auth mismatch".to_string(),
                            }),
                        ));
                    }
                    Err(e) => {
                        return Err((
                            StatusCode::BAD_REQUEST,
                            Json(ErrorResponse {
                                error: e.to_string(),
                            }),
                        ));
                    }
                }
            } else {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Telegram auth data not found, please authorize with telegram first"
                            .to_string(),
                    }),
                ));
            }
        }
    };

    Ok(verified_login_request)
}

pub async fn handle_login(
    headers: HeaderMap,
    session: Session,
    Json(login_request): Json<LoginRequest>,
) -> Result<Json<UserResponse>, (StatusCode, Json<ErrorResponse>)> {
    tracing::debug!("Login request: {:?}", login_request);

    let verified_login_request = verify_login_request(headers, login_request).await?;

    let mut user = match Storage::get_user2_by_social(&verified_login_request.social).await {
        Ok(user) => user,
        Err(e) => {
            tracing::error!("Failed to get user from storage: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get user from storage".to_string(),
                }),
            ));
        }
    };

    if user.is_none() {
        let social_link = {
            if let Some(SocialInfo::Google(google_user_info)) = &verified_login_request.social_info
            {
                Some(SocialProvider::Email(google_user_info.email.clone()))
            } else if let SocialProvider::Email(email) = &verified_login_request.social {
                Some(SocialProvider::GoogleAccountEmail(email.clone()))
            } else {
                None
            }
        };

        if let Some(social_link) = social_link {
            match Storage::get_user2_by_social(&social_link).await {
                Ok(Some(email_user)) => user = Some(email_user),
                Ok(None) => (),
                Err(e) => {
                    tracing::error!("Failed to get user from storage: {}", e);
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse {
                            error: "Failed to get user from storage".to_string(),
                        }),
                    ));
                }
            };
        }
    }

    if let Some(mut user) = user {
        let mut social_info_changed = false;
        let mut social_links_changed = false;
        if let Some(new_social_info) = verified_login_request.social_info {
            if let SocialInfo::Google(google_user_info) = &new_social_info {
                let email = google_user_info.email.clone();
                for social_link in user.social_links.iter_mut() {
                    if let SocialProvider::GoogleAccountEmail(cur_email) = social_link {
                        if cur_email != &email {
                            *social_link = SocialProvider::GoogleAccountEmail(email);
                            social_links_changed = true;
                            break;
                        }
                    }
                }
            }

            social_info_changed = user.update_social_info(
                verified_login_request.social.clone(),
                new_social_info.clone(),
            );
        }

        if social_info_changed || social_links_changed {
            match Storage::update_user2(user.clone()).await {
                Ok(_) => (),
                Err(e) => {
                    tracing::error!("Failed to update user: {}", e);
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse {
                            error: "Failed to update user".to_string(),
                        }),
                    ));
                }
            };
        }

        // After successful authentication:
        match session.insert(SESSION_KEY, user.id.to_string()).await {
            Ok(_) => {
                tracing::info!("Session set for user: {}", user.id);
            }
            Err(_) => {
                return Err((
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse {
                        error: "Failed to set session".to_string(),
                    }),
                ));
            }
        }

        let user_response = UserResponse::from(user);

        return Ok(Json(user_response));
    }

    match create_user(verified_login_request).await {
        Ok(user) => {
            // After successful authentication:
            match session.insert(SESSION_KEY, user.id.to_string()).await {
                Ok(_) => {
                    tracing::info!("Session set for user: {}", user.id);
                }
                Err(_) => {
                    return Err((
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse {
                            error: "Failed to set session".to_string(),
                        }),
                    ));
                }
            }

            let user_response = UserResponse::from(user);

            Ok(Json(user_response))
        }
        Err(e) => {
            tracing::error!("Failed to create user: {}", e);
            Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to create user".to_string(),
                }),
            ))
        }
    }
}

pub async fn handle_logout(session: Session) -> impl IntoResponse {
    if session.is_empty().await {
        return (StatusCode::OK, "Already logged out").into_response();
    }

    match session.delete().await {
        Ok(_) => (StatusCode::OK, "Logged out successfully").into_response(),
        Err(_) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            "Failed to destroy session",
        )
            .into_response(),
    }
}

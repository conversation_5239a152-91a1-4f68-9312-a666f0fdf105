use axum::{<PERSON><PERSON>, extract::Query, http::header::HeaderMap, response::Redirect};
use eyre::{Result, eyre};
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};

use super::{ErrorResponse, TELEGRAM_AUTH_PAGE_ROUTE};
use crate::{
    config::config,
    models::{OauthRequest, enums::OauthProvider},
    social::telegram::TelegramAuthData,
    storage::Storage,
    utils::is_unique_violation,
};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OAuthInitRequest {
    pub provider: String,
    pub redirect_uri: String,
    pub state: String,          // A unique identifier
    pub code_challenge: String, // SHA256 hash of the code_verifier
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OAuthInitResponse {
    pub client_id: String,
    pub url: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct OAuthCallbackParams {
    // Common params
    pub state: String,
    pub code: Option<String>,
    pub provider: Option<String>,
    // Telegram params
    pub id: Option<u64>,
    pub first_name: Option<String>,
    pub last_name: Option<String>,
    pub username: Option<String>,
    pub photo_url: Option<String>,
    pub auth_date: Option<u64>,
    pub hash: Option<String>,
}

pub async fn handle_oauth_init(
    headers: HeaderMap,
    Json(payload): Json<OAuthInitRequest>,
) -> Result<Json<OAuthInitResponse>, (StatusCode, Json<ErrorResponse>)> {
    let domain = get_domain(headers).map_err(|e| {
        (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: e.to_string(),
            }),
        )
    })?;

    let oauth_provider = match OauthProvider::from_str(payload.provider.as_str()) {
        Some(oauth_provider) => oauth_provider,
        None => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "Invalid provider".to_string(),
                }),
            ));
        }
    };

    let (client_id, url) = match oauth_provider {
        OauthProvider::Twitter => {
            if !config().enable_twitter {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Twitter OAuth Login is not enabled".to_string(),
                    }),
                ));
            }
            (config().twitter_client_id.clone(), get_callback_url(domain))
        }
        OauthProvider::Google => {
            if !config().enable_google {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Google OAuth Login is not enabled".to_string(),
                    }),
                ));
            }
            (config().google_client_id.clone(), get_callback_url(domain))
        }
        OauthProvider::Telegram => {
            if !config().enable_telegram_login && !config().enable_telegram_bind {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Telegram OAuth Login is not enabled".to_string(),
                    }),
                ));
            }
            let url = get_telegram_auth_url(&domain, &payload.state);
            (config().telegram_bot_name.clone(), url)
        }
    };

    let oauth_request = OauthRequest {
        provider: oauth_provider,
        redirect_uri: payload.redirect_uri,
        state: payload.state,
        code_challenge: payload.code_challenge,
        client_id: client_id.clone(),
        auth_data: None,
    };

    match Storage::create_oauth_request(oauth_request).await {
        Ok(_) => (),
        Err(e) => {
            if is_unique_violation(e.to_string().as_str()) {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Invalid state, please try again with a new state".to_string(),
                    }),
                ));
            }
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to create oauth request".to_string(),
                }),
            ));
        }
    }

    let response = OAuthInitResponse { url, client_id };

    Ok(Json(response))
}

pub async fn handle_oauth_callback(
    Query(params): Query<OAuthCallbackParams>,
) -> Result<Redirect, (StatusCode, Json<ErrorResponse>)> {
    let state = params.state;
    let mut code = params.code.unwrap_or_default();

    let mut oauth_request = match Storage::get_oauth_request(state.clone()).await {
        Ok(Some(oauth_request)) => oauth_request,
        Ok(None) => {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "Invalid state".to_string(),
                }),
            ));
        }
        Err(_) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get oauth request".to_string(),
                }),
            ));
        }
    };

    if let Some(provider) = params.provider {
        let oauth_provider = match OauthProvider::from_str(provider.as_str()) {
            Some(oauth_provider) => oauth_provider,
            None => {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Invalid provider".to_string(),
                    }),
                ));
            }
        };

        if oauth_provider == OauthProvider::Telegram {
            let id = params.id.ok_or_else(|| {
                (
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Telegram ID is required".to_string(),
                    }),
                )
            })?;
            let auth_date = params.auth_date.ok_or_else(|| {
                (
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Telegram auth date is required".to_string(),
                    }),
                )
            })?;
            let hash = params.hash.ok_or_else(|| {
                (
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "Telegram hash is required".to_string(),
                    }),
                )
            })?;

            // Use the hash of the auth data as the code
            code = hash.clone();

            let telegram_auth_data = TelegramAuthData {
                id,
                first_name: params.first_name,
                last_name: params.last_name,
                username: params.username,
                photo_url: params.photo_url,
                auth_date,
                hash,
            };

            let json_auth_data = serde_json::to_value(telegram_auth_data).map_err(|_| {
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse {
                        error: "Failed to serialize telegram auth data".to_string(),
                    }),
                )
            })?;
            oauth_request.auth_data = Some(json_auth_data);
            Storage::update_oauth_request(oauth_request.clone())
                .await
                .map_err(|_| {
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        Json(ErrorResponse {
                            error: "Failed to update oauth request".to_string(),
                        }),
                    )
                })?;
        }
    }

    let callback_url = format!(
        "{}?wallet_oauth_provider={}&wallet_oauth_state={}&wallet_oauth_code={}",
        oauth_request.redirect_uri,
        oauth_request.provider.to_str(),
        state,
        code
    );

    Ok(Redirect::to(&callback_url))
}

pub fn get_domain(headers: HeaderMap) -> Result<String> {
    let host = headers.get("host").and_then(|h| h.to_str().ok());
    let authority = headers.get("authority").and_then(|h| h.to_str().ok());
    let domain = host.or(authority);
    if let Some(domain) = domain {
        return Ok(domain
            .trim_start_matches("https://")
            .trim_end_matches('/')
            .to_string());
    } else {
        return Err(eyre!("Host or authority is not set"));
    }
}

pub fn get_callback_url(domain: String) -> String {
    format!("https://{domain}/oauth/callback")
}

pub fn get_callback_url_with_query(
    domain: String,
    provider: OauthProvider,
    state: String,
) -> String {
    format!(
        "https://{domain}/oauth/callback?provider={provider}&state={state}",
        provider = provider.to_str(),
        state = state,
    )
}

pub fn get_telegram_auth_url(domain: &str, state: &str) -> String {
    format!("https://{domain}{TELEGRAM_AUTH_PAGE_ROUTE}?state={state}")
}

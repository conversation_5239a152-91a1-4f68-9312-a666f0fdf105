use std::str::FromStr;

use axum::{<PERSON><PERSON>, extract::Query};
use eyre::Result;
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

use super::ErrorResponse;
use crate::{
    models::{Challenge, enums::ChallengeMethod},
    social::phantom::PhantomAdapter,
    storage::Storage,
};

#[derive(Debug, Deserialize)]
pub struct ChallengeQuery {
    pub method: ChallengeMethod,
    pub id: String, // wallet address for phantom
}

#[derive(Debug, Serialize)]
pub struct ChallengeResponse {
    pub challenge: String,
    pub nonce: String,
    pub expires_at: i64,
}

pub async fn handle_challenge(
    Query(payload): Query<ChallengeQuery>,
) -> Result<Json<ChallengeResponse>, (StatusCode, Json<ErrorResponse>)> {
    match payload.method {
        ChallengeMethod::Phantom => {
            let challenge = generate_phantom_challenge(&payload.id).await?;
            Ok(<PERSON><PERSON>(challenge))
        }
    }
}

pub async fn generate_phantom_challenge(
    wallet_address: &str,
) -> Result<ChallengeResponse, (StatusCode, Json<ErrorResponse>)> {
    let phantom_adapter = PhantomAdapter::get();

    if !phantom_adapter.is_enabled() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Phantom login is not enabled".to_string(),
            }),
        ));
    }

    let wallet_address = Pubkey::from_str(wallet_address).map_err(|e| {
        tracing::error!("Invalid wallet address: {}", e);
        (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Invalid wallet address".to_string(),
            }),
        )
    })?;

    let challenge = match phantom_adapter
        .generate_auth_challenge(wallet_address)
        .await
    {
        Ok(challenge) => challenge,
        Err(e) => {
            tracing::error!("Failed to generate phantom challenge: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to generate phantom challenge".to_string(),
                }),
            ));
        }
    };

    let challenge = Challenge {
        method: ChallengeMethod::Phantom,
        id: wallet_address.to_string(),
        challenge: challenge.challenge,
        nonce: challenge.nonce,
        expires_at: challenge.expires_at,
    };

    Storage::create_or_update_challenge(challenge.clone())
        .await
        .map_err(|e| {
            tracing::error!("Failed to create or update challenge: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to create or update challenge".to_string(),
                }),
            )
        })?;

    Ok(ChallengeResponse {
        challenge: challenge.challenge,
        nonce: challenge.nonce,
        expires_at: challenge.expires_at,
    })
}

use axum::<PERSON><PERSON>;
use eyre::{Result, eyre};
use reqwest::StatusCode;
use tower_sessions::Session;
use uuid::Uuid;

use super::ErrorResponse;
use crate::{
    models::{User2, UserResponse},
    storage::Storage,
};

pub const SESSION_KEY: &str = "user_id";

pub async fn handle_session(
    session: Session,
) -> Result<Json<UserResponse>, (StatusCode, Json<ErrorResponse>)> {
    match check_session(session).await {
        Ok(user) => {
            let user_response = UserResponse::from(user);

            Ok(Json(user_response))
        }
        Err(e) => Err((
            StatusCode::UNAUTHORIZED,
            Json(ErrorResponse {
                error: e.to_string(),
            }),
        )),
    }
}

pub async fn check_session(session: Session) -> Result<User2> {
    match session.get::<String>(SESSION_KEY).await {
        Ok(Some(user_id)) => {
            tracing::debug!("User ID: {}", user_id);
            let user_id = Uuid::parse_str(&user_id)?;
            let user = Storage::get_user2(user_id).await?;
            if let Some(user) = user {
                Ok(user)
            } else {
                Err(eyre!("User not found"))
            }
        }
        Ok(None) => {
            tracing::debug!("No user ID found in session");
            Err(eyre!("Not authenticated"))
        }
        Err(e) => {
            tracing::error!("Error checking session: {}", e);
            Err(eyre!("Not authenticated"))
        }
    }
}

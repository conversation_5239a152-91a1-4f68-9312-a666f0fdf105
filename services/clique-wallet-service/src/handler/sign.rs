use axum::{<PERSON><PERSON>, http::HeaderMap, response::IntoResponse};
use clique_wallet_signer_types::Network;
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};
use tower_sessions::Session;
use uuid::Uuid;

use super::ErrorResponse;
use crate::core::sign::sign;
use crate::models::{User2, api_key::authenticate_user_by_api_key};

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SignRequest {
    pub address: String,
    pub user_id: String,
    pub message: String,
    pub network: Network,
    pub request_id: String,
}

pub async fn handle_sign(
    session: Session,
    Json(sign_request): Json<SignRequest>,
) -> impl IntoResponse {
    let user = match super::session::check_session(session).await {
        Ok(user) => user,
        Err(_) => {
            return (
                StatusCode::UNAUTHORIZED,
                <PERSON>son(ErrorResponse {
                    error: "Invalid session".to_string(),
                }),
            )
                .into_response();
        }
    };

    let user_id = match Uuid::parse_str(&sign_request.user_id) {
        Ok(user_id) => user_id,
        Err(_) => {
            return (
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "Invalid user ID".to_string(),
                }),
            )
                .into_response();
        }
    };
    if user.id != user_id {
        return (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "User ID mismatch".to_string(),
            }),
        )
            .into_response();
    }

    // TODO: Check the request id
    // TODO: Check if the message is valid

    perform_sign(user, sign_request).await.into_response()
}

/// Common signing logic shared between session and API key authentication
async fn perform_sign(user: User2, sign_request: SignRequest) -> impl IntoResponse {
    // Check if user owns the wallet
    let has_corresponding_wallet = user
        .wallets
        .iter()
        .any(|w| w.address == sign_request.address && w.network == sign_request.network);
    if !has_corresponding_wallet {
        return (
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Address mismatch".to_string(),
            }),
        )
            .into_response();
    }

    // TODO: Check the request id
    // TODO: Check if the message is valid

    match sign(
        user,
        sign_request.address,
        sign_request.network,
        sign_request.message,
    )
    .await
    {
        Ok(response) => (StatusCode::OK, Json(response)).into_response(),
        Err(e) => (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: e.to_string(),
            }),
        )
            .into_response(),
    }
}

/// API version of sign handler using API key authentication
pub async fn handle_api_sign(
    headers: HeaderMap,
    Json(sign_request): Json<SignRequest>,
) -> impl IntoResponse {
    // Authenticate using API key
    let user = match authenticate_user_by_api_key(&headers).await {
        Ok(user) => user,
        Err(response) => return response.into_response(),
    };

    // Note: user_id is ignored in API version since we get user from API key
    perform_sign(user, sign_request).await.into_response()
}

mod address;
mod api_key;
mod bind;
mod challenge;
mod coinbase;
pub(crate) mod login;
mod oauth;
mod send;
mod session;
mod sign;
mod telegram;

pub use address::handle_address;
pub use api_key::{handle_create_api_key, handle_delete_api_key, handle_list_api_keys};
pub use bind::handle_bind;
pub use challenge::handle_challenge;
pub use coinbase::{handle_coinbase_offramp_transaction_status, handle_coinbase_session_token};
pub use login::{handle_login, handle_logout};
pub use oauth::{handle_oauth_callback, handle_oauth_init};
pub use send::handle_send_verification;
pub use session::handle_session;
pub use sign::{SignRequest, handle_api_sign, handle_sign};
pub use telegram::{TELEGRAM_AUTH_PAGE_ROUTE, telegram_auth_page};

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct ErrorResponse {
    pub error: String,
}

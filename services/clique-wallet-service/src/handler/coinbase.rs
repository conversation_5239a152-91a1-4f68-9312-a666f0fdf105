use axum::{<PERSON><PERSON>, extract::Query};
use eyre::Result;
use reqwest::StatusCode;
use serde::Deserialize;
use tower_sessions::Session;

use super::ErrorResponse;
use crate::coinbase::{
    CoinbaseJwtBuilder, OfframpTransactionStatusResponse, SessionTokenApiPayload,
    SessionTokenApiResponse,
};

#[derive(Debug, Deserialize)]
pub struct OfframpTransactionStatusApiParams {
    pub partner_user_id: String,
    pub page_key: Option<String>,
    pub page_size: Option<u32>,
}

pub async fn handle_coinbase_session_token(
    _session: Session,
    Json(payload): Json<SessionTokenApiPayload>,
) -> Result<Json<SessionTokenApiResponse>, (StatusCode, Json<ErrorResponse>)> {
    // let _user = match super::session::check_session(session).await {
    //     Ok(user) => user,
    //     Err(_) => {
    //         return Err((
    //             StatusCode::UNAUTHORIZED,
    //             Json(ErrorResponse { error: "Invalid session".to_string() }),
    //         ));
    //     }
    // };

    let builder = CoinbaseJwtBuilder::get();
    if !builder.enable_coinbase() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Coinbase is not enabled".to_string(),
            }),
        ));
    }

    let session_token = match builder.get_session_token(payload).await {
        Ok(session_token) => session_token,
        Err(_) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get coinbase session token".to_string(),
                }),
            ));
        }
    };

    Ok(Json(session_token))
}

pub async fn handle_coinbase_offramp_transaction_status(
    _session: Session,
    Query(params): Query<OfframpTransactionStatusApiParams>,
) -> Result<Json<OfframpTransactionStatusResponse>, (StatusCode, Json<ErrorResponse>)> {
    // let _user = match super::session::check_session(session).await {
    //     Ok(user) => user,
    //     Err(_) => {
    //         return Err((
    //             StatusCode::UNAUTHORIZED,
    //             Json(ErrorResponse { error: "Invalid session".to_string() }),
    //         ));
    //     }
    // };

    let builder = CoinbaseJwtBuilder::get();
    if !builder.enable_coinbase() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Coinbase is not enabled".to_string(),
            }),
        ));
    }

    let partner_user_id = params.partner_user_id;
    let page_key = params.page_key;
    let page_size = params.page_size;

    let tx_status = match builder
        .get_offramp_transaction_status(&partner_user_id, page_key.as_deref(), page_size)
        .await
    {
        Ok(tx_status) => tx_status,
        Err(_) => {
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get coinbase offramp transaction status".to_string(),
                }),
            ));
        }
    };

    Ok(Json(tx_status))
}

use axum::{<PERSON><PERSON>, extract::Query};
use clique_wallet_signer_types::Network;
use eyre::Result;
use reqwest::StatusCode;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use super::ErrorResponse;
use crate::{
    models::{MinimalWallet, WalletResponse},
    storage::Storage,
};

#[derive(Debug, Serialize, Deserialize)]
pub struct AddressQuery {
    pub address: String,
    pub network: Network,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AddressResponse {
    pub id: Uuid,
    pub wallets: Vec<WalletResponse>,
}

pub async fn handle_address(
    Query(address_query): Query<AddressQuery>,
) -> Result<Json<AddressResponse>, (StatusCode, Json<ErrorResponse>)> {
    let minimal_wallet = MinimalWallet {
        address: address_query.address.clone(),
        network: address_query.network.clone(),
    };
    let user = Storage::get_user2_by_wallet(&minimal_wallet)
        .await
        .map_err(|_| {
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                <PERSON><PERSON>(ErrorResponse {
                    error: "Database error".to_string(),
                }),
            )
        })?;

    if let Some(user) = user {
        Ok(Json(AddressResponse {
            id: user.id,
            wallets: user.wallets.into_iter().map(Into::into).collect(),
        }))
    } else {
        Err((
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "Wallet not found".to_string(),
            }),
        ))
    }
}

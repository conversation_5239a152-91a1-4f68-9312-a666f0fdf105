use axum::{
    <PERSON><PERSON>,
    extract::Query,
    http::{StatusCode, header::HeaderMap},
    response::Html,
};
use serde::Deserialize;

use super::ErrorResponse;
use crate::{config::config, models::enums::OauthProvider};

pub const TELEGRAM_AUTH_PAGE_ROUTE: &str = "/auth/telegram";

#[derive(Debug, Deserialize)]
pub struct TelegramPageParams {
    pub state: String,
}

pub async fn telegram_auth_page(
    headers: HeaderMap,
    Query(params): Query<TelegramPageParams>,
) -> Result<Html<String>, (StatusCode, Json<ErrorResponse>)> {
    let config = config();
    if !config.enable_telegram_login && !config.enable_telegram_bind {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Telegram auth is not enabled".to_string(),
            }),
        ));
    }

    let mut html = config
        .telegram_auth_page_template_str
        .clone()
        .unwrap_or_default();
    if html.is_empty() {
        return Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "Telegram auth page template is not set".to_string(),
            }),
        ));
    }

    let domain = super::oauth::get_domain(headers).map_err(|_| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: "Failed to get domain".to_string(),
            }),
        )
    })?;
    let callback_url =
        super::oauth::get_callback_url_with_query(domain, OauthProvider::Telegram, params.state);

    let title = format!("{} Telegram Auth", config.wallet_name);
    let bot_name = &config.telegram_bot_name;

    // Replace placeholders
    html = html.replace("{title}", &title);
    html = html.replace("{bot_name}", &bot_name);
    html = html.replace("{callback_url}", &callback_url);

    Ok(Html(html))
}

use axum::<PERSON><PERSON>;
use eyre::Result;
use reqwest::StatusCode;
use serde::Deserialize;

use super::ErrorResponse;
use crate::{
    social::{
        email::EmailProvider,
        sms::{PreludeProvider, prelude},
    },
    storage::Storage,
    utils::get_current_timestamp,
};

#[derive(Debug, Deserialize)]
pub struct SendVerificationRequest {
    email: Option<String>,
    phone: Option<String>,
}

pub async fn handle_send_verification(
    Json(payload): Json<SendVerificationRequest>,
) -> Result<Json<String>, (StatusCode, Json<ErrorResponse>)> {
    match (payload.email, payload.phone) {
        (Some(email), None) => send_verification_via_email(email).await,
        (None, Some(phone)) => send_verification_via_phone(phone).await,
        (Some(_), Some(_)) => Err((
            StatusCode::BAD_REQUEST,
            <PERSON><PERSON>(ErrorResponse {
                error: "Email and phone cannot be provided together".to_string(),
            }),
        )),
        (None, None) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Email or phone is required".to_string(),
            }),
        )),
    }
}

async fn send_verification_via_email(
    email: String,
) -> Result<Json<String>, (StatusCode, Json<ErrorResponse>)> {
    let email = email.trim();

    if !EmailProvider::validate_email_address(email) {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Invalid email address format".to_string(),
            }),
        ));
    }

    let verification = match Storage::get_email_verification(email).await {
        Ok(verification) => verification,
        Err(e) => {
            tracing::error!("Failed to get user's email verification status: {}", e);
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "Failed to get user's email verification status".to_string(),
                }),
            ));
        }
    };

    let mut preferred_backup_provider = false;
    if let Some(verification) = verification {
        if !verification.can_resend() {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "Please wait before requesting another code".to_string(),
                }),
            ));
        }
        // if the last verification is sent less than 2 minutes ago, it means the user might be
        // failed to receive the email by the main provider, so we should prefer the backup
        // provider to improve the success rate
        if verification.timestamp + 60 * 2 >= get_current_timestamp() {
            preferred_backup_provider = true;
        }
    }

    // Generate and store code
    let code = EmailProvider::generate_verification_code();
    match Storage::add_email_verification(email, code.clone()).await {
        Ok(_) => (),
        Err(e) => {
            tracing::error!("Failed to store email verification: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to store email verification".to_string(),
                }),
            ));
        }
    };

    // Send email
    match EmailProvider::send_email(email, &code, preferred_backup_provider).await {
        Ok(_) => Ok(Json("Verification code sent".to_string())),
        Err(e) => Err((
            StatusCode::INTERNAL_SERVER_ERROR,
            Json(ErrorResponse {
                error: e.to_string(),
            }),
        )),
    }
}

fn validate_phone(phone: &str) -> Result<(), (StatusCode, Json<ErrorResponse>)> {
    if !phone.starts_with('+') {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Phone number must start with '+' followed by country code and digits"
                    .to_string(),
            }),
        ));
    }

    let digits_only = &phone[1..];
    if !digits_only.chars().all(|c| c.is_ascii_digit()) {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "Phone number must contain only digits after the '+' prefix".to_string(),
            }),
        ));
    }

    Ok(())
}

async fn send_verification_via_phone(
    phone: String,
) -> Result<Json<String>, (StatusCode, Json<ErrorResponse>)> {
    let phone = phone.trim();
    validate_phone(phone)?;

    let sms_provider = PreludeProvider::get();

    if !sms_provider.is_enabled() {
        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "SMS verification is not enabled".to_string(),
            }),
        ));
    }

    let phone_verification = match Storage::get_phone_verification(phone).await {
        Ok(verification) => verification,
        Err(e) => {
            tracing::error!("Failed to get user's phone verification status: {}", e);
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "Failed to get user's phone verification status".to_string(),
                }),
            ));
        }
    };
    if let Some(phone_verification) = phone_verification.as_ref() {
        if !phone_verification.can_resend() {
            return Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: "Please wait before requesting another phone verification code"
                        .to_string(),
                }),
            ));
        }
    }

    let response = match sms_provider.send_sms_verification(phone).await {
        Ok(response) => response,
        Err(e) => {
            tracing::error!("Failed to send SMS verification: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to send SMS verification".to_string(),
                }),
            ));
        }
    };

    if let Some(mut phone_verification) = phone_verification {
        phone_verification.add_verification(response.id.clone());
        if let Err(e) = Storage::update_phone_verification(phone_verification).await {
            tracing::error!("Failed to update phone verification: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to update phone verification".to_string(),
                }),
            ));
        }
    } else {
        if let Err(e) = Storage::add_phone_verification(phone, response.id.clone()).await {
            tracing::error!("Failed to store phone verification: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to store phone verification".to_string(),
                }),
            ));
        }
    }

    match response.status {
        prelude::VerificationStatus::Success => {
            Ok(Json("Verification code sent successfully".to_string()))
        }
        prelude::VerificationStatus::Blocked => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse { error: "Phone number is blocked, it's might because of this number is blocked or you have sent too many sms to this number today".to_string() }),
        )),
        prelude::VerificationStatus::Retry => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse { error: "Failed to send SMS verification, please try again later after at least 30 seconds".to_string() }),
        )),
    }
}

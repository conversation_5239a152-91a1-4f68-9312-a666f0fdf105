use axum::{<PERSON><PERSON>, http::header::HeaderMap};
use eyre::Result;
use reqwest::StatusCode;
use tower_sessions::Session;

use super::{ErrorResponse, login::LoginRequest};
use crate::{
    core::create::VerifiedLoginRequest,
    models::{
        User2, UserResponse,
        enums::{SocialInfo, SocialProvider},
    },
    storage::Storage,
};

pub async fn handle_bind(
    headers: HeaderMap,
    session: Session,
    Json(login_request): <PERSON><PERSON><LoginRequest>,
) -> Result<Json<UserResponse>, (StatusCode, Json<ErrorResponse>)> {
    let mut current_user = match super::session::check_session(session).await {
        Ok(user) => user,
        Err(_) => {
            return Err((
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    error: "Invalid session".to_string(),
                }),
            ));
        }
    };

    let verified_login_request = super::login::verify_login_request(headers, login_request).await?;

    if current_user
        .social_links
        .contains(&verified_login_request.social)
    {
        // update social info if has new social info
        update_user_social_info(current_user.clone(), verified_login_request).await?;

        return Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: "You have already bound to this social account".to_string(),
            }),
        ));
    }

    match Storage::get_user2_by_social(&verified_login_request.social).await {
        Ok(Some(corresponding_user)) => {
            if corresponding_user.id == current_user.id {
                // update social info if has new social info
                update_user_social_info(current_user.clone(), verified_login_request).await?;

                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "You have already bound to this social account".to_string(),
                    }),
                ));
            } else {
                return Err((
                    StatusCode::BAD_REQUEST,
                    Json(ErrorResponse {
                        error: "This social account has already been bound to another user"
                            .to_string(),
                    }),
                ));
            }
        }
        Ok(None) => (),
        Err(e) => {
            tracing::error!("Failed to get user from storage: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to get user from storage".to_string(),
                }),
            ));
        }
    };

    let mut new_social_links = vec![verified_login_request.social.clone()];
    if let Some(social_info) = verified_login_request.social_info {
        if let SocialInfo::Google(google_user_info) = &social_info {
            new_social_links.push(SocialProvider::GoogleAccountEmail(
                google_user_info.email.clone(),
            ));
        }
        current_user.update_social_info(verified_login_request.social.clone(), social_info);
    }
    current_user.social_links.extend(new_social_links.clone());

    match Storage::add_social_links_for_user(current_user.clone(), new_social_links).await {
        Ok(_) => Ok(Json(UserResponse::from(current_user))),
        Err(e) => {
            tracing::error!("Failed to update user: {}", e);
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to update user".to_string(),
                }),
            ));
        }
    }
}

async fn update_user_social_info(
    mut user: User2,
    verified_login_request: VerifiedLoginRequest,
) -> Result<(), (StatusCode, Json<ErrorResponse>)> {
    if let Some(social_info) = verified_login_request.social_info {
        if user.update_social_info(verified_login_request.social.clone(), social_info.clone()) {
            Storage::update_user2(user).await.map_err(|e| {
                tracing::error!("Failed to update user: {}", e);
                (
                    StatusCode::INTERNAL_SERVER_ERROR,
                    Json(ErrorResponse {
                        error: "Failed to update user".to_string(),
                    }),
                )
            })?;
        }
    }

    Ok(())
}

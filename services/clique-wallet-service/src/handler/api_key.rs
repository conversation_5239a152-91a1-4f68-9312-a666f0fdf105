use axum::{<PERSON><PERSON>, extract::Path, http::StatusCode, response::IntoResponse};
use tower_sessions::Session;
use uuid::Uuid;

use super::{ErrorResponse, session::check_session};
use crate::models::{
    User2,
    api_key::{<PERSON><PERSON><PERSON><PERSON>, CreateApi<PERSON>eyRequest},
};

/// Create a new API key
pub async fn handle_create_api_key(
    session: Session,
    Json(request): Json<CreateApiKeyRequest>,
) -> impl IntoResponse {
    // Check session authentication
    let user = match check_session(session).await {
        Ok(user) => user,
        Err(e) => {
            return (
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    error: "Authentication failed".to_string(),
                }),
            )
                .into_response();
        }
    };

    // Validate request
    if request.name.trim().is_empty() {
        return (
            StatusCode::BAD_REQUEST,
            <PERSON>son(ErrorResponse {
                error: "API key name cannot be empty".to_string(),
            }),
        )
            .into_response();
    }

    // Create API key
    match ApiKey::create(user.id, request.name, request.expires_at).await {
        Ok(response) => (StatusCode::CREATED, Json(response)).into_response(),
        Err(e) => {
            tracing::error!("Failed to create API key: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to create API key".to_string(),
                }),
            )
                .into_response()
        }
    }
}

/// List user's API keys
pub async fn handle_list_api_keys(session: Session) -> impl IntoResponse {
    // Check session authentication
    let user = match check_session(session).await {
        Ok(user) => user,
        Err(e) => {
            return (
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    error: "Authentication failed".to_string(),
                }),
            )
                .into_response();
        }
    };

    // Get API keys
    match ApiKey::find_by_user_id(user.id).await {
        Ok(api_keys) => (StatusCode::OK, Json(api_keys)).into_response(),
        Err(e) => {
            tracing::error!("Failed to list API keys: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to list API keys".to_string(),
                }),
            )
                .into_response()
        }
    }
}

/// Delete an API key
pub async fn handle_delete_api_key(
    session: Session,
    Path(key_id): Path<Uuid>,
) -> impl IntoResponse {
    // Check session authentication
    let user = match check_session(session).await {
        Ok(user) => user,
        Err(e) => {
            return (
                StatusCode::UNAUTHORIZED,
                Json(ErrorResponse {
                    error: "Authentication failed".to_string(),
                }),
            )
                .into_response();
        }
    };

    // Delete API key
    match ApiKey::deactivate(key_id, user.id).await {
        Ok(true) => (StatusCode::NO_CONTENT, "").into_response(),
        Ok(false) => (
            StatusCode::NOT_FOUND,
            Json(ErrorResponse {
                error: "API key not found".to_string(),
            }),
        )
            .into_response(),
        Err(e) => {
            tracing::error!("Failed to delete API key: {}", e);
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ErrorResponse {
                    error: "Failed to delete API key".to_string(),
                }),
            )
                .into_response()
        }
    }
}

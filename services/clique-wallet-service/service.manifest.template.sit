# Rust manifest example

libos.entrypoint = "{{ self_exe }}"

loader.log_level = "{{ log_level }}"

loader.env.LD_LIBRARY_PATH = "/lib:{{ arch_libdir }}"

# See https://gramine.readthedocs.io/en/latest/devel/performance.html#glibc-malloc-tuning
loader.env.MALLOC_ARENA_MAX = "1"

# For easier debugging — not strictly required to run this workload
loader.env.RUST_BACKTRACE = "full"

fs.mounts = [
  { path = "/lib", uri = "file:{{ gramine.runtimedir() }}" },
  { path = "{{ arch_libdir }}", uri = "file:{{ arch_libdir }}" },
  { path = "/etc/ssl/certs", uri = "file:ca-certificates.crt" },
  { path = "/usr/lib/ssl/certs", uri = "file:ca-certificates.crt" },
]

sys.enable_extra_runtime_domain_names_conf = true
sys.fds.limit = 65535

sgx.debug = false
sgx.edmm_enable = false
sgx.enclave_size = "2G"
sgx.remote_attestation = "dcap"

sgx.trusted_files = [
  "file:{{ gramine.libos }}",
  "file:{{ self_exe }}",
  "file:{{ gramine.runtimedir() }}/",
  "file:{{ arch_libdir }}/",
  "file:ca-certificates.crt",
  "file:migrations/",
  "file:templates/",
]

# The maximum number of threads in a single process needs to be declared in advance.
# You need to account for:
# - one main thread
# - any threads and threadpools you might be starting
# - helper threads internal to Gramine — see:
#   https://gramine.readthedocs.io/en/latest/manifest-syntax.html#number-of-threads
sgx.max_threads = 256


loader.env.SGX = "1"

loader.env.RUST_LOG = "info"

loader.env.SIGNER_ENDPOINT = "https://k4-wallet-signer.k4-wallet-signer.svc.cluster.local"

loader.env.PORT = "3000"
loader.env.SENSITIVE_SERVICE_PORT = "3003"

loader.env.KMS_URL = "https://k4-protocol-kms.k4-protocol-kms.svc.cluster.local"

loader.env.KMS_MR_ENCLAVE = ""
loader.env.KMS_MR_SIGNER = "3dbcabf8fb2df803e495c6e49d52c3ebde2a9716fdc3fc7a218f37e5747974bf"

loader.env.MR_SIGNER = "3dbcabf8fb2df803e495c6e49d52c3ebde2a9716fdc3fc7a218f37e5747974bf"

loader.env.TRUSTED_ENCLAVES = "all"
loader.env.TRUSTED_SIGNERS = "3dbcabf8fb2df803e495c6e49d52c3ebde2a9716fdc3fc7a218f37e5747974bf"

loader.env.POSTGRES_USER = { passthrough = true }
loader.env.POSTGRES_PASSWORD = { passthrough = true }
loader.env.POSTGRES_HOST = { passthrough = true }
loader.env.POSTGRES_DB = { passthrough = true }
loader.env.POSTGRES_REQUIRE_SSL = { passthrough = true }
loader.env.POSTGRES_MAX_CONNECTIONS = { passthrough = true }

loader.env.CERT_SUBJECT_NAME = "wallet.test.superstack.xyz"

loader.env.WALLET_NAME = "Superstack"

loader.env.ENABLE_TWITTER = "false"
loader.env.TWITTER_CLIENT_ID = ""
loader.env.TWITTER_CLIENT_SECRET = ""

loader.env.ENABLE_GOOGLE = { passthrough = true }
loader.env.GOOGLE_CLIENT_ID = { passthrough = true }
loader.env.GOOGLE_CLIENT_SECRET = { passthrough = true }

loader.env.ENABLE_TELEGRAM_LOGIN = "false"
loader.env.ENABLE_TELEGRAM_BIND = "false"
loader.env.TELEGRAM_BOT_TOKEN = ""
loader.env.TELEGRAM_BOT_NAME = ""
loader.env.TELEGRAM_AUTH_PAGE_TEMPLATE_PATH = "templates/telegram.html"
loader.env.TELEGRAM_OUTER_BOT_TOKEN = ""

loader.env.ENABLE_EMAIL = { passthrough = true }
loader.env.EMAIL_TEMPLATE_PATH = "templates/superstack/superstack_email_one_logo.html"

loader.env.ENABLE_AZURE_EMAIL = { passthrough = true }
loader.env.AZURE_EMAIL_SERVICE_URL = { passthrough = true }
loader.env.AZURE_EMAIL_SERVICE_API_VERSION = { passthrough = true }
loader.env.AZURE_EMAIL_SERVICE_API_KEY = { passthrough = true }
loader.env.AZURE_EMAIL_SERVICE_FROM_ADDRESS = { passthrough = true }

loader.env.ENABLE_MAILTRAP_EMAIL = { passthrough = true }
loader.env.MAILTRAP_EMAIL_SERVICE_API_KEY = { passthrough = true }
loader.env.MAILTRAP_EMAIL_SERVICE_FROM_ADDRESS = { passthrough = true }

loader.env.ENABLE_SMS = { passthrough = true }
loader.env.SMS_PRELUDE_API_KEY = { passthrough = true }

loader.env.ENABLE_PHANTOM = { passthrough = true }

loader.env.ENABLE_COINBASE = { passthrough = true }
loader.env.COINBASE_API_KEY = { passthrough = true }
loader.env.COINBASE_API_SECRET = { passthrough = true }

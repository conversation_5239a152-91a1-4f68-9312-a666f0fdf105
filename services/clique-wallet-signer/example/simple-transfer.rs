use solana_client::rpc_client::RpcClient;
use solana_sdk::{
    commitment_config::CommitmentConfig,
    message::{v0, Message, VersionedMessage},
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    system_instruction,
    transaction::{TransactionVersion, VersionedTransaction},
};
use std::str::FromStr;

#[tokio::main]
async fn main() {
    // Connect to the Solana devnet cluster
    let rpc_url = String::from("https://api.devnet.solana.com");
    let client = RpcClient::new_with_commitment(rpc_url, CommitmentConfig::confirmed());

    // Create a new keypair for the sender
    let sender = Keypair::from_bytes(&[
        245, 91, 54, 11, 173, 152, 121, 248, 209, 31, 253, 242, 168, 61, 226, 191, 240, 11, 212,
        30, 11, 253, 118, 2, 111, 13, 151, 181, 81, 176, 195, 169, 96, 67, 129, 29, 36, 190, 75,
        160, 48, 93, 155, 135, 77, 214, 125, 32, 87, 177, 9, 177, 120, 180, 205, 130, 38, 50, 215,
        208, 5, 51, 246, 85,
    ])
    .unwrap();

    // Specify the recipient's public key
    let recipient = Pubkey::from_str("3FuYExoXrapPVRAmGTJEsiSig9jgz5p6Y8cq8yY1KT9i").unwrap();

    // Amount to transfer in lamports (1 SOL = 1_000_000_000 lamports)
    let amount_in_lamports = 1_000_000; // 0.001 SOL

    // First, request an airdrop of SOL to the sender's account for testing
    let sender_balance_before = client
        .get_balance(&sender.pubkey())
        .expect("Failed to get balance");

    if sender_balance_before == 0 {
        let signature = client
            .request_airdrop(&sender.pubkey(), amount_in_lamports * 2)
            .expect("Failed to request airdrop");

        // Confirm transaction
        client
            .confirm_transaction(&signature)
            .expect("Failed to confirm airdrop transaction");

        println!("Airdrop received!");
    }

    // Create a transfer instruction
    let instruction =
        system_instruction::transfer(&sender.pubkey(), &recipient, amount_in_lamports);

    // Get recent blockhash
    let recent_blockhash = client
        .get_latest_blockhash()
        .expect("Failed to get recent blockhash");

    // Create a message containing the instruction
    let message = VersionedMessage::V0(
        v0::Message::try_compile(&sender.pubkey(), &[instruction], &[], recent_blockhash).unwrap(),
    );

    // Create and sign the versioned transaction
    // let sig = sender.sign_message(message.serialize().as_ref());
    let msg_str = bs58::encode(message.serialize()).into_string();
    println!("sender: {}", sender.pubkey());
    println!("msg_str: {}", msg_str);
    let response = reqwest::get(format!("http://localhost:3000/sign?user=mock&share=jQ66Jg6SkkgmxpAKDu4y9uwKPeWgZQytJQR5AJmq6R7&network=Solana&msg={}", bs58::encode(message.serialize()).into_string())).await.unwrap();
    let response = response.text().await.unwrap();

    println!("response: {}", response);

    let transaction = VersionedTransaction {
        message,
        signatures: vec![response.parse().unwrap()],
    };

    // Send and confirm transaction
    let signature = client
        .send_and_confirm_transaction(&transaction)
        .expect("Failed to send transaction");

    println!("Transaction successful!");
    println!("Signature: {}", signature);

    // Get final balances
    let sender_balance = client
        .get_balance(&sender.pubkey())
        .expect("Failed to get sender balance");
    let recipient_balance = client
        .get_balance(&recipient)
        .expect("Failed to get recipient balance");

    println!("Sender balance: {} lamports", sender_balance);
    println!("Recipient balance: {} lamports", recipient_balance);
}

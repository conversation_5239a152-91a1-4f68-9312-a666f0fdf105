[package]
name = "clique-wallet-signer"
version = "0.1.0"
edition = "2021"

[dependencies]
axum = { version = "0.8.1", features = ["query"] }
axum-server = { version = "0.7.1", features = ["tls-rustls"] }
hex = "0.4.3"
hex-literal = "1.0.0"
k256 = { version = "0.13.4", features = ["ecdh", "ecdsa"] }
pbkdf2 = "0.12.2"
rand = "0.9.0"
serde = { version = "1.0", features = ["derive"] }
tokio = { version = "1.43.0", features = ["full"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
bs58 = "0.5.1"
eyre = "0.6.12"
aes-gcm = "0.10.3"
ed25519 = "2.2.3"
ed25519-dalek = "2.1.1"
sha3 = "0.10.8"
reqwest = { version = "0.12.12", features = ["json", "blocking", "rustls-tls"] }
serde_json = "1.0.139"
tokio-rustls = { version = "0.26.2" }
rustls-pemfile = "2.2.0"
futures-util = "0.3"
tower = "0.5.2"
x509-parser = "0.16"
asn1-rs = "0.6.1"
bip32 = "0.5.3"

clique-wallet-signer-types = { path = "../clique-wallet-signer-types" }

# Clique
clique-sibyl-commonlib = { git = "https://github.com/CliqueOfficial/clique-sibyl-commonlib.git", tag = "v2.5.2", features = ["rustls-0_23"] }

[dev-dependencies]
solana-client = "2.2.0"
solana-sdk = "2.2.1"

[[example]]
name = "simple-transfer"
path = "example/simple-transfer.rs"

[features]
default = []
mock = []

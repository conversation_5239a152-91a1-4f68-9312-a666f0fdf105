# Rust manifest example

libos.entrypoint = "{{ self_exe }}"

loader.log_level = "{{ log_level }}"

loader.env.LD_LIBRARY_PATH = "/lib:{{ arch_libdir }}"

# See https://gramine.readthedocs.io/en/latest/devel/performance.html#glibc-malloc-tuning
loader.env.MALLOC_ARENA_MAX = "1"

# For easier debugging — not strictly required to run this workload
loader.env.RUST_BACKTRACE = "full"

fs.mounts = [
  { path = "/lib", uri = "file:{{ gramine.runtimedir() }}" },
  { path = "{{ arch_libdir }}", uri = "file:{{ arch_libdir }}" },
]

sys.enable_extra_runtime_domain_names_conf = true
sys.fds.limit = 65535

sgx.debug = false
sgx.edmm_enable = false
sgx.enclave_size = "2G"
sgx.remote_attestation = "dcap"

sgx.trusted_files = [
  "file:{{ gramine.libos }}",
  "file:{{ self_exe }}",
  "file:{{ gramine.runtimedir() }}/",
  "file:{{ arch_libdir }}/",
]

# The maximum number of threads in a single process needs to be declared in advance.
# You need to account for:
# - one main thread
# - any threads and threadpools you might be starting
# - helper threads internal to Gramine — see:
#   https://gramine.readthedocs.io/en/latest/manifest-syntax.html#number-of-threads
sgx.max_threads = 32


loader.env.SGX = "1"

loader.env.RUST_LOG = "info"

loader.env.MR_SIGNER = "3dbcabf8fb2df803e495c6e49d52c3ebde2a9716fdc3fc7a218f37e5747974bf"

loader.env.KMS_URL = "https://k4-protocol-kms.k4-protocol-kms.svc.cluster.local"

loader.env.KMS_MR_ENCLAVE = ""
loader.env.KMS_MR_SIGNER = "3dbcabf8fb2df803e495c6e49d52c3ebde2a9716fdc3fc7a218f37e5747974bf"

loader.env.TRUSTED_ENCLAVES = "all"
loader.env.TRUSTED_SIGNERS = "3dbcabf8fb2df803e495c6e49d52c3ebde2a9716fdc3fc7a218f37e5747974bf"

loader.env.SENSITIVE_SERVICE_PORT = "3000"
loader.env.NON_SENSITIVE_SERVICE_PORT = "3001"

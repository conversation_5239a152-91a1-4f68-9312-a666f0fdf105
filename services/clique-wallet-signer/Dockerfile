FROM stuart2024/gramine:1.7-jammy-patched-v2 AS builder

COPY . /clique-wallet-signer

RUN apt update && apt install -y libclang-dev

ENV PATH="/root/.cargo/bin:${PATH}"

# ENV Cargo enable net.git-fetch-with-cli
ENV CARGO_NET_GIT_FETCH_WITH_CLI=true

RUN --mount=type=secret,id=gitconfig,target=/root/.gitconfig \
    --mount=type=secret,id=enclave-key,target=/root/.config/gramine/enclave-key.pem \
    bash -c "cd /clique-wallet-signer && make clean && make SGX=1"

RUN mkdir -p /compiled/target/release
RUN cp /clique-wallet-signer/signer.manifest /compiled
RUN cp /clique-wallet-signer/signer.manifest.sgx /compiled
RUN cp /clique-wallet-signer/signer.sig /compiled
RUN cp /clique-wallet-signer/target/release/clique-wallet-signer /compiled/target/release

RUN gramine-sgx-sigstruct-view /compiled/signer.sig

# TODO: use a light docker image
FROM stuart2024/gramine:1.7-jammy-patched-v2

COPY --from=builder /compiled /clique-wallet-signer

RUN echo "#!/bin/bash" > /run.sh

RUN echo "/restart_aesm.sh && cd /clique-wallet-signer && gramine-sgx-sigstruct-view signer.sig && gramine-sgx signer" >> /run.sh
RUN chmod +x /run.sh
ENTRYPOINT ["/run.sh"]

use axum::{http::StatusC<PERSON>, J<PERSON>};
use clique_wallet_signer_types::{
    CreatePayload, CreateResponse, CreateSingleWalletResponse, ErrorResponse, Network, WalletSet,
};
use eyre::{eyre, Result};

use crate::derive;

/// Create a share for a user
///
/// # Arguments
///
/// * `user` - The user to create the share for
/// * `network` - The network to create the share for
pub async fn handle_create(
    Json(payload): Json<CreatePayload>,
) -> Result<Json<CreateResponse>, (StatusCode, Json<ErrorResponse>)> {
    // Only log the user id, never log the sensitive data
    tracing::info!("Handle create for user: {}", payload.user);

    match create(payload) {
        Ok(response) => Ok(Json(response)),
        Err(e) => {
            tracing::error!("Error handling create: {}", e);
            Err((
                StatusCode::BAD_REQUEST,
                Json(ErrorResponse {
                    error: e.to_string(),
                }),
            ))
        }
    }
}

fn create(payload: CreatePayload) -> Result<CreateResponse> {
    let create_solana_main = payload
        .wallets
        .iter()
        .any(|w| w.network == Network::Solana && w.wallet_set == WalletSet::Main);

    let share = match payload.share {
        Some(share) => {
            if !create_solana_main {
                // If share is provided for creating non-solana-main wallet, decode it
                super::utils::decode_share(&share)?
            } else {
                return Err(eyre!("Share provided for Solana main wallet creation"));
            }
        }
        None => {
            if create_solana_main {
                // If no share is provided for creating solana main wallet, create a new one
                derive::create_shares()
            } else {
                return Err(eyre!("No share provided for creating additional wallets"));
            }
        }
    };

    let seed = super::utils::get_seed(share, &payload.user)?;

    let mut response = CreateResponse {
        user: payload.user,
        share: if create_solana_main {
            Some(bs58::encode(share.to_bytes()).into_string())
        } else {
            None
        },
        wallets: vec![],
    };
    for single_wallet_param in &payload.wallets {
        let xpriv_bytes = super::utils::get_derived_xpriv(seed, single_wallet_param.wallet_set)?;

        let address = match single_wallet_param.network {
            Network::Ethereum => {
                let (_, address) = super::utils::get_ethereum_keypair(xpriv_bytes);
                address
            }
            Network::Solana => {
                let (_, address) = super::utils::get_solana_keypair(xpriv_bytes);
                address
            }
        };

        response.wallets.push(CreateSingleWalletResponse {
            network: single_wallet_param.network,
            wallet_set: single_wallet_param.wallet_set,
            address,
        });
    }

    Ok(response)
}

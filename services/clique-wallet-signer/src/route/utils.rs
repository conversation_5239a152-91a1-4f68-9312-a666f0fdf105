use bip32::{Derivation<PERSON><PERSON>, XPrv};
use clique_wallet_signer_types::WalletSet;
use eyre::{eyre, Result};
use k256::{elliptic_curve::PrimeField, NonZeroScalar, Scalar};
use sha3::{Digest, Keccak256};

use crate::derive;

pub fn decode_share(share: &str) -> Result<Scalar> {
    let share_bytes = match bs58::decode(share).into_vec() {
        Ok(share) if share.len() == 32 => share,
        _ => return Err(eyre!("Invalid share")),
    };
    let share_fixed_bytes: [u8; 32] = share_bytes.as_slice().try_into()?;
    let share_scalar = Scalar::from_repr(share_fixed_bytes.into()).unwrap();
    Ok(share_scalar)
}

pub fn get_seed(share: Scalar, user: &str) -> Result<[u8; 32]> {
    let user_id = bs58::decode(user).into_vec()?;
    let user_master_key = derive::user_master_key(user_id);
    let user_key = derive::derive_user_key(user_master_key);

    let shares = [
        (
            Scalar::from_u128(1),
            Scalar::from_repr(user_key.into()).unwrap(),
        ),
        (
            Scalar::from_u128(2),
            Scalar::from_repr(share.to_bytes()).unwrap(),
        ),
    ];

    let sk = derive::interpolate(&shares, Scalar::ZERO);
    let sk_bytes = sk.to_bytes().into();
    Ok(sk_bytes)
}

pub fn get_derived_xpriv(seed: [u8; 32], wallet_set: WalletSet) -> Result<[u8; 32]> {
    let wallet_path = wallet_set.to_bip32_derivation_path();
    let derivation_path = wallet_path.parse::<DerivationPath>()?;
    let xpriv = XPrv::derive_from_path(seed, &derivation_path)?;
    let xpriv_bytes = xpriv.to_bytes();
    Ok(xpriv_bytes)
}

pub fn get_ethereum_keypair(xpriv_bytes: [u8; 32]) -> (k256::ecdsa::SigningKey, String) {
    let sk_scalar = Scalar::from_repr(xpriv_bytes.into()).unwrap();
    let sk = k256::ecdsa::SigningKey::from(NonZeroScalar::new(sk_scalar).unwrap());

    let pk = sk.verifying_key().to_encoded_point(false);
    let address = Keccak256::digest(&pk.as_bytes()[1..]);
    let address_str = format!("0x{}", hex::encode(&address[12..]));

    (sk, address_str)
}

pub fn get_solana_keypair(xpriv_bytes: [u8; 32]) -> (ed25519_dalek::SigningKey, String) {
    let sk = ed25519_dalek::SigningKey::from_bytes(&xpriv_bytes.into());

    let vk_bytes = sk.verifying_key().to_bytes();
    let address_str = bs58::encode(vk_bytes).into_string();

    (sk, address_str)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_get_derived_xpriv() {
        let seed = [1u8; 32];

        let wallet_set = WalletSet::Main;
        let derived_xpriv = get_derived_xpriv(seed, wallet_set).unwrap();
        assert_eq!(
            derived_xpriv,
            [
                141, 63, 198, 147, 111, 243, 1, 4, 229, 35, 184, 37, 244, 109, 173, 243, 66, 124,
                89, 66, 44, 6, 218, 51, 13, 72, 87, 91, 49, 119, 44, 237,
            ]
        );

        let wallet_set = WalletSet::Secondary(0);
        let derived_xpriv = get_derived_xpriv(seed, wallet_set).unwrap();
        assert_eq!(
            derived_xpriv,
            [
                68, 207, 56, 21, 186, 237, 200, 176, 154, 32, 52, 212, 100, 12, 121, 136, 114, 179,
                43, 134, 77, 39, 196, 66, 20, 109, 40, 230, 30, 104, 229, 157
            ]
        );

        let wallet_set = WalletSet::Secondary(1);
        let derived_xpriv = get_derived_xpriv(seed, wallet_set).unwrap();
        assert_eq!(
            derived_xpriv,
            [
                126, 132, 20, 197, 8, 104, 107, 207, 82, 239, 73, 85, 14, 236, 206, 177, 193, 99,
                68, 177, 187, 177, 158, 252, 238, 21, 131, 117, 224, 195, 32, 74
            ]
        );

        let wallet_set = WalletSet::Secondary(113112342);
        let derived_xpriv = get_derived_xpriv(seed, wallet_set).unwrap();
        assert_eq!(
            derived_xpriv,
            [
                103, 37, 9, 91, 248, 163, 157, 12, 123, 247, 45, 153, 81, 96, 191, 12, 184, 156,
                204, 153, 27, 213, 32, 47, 218, 32, 97, 229, 61, 250, 229, 188
            ]
        );

        let wallet_set = WalletSet::Forward(0);
        let derived_xpriv = get_derived_xpriv(seed, wallet_set).unwrap();
        assert_eq!(
            derived_xpriv,
            [
                211, 145, 232, 75, 130, 193, 183, 22, 173, 48, 107, 104, 52, 52, 64, 72, 90, 57, 1,
                171, 4, 89, 242, 110, 147, 235, 26, 130, 80, 122, 119, 216
            ]
        );

        let wallet_set = WalletSet::Forward(1);
        let derived_xpriv = get_derived_xpriv(seed, wallet_set).unwrap();
        assert_eq!(
            derived_xpriv,
            [
                59, 67, 122, 142, 237, 245, 14, 142, 194, 26, 58, 158, 24, 222, 99, 226, 218, 161,
                106, 143, 119, 30, 206, 202, 194, 114, 252, 230, 244, 196, 47, 66
            ]
        );
    }
}

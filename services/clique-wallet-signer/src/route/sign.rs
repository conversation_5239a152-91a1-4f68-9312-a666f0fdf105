use axum::{http::StatusC<PERSON>, J<PERSON>};
use clique_wallet_signer_types::{ErrorResponse, Network, SignPayload, SignResponse};
use eyre::Result;
use k256::ecdsa::signature::Signer;

pub async fn handle_sign(
    Json(payload): Json<SignPayload>,
) -> Result<Json<SignResponse>, (StatusCode, Json<ErrorResponse>)> {
    tracing::info!(
        msg = %payload.msg,
        user = %payload.user,
        network = %payload.network,
        "Sign request received"
    );

    match sign(payload) {
        Ok(signature) => Ok(Json(SignResponse { signature })),
        Err(e) => Err((
            StatusCode::BAD_REQUEST,
            Json(ErrorResponse {
                error: e.to_string(),
            }),
        )),
    }
}

fn sign(payload: SignPayload) -> Result<String> {
    let share = super::utils::decode_share(&payload.share)?;
    let seed = super::utils::get_seed(share, &payload.user)?;
    let xpriv_bytes = super::utils::get_derived_xpriv(seed, payload.wallet_set)?;

    match payload.network {
        Network::Ethereum => {
            let (sk, address) = super::utils::get_ethereum_keypair(xpriv_bytes);
            if address != payload.address {
                return Err(eyre::eyre!("address mismatch"));
            }

            let msg_bytes = bs58::decode(payload.msg.as_str()).into_vec()?;

            // Sign the message with a recoverable signature
            let (signature_64, rec_id) = sk.sign_prehash_recoverable(&msg_bytes).unwrap();

            // Convert to 65-byte Ethereum format (r, s, v)
            let mut eth_sig = [0u8; 65];
            eth_sig[..64].copy_from_slice(&signature_64.to_bytes());
            eth_sig[64] = rec_id.to_byte();

            Ok(bs58::encode(eth_sig).into_string())
        }
        Network::Solana => {
            let (sk, address) = super::utils::get_solana_keypair(xpriv_bytes);
            if address != payload.address {
                return Err(eyre::eyre!("address mismatch"));
            }

            let msg_bytes = bs58::decode(payload.msg.as_str()).into_vec()?;
            let signature = sk.sign(msg_bytes.as_ref());
            Ok(bs58::encode(signature.to_vec()).into_string())
        }
    }
}

use std::{ops::Deref, sync::OnceLock};

use clique_sibyl_commonlib::{
    attestation::generate_attestation, tls::config::create_tls_client_config_with_client_auth,
};
use k256::ecdsa::Signing<PERSON>ey;
use serde_json::{json, Value};

#[derive(Clone)]
pub struct MasterKey {
    inner: SigningKey,
}

impl MasterKey {
    const APP_SALT: &[u8] = b"clique-wallet-signer/v0.1.0";

    #[cfg(not(feature = "mock"))]
    pub fn open() -> &'static Self {
        static MASTER_KEY: OnceLock<MasterKey> = OnceLock::new();
        let kms_url = std::env::var("KMS_URL").expect("KMS_URL env var not set");
        let mr_signer = std::env::var("MR_SIGNER").expect("MR_SIGNER env var not set");
        MASTER_KEY.get_or_init(|| {
            let master_key = match get_app_key_from_kms(&kms_url, Self::APP_SALT, &mr_signer) {
                Ok(key) => key,
                Err(e) => {
                    tracing::error!("Failed to get app key from KMS: {:?}", e);
                    panic!("Failed to get app key from KMS");
                }
            };

            let public_key = master_key.verifying_key();
            let public_key_bytes = public_key.to_sec1_bytes();
            tracing::info!("Init: {}", hex::encode(public_key_bytes));

            MasterKey { inner: master_key }
        })
    }

    #[cfg(feature = "mock")]
    pub fn open() -> &'static Self {
        static MASTER_KEY: OnceLock<MasterKey> = OnceLock::new();
        MASTER_KEY.get_or_init(|| {
            let master_key = SigningKey::from_slice(&[1u8; 32]).unwrap();
            MasterKey { inner: master_key }
        })
    }
}

impl Deref for MasterKey {
    type Target = SigningKey;

    fn deref(&self) -> &Self::Target {
        &self.inner
    }
}

fn get_app_key_from_kms(kms_url: &str, salt: &[u8], mr_signer: &str) -> eyre::Result<SigningKey> {
    let kms_mr_signer = match std::env::var("KMS_MR_SIGNER") {
        Ok(s) => {
            if s.is_empty() {
                tracing::info!("KMS_MR_SIGNER is empty, ignore");
                None
            } else {
                tracing::info!("KMS_MR_SIGNER: {}", s);
                Some(vec![s])
            }
        }
        Err(_) => {
            tracing::info!("KMS_MR_SIGNER is not set, ignore");
            None
        }
    };
    let kms_mr_enclave = match std::env::var("KMS_MR_ENCLAVE") {
        Ok(s) => {
            if s.is_empty() {
                tracing::info!("KMS_MR_ENCLAVE is empty, ignore");
                None
            } else {
                tracing::info!("KMS_MR_ENCLAVE: {}", s);
                Some(vec![s])
            }
        }
        Err(_) => {
            tracing::info!("KMS_MR_ENCLAVE is not set, ignore");
            None
        }
    };

    let tls = create_tls_client_config_with_client_auth(kms_mr_enclave, kms_mr_signer)?;
    let client = reqwest::blocking::ClientBuilder::new()
        .use_preconfigured_tls(tls)
        .build()?;
    let attestation = generate_attestation(salt)?;

    let req = json!(
        {
            "attestation": attestation,
            "key_mode":  {
                "mode": "relaxed",
                "mr_signer": mr_signer
            }
        }
    );

    let url = format!("{kms_url}/get_key");
    tracing::info!("KMS url: {}", url);
    tracing::info!("MR_SIGNER: {}", mr_signer);

    let resp = client.post(&url).json(&req).send()?;

    if !resp.status().is_success() {
        tracing::info!("KMS response: {:?}", resp);
        let body = resp.text()?;
        return Err(eyre::eyre!("Failed to get app key from KMS: {}", body));
    }

    let resp = resp.json::<Value>()?;

    let app_key = resp["app_key"]
        .as_str()
        .ok_or(eyre::eyre!("app_key not found"))?;
    let app_key = hex::decode(app_key)?;

    tracing::info!("Get app key from KMS success");

    Ok(SigningKey::from_slice(&app_key)?)
}

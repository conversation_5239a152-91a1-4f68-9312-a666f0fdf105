mod derive;
mod master_key;
mod route;

use std::{net::SocketAddr, sync::Arc};

use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    routing::{get, post},
    Router,
};
use axum_server::tls_rustls::{RustlsAcceptor, RustlsConfig};
use clique_sibyl_commonlib::tls::config::{
    create_tls_server_config_with_client_auth, create_tls_server_config_without_client_auth,
};

use self::route::{create::handle_create, sign::handle_sign};

async fn healthz() -> Response {
    tracing::info!("Health check request received");
    StatusCode::OK.into_response()
}

#[tokio::main]
async fn main() {
    // Initialize tracing
    tracing_subscriber::fmt()
        .with_target(false)
        .with_level(true)
        .with_file(true)
        .with_line_number(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_env_filter(tracing_subscriber::EnvFilter::new(
            std::env::var("RUST_LOG").unwrap_or_else(|_| "info".into()),
        ))
        .init();

    let sensitive_service_port = std::env::var("SENSITIVE_SERVICE_PORT")
        .unwrap_or_else(|_| "3000".to_string())
        .parse::<u16>()
        .expect("Invalid SENSITIVE_SERVICE_PORT");
    let non_sensitive_service_port = std::env::var("NON_SENSITIVE_SERVICE_PORT")
        .unwrap_or_else(|_| "3001".to_string())
        .parse::<u16>()
        .expect("Invalid NON_SENSITIVE_SERVICE_PORT");

    // init master key
    let _ = master_key::MasterKey::open();
    tracing::info!("Master key initialized");

    let sensitive_app = Router::new()
        .route("/create", post(handle_create))
        .route("/sign", post(handle_sign));
    let non_sensitive_app = Router::new().route("/healthz", get(healthz));

    let sensitive_addr = SocketAddr::from(([0, 0, 0, 0], sensitive_service_port));
    let non_sensitive_addr = SocketAddr::from(([0, 0, 0, 0], non_sensitive_service_port));
    tracing::info!("Sensitive server starting on https://{}", sensitive_addr);
    tracing::info!(
        "Non-sensitive server starting on https://{}",
        non_sensitive_addr
    );

    let trusted_enclaves = clique_sibyl_commonlib::utils::get_trusted_enclaves_from_env()
        .expect("Failed to get trusted enclaves")
        .cloned();
    let trusted_signers = clique_sibyl_commonlib::utils::get_trusted_signers_from_env()
        .expect("Failed to get trusted signers")
        .cloned();

    let require_client_cert = true;
    let sensitive_server_config = create_tls_server_config_with_client_auth(
        trusted_enclaves,
        trusted_signers,
        require_client_cert,
    )
    .expect("Failed to create TLS server config");
    let sensitive_rustls_config = RustlsConfig::from_config(Arc::new(sensitive_server_config));

    let non_sensitive_server_config =
        create_tls_server_config_without_client_auth().expect("Failed to create TLS server config");
    let non_sensitive_rustls_config =
        RustlsConfig::from_config(Arc::new(non_sensitive_server_config));

    // Spawn servers concurrently
    let sensitive_server = tokio::spawn(
        axum_server::bind(sensitive_addr)
            .acceptor(RustlsAcceptor::new(sensitive_rustls_config))
            .serve(sensitive_app.into_make_service()),
    );

    let non_sensitive_handle = axum_server::Handle::new();
    let non_sensitive_server = tokio::spawn(
        axum_server::bind_rustls(non_sensitive_addr, non_sensitive_rustls_config)
            .handle(non_sensitive_handle.clone())
            .serve(non_sensitive_app.into_make_service()),
    );

    // Wait for sensitive server to complete then trigger shutdown
    sensitive_server.await.unwrap().unwrap();
    non_sensitive_handle.graceful_shutdown(None);

    // Wait for non-sensitive server to finish
    non_sensitive_server.await.unwrap().unwrap();
}

use k256::{
    elliptic_curve::{rand_core::OsRng, Field},
    sha2::<PERSON>ha256,
    <PERSON><PERSON><PERSON>,
};

use crate::master_key::<PERSON><PERSON><PERSON>;

pub fn user_master_key(user_id: impl AsRef<[u8]>) -> [u8; 65] {
    let signing_key = MasterKey::open();

    // Sign the user_id
    let (sig, recid) = signing_key
        .sign_recoverable(user_id.as_ref())
        .expect("signing operation succeeded; qed");
    let mut sig_bytes = [0u8; 65];
    sig_bytes[..64].copy_from_slice(&sig.to_bytes());
    sig_bytes[64] = recid.to_byte();
    sig_bytes
}

pub fn derive_user_key(passwd: impl AsRef<[u8]>) -> [u8; 32] {
    let mut key = [0u8; 32];
    pbkdf2::pbkdf2_hmac::<Sha256>(passwd.as_ref(), &[], 10000, &mut key);
    key
}

pub fn create_shares() -> Scalar {
    let mut rng = OsRng;
    Scalar::random(&mut rng)
}

pub fn interpolate(shares: &[(<PERSON><PERSON><PERSON>, <PERSON>alar)], x: Scalar) -> Scalar {
    // Initialize result to zero
    let mut result = Scalar::ZERO;

    // For each share, calculate its contribution using Lagrange basis polynomial
    for (i, (x_i, y_i)) in shares.iter().enumerate() {
        let mut basis = Scalar::ONE;

        // Calculate the Lagrange basis polynomial
        for (j, (x_j, _)) in shares.iter().enumerate() {
            if i != j {
                // basis *= (x - x_j) / (x_i - x_j)
                let numerator = x - x_j;
                let denominator = x_i - x_j;
                basis *= numerator * denominator.invert().unwrap();
            }
        }

        // Multiply by y_i and add to result
        result += basis * y_i;
    }

    result
}

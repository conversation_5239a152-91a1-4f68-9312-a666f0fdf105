use std::fmt;
use std::str::FromStr;

use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, <PERSON>h, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum Network {
    Ethereum,
    Solana,
}

impl fmt::Display for Network {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Network::Ethereum => write!(f, "ethereum"),
            Network::Solana => write!(f, "solana"),
        }
    }
}

impl FromStr for Network {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s {
            "ethereum" => Ok(Network::Ethereum),
            "solana" => Ok(Network::Solana),
            _ => Err("Invalid network".to_string()),
        }
    }
}

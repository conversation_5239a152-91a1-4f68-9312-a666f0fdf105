use std::fmt;
use std::str::FromStr;

use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, Hash, <PERSON><PERSON>, Copy)]
pub enum WalletSet {
    Main,
    Secondary(u32),
    Forward(u32),
}

impl fmt::Display for WalletSet {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            WalletSet::Main => write!(f, "Main"),
            WalletSet::Secondary(id) => write!(f, "Secondary-{}", id),
            WalletSet::Forward(id) => write!(f, "Forward-{}", id),
        }
    }
}

impl FromStr for WalletSet {
    type Err = String;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        if s == "Main" {
            Ok(WalletSet::Main)
        } else if let Some(id_str) = s.strip_prefix("Secondary-") {
            let id = id_str
                .parse::<u32>()
                .map_err(|e| format!("Invalid secondary id '{}': {}", id_str, e))?;
            Ok(WalletSet::Secondary(id))
        } else if let Some(id_str) = s.strip_prefix("Forward-") {
            let id = id_str
                .parse::<u32>()
                .map_err(|e| format!("Invalid forward id '{}': {}", id_str, e))?;
            Ok(WalletSet::Forward(id))
        } else {
            Err(format!("Invalid wallet set: {}", s))
        }
    }
}

impl WalletSet {
    pub fn to_bip32_derivation_path(&self) -> String {
        match self {
            WalletSet::Main => "m/0'/0'".to_string(),
            WalletSet::Secondary(id) => {
                format!("m/0'/1'/{id}")
            }
            WalletSet::Forward(id) => {
                format!("m/0'/2'/{id}")
            }
        }
    }
}

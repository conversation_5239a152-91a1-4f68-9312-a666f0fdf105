use serde::{Deserialize, Serialize};

use crate::network::Network;
use crate::wallet_set::WalletSet;

#[derive(Debug, Deserialize, Serialize, Eq, PartialEq, Clone)]
pub struct CreateSingleWalletParams {
    pub network: Network,
    pub wallet_set: WalletSet,
}

#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct CreatePayload {
    pub user: String,
    pub share: Option<String>,
    pub wallets: Vec<CreateSingleWalletParams>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct CreateSingleWalletResponse {
    pub network: Network,
    pub wallet_set: WalletSet,
    pub address: String,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct CreateResponse {
    pub user: String,
    pub share: Option<String>,
    pub wallets: Vec<CreateSingleWalletResponse>,
}

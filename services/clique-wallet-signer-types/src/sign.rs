use serde::{Deserialize, Serialize};

use crate::network::Network;
use crate::wallet_set::WalletSet;

#[derive(Debug, Deserialize, Serialize)]
pub struct SignPayload {
    // message to sign
    pub msg: String,
    pub user: String,
    pub network: Network,
    pub address: String,
    pub wallet_set: WalletSet,
    pub share: String,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct SignResponse {
    pub signature: String,
}
